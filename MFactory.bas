Attribute VB_Name = "MFactory"
Option Explicit

' 工厂函数模块 - 用于创建各种类的实例

Public Function NewCBytes() As CBytes
    Set NewCBytes = New CBytes
End Function

Public Function NewCFile() As CFile
    Set NewCFile = New CFile
End Function

Public Function NewCCompoundFile() As CCompoundFile
    Set NewCCompoundFile = New CCompoundFile
End Function

Public Function NewCHash(MaxCount As Long) As CHash
    Set NewCHash = New CHash
    NewCHash.MaxCount = MaxCount
End Function
