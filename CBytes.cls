VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
END
Attribute VB_Name = "CBytes"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
' VBA-Parser-Project: CBytes.cls
' =================================================================
' Attribute VB_Name = "CBytes"
' Attribute VB_Base = "0{FCFB3D2A-A0FA-1068-A738-08002B3371B5}"
' Attribute VB_GlobalNameSpace = False
' Attribute VB_Creatable = False
' Attribute VB_PredeclaredId = False
' Attribute VB_Exposed = False
' Attribute VB_TemplateDerived = False
' Attribute VB_Customizable = False
'
' 模块属性设置 (Module Property Settings):
' Instancing: 2 - PublicNotCreatable
' =================================================================
Option Explicit

Implements IReadWrite

' --- 私有变量 ---
Private bSrc() As Byte    ' 存储二进制数据的字节数组
Private pos As Long       ' 模拟文件指针的当前位置
Private lLen As Long      ' 数组的总长度

' --- 公共属性 ---

' 设置数据源
Public Property Let SetData(v() As Byte)
    On Error Resume Next
    bSrc = v
    If Err.Number = 0 Then
        ' 确保使用LBound来正确处理非0基数组
        lLen = UBound(v) - LBound(v) + 1
    Else
        lLen = 0
        Erase bSrc
    End If
    On Error GoTo 0
    pos = 0
End Property

' 获取数据源
Public Property Get GetData() As Byte()
    GetData = bSrc
End Property

' (其他高级搜索函数，如Index, KMP等，与您提供的版本相同，此处省略)
' ...

' #############################################################################
' ##  IReadWrite 接口实现
' #############################################################################

Private Function IReadWrite_Read(b() As Byte) As Long
    Dim ilen As Long
    On Error GoTo BoundsError
    ilen = UBound(b) - LBound(b) + 1
    GoTo ContinueRead
BoundsError:
    ilen = 0 ' 如果传入的b()未初始化，则ilen为0
ContinueRead:
    On Error GoTo 0

    Dim bytesToRead As Long
    bytesToRead = ilen
    If pos + bytesToRead > lLen Then
        bytesToRead = lLen - pos
    End If

    If bytesToRead <= 0 Then
        IReadWrite_Read = 0
        Exit Function
    End If

    If bytesToRead <> ilen Then ReDim b(0 To bytesToRead - 1)

    ' 使用CopyMemory高效复制
    CopyMemory b(0), bSrc(pos), bytesToRead
    pos = pos + bytesToRead

    IReadWrite_Read = bytesToRead
End Function

Private Function IReadWrite_ReadAt(b() As Byte, ByVal offset As Long) As Long
    pos = offset
    IReadWrite_ReadAt = IReadWrite_Read(b)
End Function

Private Function IReadWrite_ReadByte() As Byte
    If pos >= lLen Then Exit Function

    IReadWrite_ReadByte = bSrc(pos)
    pos = pos + 1
End Function

' #############################################################################
' ##  ↓↓↓ 已添加调试代码的、修正后的函数 ↓↓↓
' #############################################################################

Private Function IReadWrite_ReadInteger() As Integer
    If pos + 1 >= lLen Then Exit Function

    Dim result As Integer
    ' 使用CopyMemory来正确地将2个字节解释为有符号Integer
    ' 确保CopyMemory在标准模块中被声明为Public
    CopyMemory result, bSrc(pos), 2

    ' --- 新增的调试代码 ---
    Debug.Print "正在读取Integer，原始字节: " & Hex(bSrc(pos)) & " " & Hex(bSrc(pos + 1)) & " -> 结果: " & result
    ' --- 调试代码结束 ---

    IReadWrite_ReadInteger = result
    pos = pos + 2
End Function

Private Function IReadWrite_ReadLong() As Long
    If pos + 3 >= lLen Then Exit Function

    Dim result As Long
    ' 使用CopyMemory来正确地将4个字节解释为有符号Long
    CopyMemory result, bSrc(pos), 4

    ' --- 新增的调试代码 ---
    Debug.Print "正在读取Long，原始字节: " & Hex(bSrc(pos)) & " " & Hex(bSrc(pos + 1)) & " " & Hex(bSrc(pos + 2)) & " " & Hex(bSrc(pos + 3)) & " -> 结果: " & result
    ' --- 调试代码结束 ---

    IReadWrite_ReadLong = result
    pos = pos + 4
End Function

' #############################################################################

Private Function IReadWrite_ReadDate() As Date
    ' 占位符
    pos = pos + 8
    IReadWrite_ReadDate = 0
End Function

Private Function IReadWrite_SeekFile(ByVal offsetZeroBase As Long, ByVal whence As SeekPos) As Long
    Select Case whence
        Case SeekPos.OriginF
            pos = offsetZeroBase
        Case SeekPos.CurrentF
            pos = pos + offsetZeroBase
        Case SeekPos.EndF
            pos = lLen - offsetZeroBase
    End Select
    
    If pos < 0 Then pos = 0
    If pos > lLen Then pos = lLen
    
    IReadWrite_SeekFile = pos
End Function

' --- 写操作 (占位符) ---
Private Function IReadWrite_WriteFile(b() As Byte) As Long
    ' TODO
End Function

Private Function IReadWrite_WriteLong(ByVal l As Long) As Long
    ' TODO
End Function
