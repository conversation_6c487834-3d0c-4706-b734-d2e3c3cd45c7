' Attribute VB_Name = "CCompoundFile"
Option Explicit

Private Const CFHEADER_SIZE As Long = 2 ^ 9
Private Const DIR_SIZE As Long = 128
Private Const Free_SID As Long = -1
Private Const End_Of_Chain_SID As Long = -2
Private Const FAT_SID As Long = -3
Private Const DIFAT_SID As Long = -4

Private Type CFHeader
    Signature(7) As Byte
    CLSID(15) As Byte
    MinorVersion As Integer
    MajorVersion As Integer
    ByteOrder As Integer
    SectorShift As Integer
    MiniSectorShift As Integer
    Reserved(5) As Byte
    DirSectorsCount As Long
    FATSectorsCount As Long
    FirstDirSID As Long
    TransactionSignatureNumber As Long
    MiniStreamSize As Long
    FirstMiniFATSID As Long
    MiniFATSectorsCount As Long
    FirstDIFATSID As Long
    DIFATSectorsCount As Long
    DIFATS(108) As Long
End Type

Private Type CFDir
    EntryName(63) As Byte
    EntryNameLen As Integer
    ObjectType As Byte
    ColorFlag As Byte
    leftSiblingID As Long
    rightSiblingID As Long
    childID As Long
    CLSID(15) As Byte
    StateBits As Long
    CreationTime As Date
    ModifiedTime As Date
    StartingSectorID As Long
    StreamSize As LongLong
    not_used As Long
    StrDirName As String
    lOffset As LongLong
End Type

Private Type cf
    r As IReadWrite
    lSectorSize As Long
    lShortSectorSize As Long
    longNumPerSector As Long
    ssNumPerSector As Long
    Header As CFHeader
    FAT() As Long
    DIFAT() As Long
    MiniFAT() As Long
    ArrDir() As CFDir
    h As CHash
End Type

Private ArrDirsName() As String
Private cf As cf

Property Get DirsName() As String()
    DirsName = ArrDirsName
End Property

Private Function GetDirsName() As String
    Dim i As Long
    ReDim ArrDirsName(UBound(cf.ArrDir)) As String
    For i = 0 To UBound(cf.ArrDir)
        ArrDirsName(i) = cf.ArrDir(i).StrDirName
    Next
End Function

Function Parse(ir As IReadWrite) As String
    Set cf.r = ir
    Dim ret As String
    ret = parseCfHeader()
    If VBA.Len(ret) Then
        Parse = ret
        Exit Function
    End If
    ret = parseDIFAT()
    If VBA.Len(ret) Then
        Parse = ret
        Exit Function
    End If
    ret = parseFAT()
    If VBA.Len(ret) Then
        Parse = ret
        Exit Function
    End If
    ret = parseDir()
    If VBA.Len(ret) Then
        Parse = ret
        Exit Function
    End If
    ret = parseMiniFAT()
    If VBA.Len(ret) Then
        Parse = ret
        Exit Function
    End If
End Function

Private Function parseCfHeader() As String
    Dim iret As Long
    iret = cf.r.Read(cf.Header.Signature)
    If iret <> 8 Then
        parseCfHeader = "复合文档：文件头id读取出错"
        Exit Function
    End If
    Dim arr
    arr = Array(208, 207, 17, 224, 161, 177, 26, 225)
    Dim i As Long
    For i = 0 To 7
        If cf.Header.Signature(i) <> arr(i) Then
            parseCfHeader = "复合文档：文件头id出错"
            Exit Function
        End If
    Next
    cf.r.Read cf.Header.CLSID
    cf.Header.MinorVersion = cf.r.ReadInteger
    cf.Header.MajorVersion = cf.r.ReadInteger
    cf.Header.ByteOrder = cf.r.ReadInteger
    cf.Header.SectorShift = cf.r.ReadInteger
    cf.Header.MiniSectorShift = cf.r.ReadInteger
    cf.r.Read cf.Header.Reserved
    cf.Header.DirSectorsCount = cf.r.ReadLong()
    cf.Header.FATSectorsCount = cf.r.ReadLong
    cf.Header.FirstDirSID = cf.r.ReadLong
    cf.Header.TransactionSignatureNumber = cf.r.ReadLong
    cf.Header.MiniStreamSize = cf.r.ReadLong
    cf.Header.FirstMiniFATSID = cf.r.ReadLong
    cf.Header.MiniFATSectorsCount = cf.r.ReadLong
    cf.Header.FirstDIFATSID = cf.r.ReadLong
    cf.Header.DIFATSectorsCount = cf.r.ReadLong
    For i = 0 To 108
        cf.Header.DIFATS(i) = cf.r.ReadLong
    Next
    If cf.Header.ByteOrder <> -2 Then
        parseCfHeader = "复合文档：memory endian 不是小端"
        Exit Function
    End If
    If cf.Header.MiniSectorShift <> 6 Then
        parseCfHeader = "复合文档：The sector size of the Mini Stream MUST be 64 bytes"
        Exit Function
    End If
    If cf.Header.MajorVersion = 3 Then
        If cf.Header.SectorShift <> &H9 Then
            parseCfHeader = "复合文档：If Major Version is 3, the Sector Shift MUST be 0x0009, specifying a sector size of 512 bytes."
            Exit Function
        End If
    ElseIf cf.Header.MajorVersion = 4 Then
        If cf.Header.SectorShift <> &HC Then
            parseCfHeader = "复合文档：If Major Version is 4, the Sector Shift MUST be 0x000C, specifying a sector size of 4,096 bytes."
            Exit Function
        End If
    Else
        parseCfHeader = "复合文档：Major Version must by 3 or 4."
        Exit Function
    End If
    cf.lSectorSize = 2 ^ cf.Header.SectorShift
    cf.longNumPerSector = cf.lSectorSize \ 4
    cf.lShortSectorSize = 2 ^ cf.Header.MiniSectorShift
    cf.ssNumPerSector = cf.lSectorSize / cf.lShortSectorSize
End Function

Private Function parseDIFAT() As String
    Dim i As Long
    Dim next_SID As Long
    Dim flag As Boolean
    Dim Count As Long
    ReDim cf.DIFAT(cf.Header.FATSectorsCount - 1) As Long
    For i = 0 To 108
        If cf.Header.DIFATS(i) = -1 Then
            Exit Function
        End If
        cf.DIFAT(i) = cf.Header.DIFATS(i)
    Next
    Count = 109
    next_SID = cf.Header.FirstDIFATSID
    flag = True
    Dim tmp As Long
    Do
        Dim offset As LongLong
        offset = getOffsetBySID(next_SID)
        If offset > &H7FFFFFFF Then
            parseDIFAT = "复合文档：偏移量超过 32 位限制"
            Exit Function
        End If
        cf.r.SeekFile CLng(offset), OriginF
        For i = 0 To cf.longNumPerSector - 2
            tmp = cf.r.ReadLong()
            If tmp = Free_SID Then
                flag = False
                Exit For
            End If
            If Count > UBound(cf.DIFAT) Then
                ReDim Preserve cf.DIFAT(Count + cf.longNumPerSector - 1)
            End If
            cf.DIFAT(Count) = tmp
            Count = Count + 1
        Next
        next_SID = cf.r.ReadLong()
    Loop While flag And next_SID <> End_Of_Chain_SID
End Function

Private Function parseFAT() As String
    Dim i As Long, j As Long
    Dim Count As Long
    ReDim cf.FAT(cf.Header.FATSectorsCount * cf.longNumPerSector - 1) As Long
    Count = 0
    For i = 0 To cf.Header.FATSectorsCount - 1
        Dim offset As LongLong
        offset = getOffsetBySID(cf.DIFAT(i))
        If offset > &H7FFFFFFF Then
            parseFAT = "复合文档：偏移量超过 32 位限制"
            Exit Function
        End If
        cf.r.SeekFile CLng(offset), OriginF
        For j = 0 To cf.longNumPerSector - 1
            cf.FAT(Count) = cf.r.ReadLong()
            Count = Count + 1
        Next
    Next
End Function

Private Function parseDir() As String
    Dim l_SID As Long
    Dim k As Long
    Dim i As Long
    Dim lOffset As LongLong
    l_SID = cf.Header.FirstDirSID
    k = 0
    Do
        lOffset = getOffsetBySID(l_SID)
        If lOffset > &H7FFFFFFF Then
            parseDir = "复合文档：偏移量超过 32 位限制"
            Exit Function
        End If
        cf.r.SeekFile CLng(lOffset), OriginF
        ReDim Preserve cf.ArrDir(k + 3) As CFDir
        For i = 0 To 3
            cf.r.Read cf.ArrDir(k + i).EntryName
            cf.ArrDir(k + i).EntryNameLen = cf.r.ReadInteger()
            If cf.ArrDir(k + i).EntryNameLen = 0 Then Exit Do
            cf.ArrDir(k + i).ObjectType = cf.r.ReadByte()
            cf.ArrDir(k + i).ColorFlag = cf.r.ReadByte()
            cf.ArrDir(k + i).leftSiblingID = cf.r.ReadLong()
            cf.ArrDir(k + i).rightSiblingID = cf.r.ReadLong()
            cf.ArrDir(k + i).childID = cf.r.ReadLong()
            cf.r.Read cf.ArrDir(k + i).CLSID
            cf.ArrDir(k + i).StateBits = cf.r.ReadLong()
            cf.ArrDir(k + i).CreationTime = cf.r.ReadDate()
            cf.ArrDir(k + i).ModifiedTime = cf.r.ReadDate()
            cf.ArrDir(k + i).StartingSectorID = cf.r.ReadLong()
            cf.ArrDir(k + i).StreamSize = cf.r.ReadLong()
            cf.ArrDir(k + i).not_used = cf.r.ReadLong()
            Dim nameBytes() As Byte
            ReDim nameBytes(cf.ArrDir(k + i).EntryNameLen - 1)
            Dim j As Long
            For j = 0 To cf.ArrDir(k + i).EntryNameLen - 1
                nameBytes(j) = cf.ArrDir(k + i).EntryName(j)
            Next
            cf.ArrDir(k + i).StrDirName = StrConv(nameBytes, vbFromUnicode)
            If cf.ArrDir(k + i).EntryName(0) <= 5 Then
                cf.ArrDir(k + i).StrDirName = "[" & cf.ArrDir(k + i).StrDirName & "]"
            End If
            cf.ArrDir(k + i).lOffset = lOffset
            lOffset = lOffset + DIR_SIZE
        Next
        k = k + 4
        l_SID = cf.FAT(l_SID)
    Loop Until l_SID = End_Of_Chain_SID
    ReDim Preserve cf.ArrDir(k + i - 1) As CFDir
    recordDir
    GetDirsName
End Function

Private Function recordDir() As String
    Set cf.h = NewCHash(UBound(cf.ArrDir) + 1)
    RrecordDir cf.ArrDir(0), "", 0
End Function

Private Sub RrecordDir(ByVal dirIndex As Long, ByVal preDir As String)
    Dim maxIndex As Long
    On Error Resume Next
    maxIndex = UBound(cf.ArrDir)
    If Err.Number <> 0 Then Exit Sub
    On Error GoTo 0
    
    If dirIndex < 0 Or dirIndex > maxIndex Then Exit Sub
    If cf.visitedDirs(dirIndex) Then Exit Sub
    cf.visitedDirs(dirIndex) = True
    If cf.ArrDir(dirIndex).ObjectType = 0 Then Exit Sub

    ' --- 最终的、最健壮的字符串转换逻辑：手动解码 ---
    Dim namePart As String
    If cf.ArrDir(dirIndex).EntryNameLen > 1 Then
        Dim nameByteLen As Long
        nameByteLen = cf.ArrDir(dirIndex).EntryNameLen
        
        ' 确保长度有效且不超过缓冲区大小
        If nameByteLen > 2 And nameByteLen <= 64 Then
            Dim i As Long
            Dim charCode As Integer
            ' 手动将UTF-16 LE字节对组合成字符，直到遇到NULL终止符
            For i = 0 To nameByteLen - 2 Step 2
                ' 小端序: 低位字节在前，高位字节在后
                charCode = cf.ArrDir(dirIndex).EntryName(i + 1) * 256 + cf.ArrDir(dirIndex).EntryName(i)
                If charCode = 0 Then Exit For ' 遇到NULL终止符，提前结束
                namePart = namePart & ChrW$(charCode)
            Next i
        End If
    End If
    
    Dim currentPath As String
    currentPath = preDir & namePart
    cf.ArrDir(dirIndex).StrDirName = currentPath
    
    If Not cf.h.Exists(currentPath) Then
        cf.h.Add currentPath, dirIndex
    End If

    Dim childID As Long, leftSiblingID As Long, rightSiblingID As Long
    childID = cf.ArrDir(dirIndex).childID
    leftSiblingID = cf.ArrDir(dirIndex).leftSiblingID
    rightSiblingID = cf.ArrDir(dirIndex).rightSiblingID

    If childID <> -1 And childID <= maxIndex Then RrecordDir childID, currentPath & "\"
    If leftSiblingID <> -1 And leftSiblingID <= maxIndex Then RrecordDir leftSiblingID, preDir
    If rightSiblingID <> -1 And rightSiblingID <= maxIndex Then RrecordDir rightSiblingID, preDir
End Sub


Private Function parseMiniFAT() As String
    Dim l_SID As Long
    Dim i As Long, j As Long
    If cf.Header.MiniFATSectorsCount = 0 Then Exit Function
    ReDim cf.MiniFAT(cf.ArrDir(0).StreamSize / cf.lShortSectorSize - 1) As Long
    l_SID = cf.Header.FirstMiniFATSID
    For i = 0 To UBound(cf.MiniFAT) Step cf.longNumPerSector
        Dim offset As LongLong
        offset = getOffsetBySID(l_SID)
        If offset > &H7FFFFFFF Then
            parseMiniFAT = "复合文档：偏移量超过 32 位限制"
            Exit Function
        End If
        cf.r.SeekFile CLng(offset), OriginF
        For j = 0 To cf.longNumPerSector - 1
            If i + j > UBound(cf.MiniFAT) Then Exit For
            cf.MiniFAT(i + j) = cf.r.ReadLong()
        Next
        l_SID = cf.FAT(l_SID)
    Next
End Function

Function ReWriteStream(dir_name As String, WriteBytes() As Byte) As String
    If cf.h.Exists(dir_name) Then
        ReWriteStream = ReWriteStreamByDirIndex(VBA.CLng(cf.h.GetItem(dir_name)), WriteBytes)
    Else
        ReWriteStream = "复合文档：不存在的目录"
        Exit Function
    End If
End Function

Private Function ReWriteStreamByDirIndex(dirIndex As Long, WriteBytes() As Byte) As String
    If cf.ArrDir(dirIndex).ObjectType <> 2 Then
        ReWriteStreamByDirIndex = "复合文档：不是数据流"
        Exit Function
    End If
    If cf.ArrDir(dirIndex).StartingSectorID = Free_SID Then
        ReWriteStreamByDirIndex = "复合文档：流的大小为0"
        Exit Function
    End If
    Dim ilen As LongLong
    ilen = UBound(WriteBytes) + 1
    If cf.ArrDir(dirIndex).StreamSize < cf.Header.MiniStreamSize Then
        ReWriteStreamByDirIndex = ReWriteStreamMiniFAT(dirIndex, WriteBytes)
    Else
        ReWriteStreamByDirIndex = ReWriteStreamFAT(dirIndex, WriteBytes)
    End If
    cf.ArrDir(dirIndex).StreamSize = ilen
    Dim offset As LongLong
    offset = cf.ArrDir(dirIndex).lOffset + DIR_SIZE - 16
    If offset > &H7FFFFFFF Then
        ReWriteStreamByDirIndex = "复合文档：偏移量超过 32 位限制"
        Exit Function
    End If
    cf.r.SeekFile CLng(offset), OriginF
    If ilen > &HFFFFFFFF Then
        cf.r.WriteLong CLng(ilen And &HFFFFFFFF)
        cf.r.WriteLong CLng(ilen \ &H100000000)
    Else
        cf.r.WriteLong CLng(ilen)
    End If
    Debug.Print "ilen: " & ilen & ", Low 32: " & (ilen And &HFFFFFFFF) & ", High 32: " & (ilen \ &H100000000)
End Function

Private Function ReWriteStreamMiniFAT(dirIndex As Long, WriteBytes() As Byte) As String
    Dim ilen As LongLong
    ilen = UBound(WriteBytes) + 1
    If ilen > cf.ArrDir(dirIndex).StreamSize Then
        If ilen \ cf.lShortSectorSize > cf.ArrDir(dirIndex).StreamSize \ cf.lShortSectorSize Then
            ReWriteStreamMiniFAT = "复合文档：改写的数据超过了原来的范围，并且超越了扇区"
            Exit Function
        End If
    End If
    Dim b() As Byte
    ReDim b(cf.lShortSectorSize - 1) As Byte
    Dim miniSID As Long
    miniSID = cf.ArrDir(dirIndex).StartingSectorID
    Dim i As LongLong
    Dim p As LongLong
    Do Until miniSID = End_Of_Chain_SID
        Dim offset As LongLong
        offset = getOffsetByMiniFATSID(miniSID)
        If offset > &H7FFFFFFF Then
            ReWriteStreamMiniFAT = "复合文档：偏移量超过 32 位限制"
            Exit Function
        End If
        cf.r.SeekFile CLng(offset), OriginF
        For i = 0 To cf.lShortSectorSize - 1
            If p >= ilen Then Exit For
            b(i) = WriteBytes(p)
            p = p + 1
        Next
        If p < ilen Then
            ReDim Preserve b(i - 1)
        End If
        cf.r.WriteFile b
        If p >= ilen Then Exit Function
        miniSID = cf.MiniFAT(miniSID)
    Loop
End Function

Private Function ReWriteStreamFAT(dirIndex As Long, WriteBytes() As Byte) As String
    Dim ilen As LongLong
    ilen = UBound(WriteBytes) + 1
    If ilen > cf.ArrDir(dirIndex).StreamSize Then
        If ilen \ cf.lSectorSize > cf.ArrDir(dirIndex).StreamSize \ cf.lSectorSize Then
            ReWriteStreamFAT = "复合文档：改写的数据超过了原来的范围，并且超越了扇区"
            Exit Function
        End If
    End If
    Dim sid As Long
    sid = cf.ArrDir(dirIndex).StartingSectorID
    Dim i As LongLong
    Dim p As LongLong
    Dim b() As Byte
    ReDim b(cf.lSectorSize - 1) As Byte
    Do
        Dim offset As LongLong
        offset = getOffsetBySID(sid)
        If offset > &H7FFFFFFF Then
            ReWriteStreamFAT = "复合文档：偏移量超过 32 位限制"
            Exit Function
        End If
        cf.r.SeekFile CLng(offset), OriginF
        For i = 0 To cf.lSectorSize - 1
            If p >= ilen Then Exit For
            b(i) = WriteBytes(p)
            p = p + 1
        Next
        If p < ilen Then
            ReDim Preserve b(i - 1)
        End If
        cf.r.WriteFile b
        If p >= ilen Then Exit Function
        sid = cf.FAT(sid)
    Loop
End Function

Function GetAllStream(SavePath As String) As String
    Dim ret As String
    Dim i As Long
    Dim b() As Byte
    If VBA.Right$(SavePath, 1) <> Application.PathSeparator Then SavePath = SavePath & Application.PathSeparator
    For i = 0 To UBound(cf.ArrDir)
        If cf.ArrDir(i).ObjectType = 2 Then
            ret = GetStream(cf.ArrDir(i).StrDirName, b)
            If VBA.Len(ret) Then
                GetAllStream = ret
                Exit Function
            End If
            ByteToFile makeDir(SavePath, cf.ArrDir(i).StrDirName), b
        End If
    Next
End Function

Function GetStream(dir_name As String, RetBytes() As Byte) As String
    If cf.h.Exists(dir_name) Then
        GetStream = GetStreamByDirIndex(VBA.CLng(cf.h.GetItem(dir_name)), RetBytes)
    Else
        GetStream = "复合文档：不存在的目录"
        Exit Function
    End If
End Function

Private Function GetStreamByDirIndex(dirIndex As Long, RetBytes() As Byte) As String
    If cf.ArrDir(dirIndex).ObjectType <> 2 Then
        GetStreamByDirIndex = "复合文档：不是数据流"
        Exit Function
    End If
    If cf.ArrDir(dirIndex).StartingSectorID = Free_SID Then
        GetStreamByDirIndex = "复合文档：流的大小为0"
        Exit Function
    End If
    ReDim RetBytes(cf.ArrDir(dirIndex).StreamSize - 1) As Byte
    If cf.ArrDir(dirIndex).StreamSize < cf.Header.MiniStreamSize Then
        GetStreamByDirIndex = GetStreamMiniFAT(dirIndex, RetBytes)
    Else
        GetStreamByDirIndex = GetStreamFAT(dirIndex, RetBytes)
    End If
End Function

Private Function GetStreamFAT(dirIndex As Long, RetBytes() As Byte) As String
    Dim b() As Byte
    ReDim b(cf.lSectorSize - 1) As Byte
    Dim sid As Long
    sid = cf.ArrDir(dirIndex).StartingSectorID
    Dim i As LongLong
    Dim p As LongLong
    Do
        Dim offset As LongLong
        offset = getOffsetBySID(sid)
        If offset > &H7FFFFFFF Then
            GetStreamFAT = "复合文档：偏移量超过 32 位限制"
            Exit Function
        End If
        cf.r.SeekFile CLng(offset), OriginF
        cf.r.Read b
        For i = 0 To cf.lSectorSize - 1
            If p >= cf.ArrDir(dirIndex).StreamSize Then Exit Function
            RetBytes(p) = b(i)
            p = p + 1
        Next
        sid = cf.FAT(sid)
    Loop
End Function

Private Function GetStreamMiniFAT(dirIndex As Long, RetBytes() As Byte) As String
    Dim b() As Byte
    ReDim b(cf.lShortSectorSize - 1) As Byte
    Dim miniSID As Long
    miniSID = cf.ArrDir(dirIndex).StartingSectorID
    Dim i As LongLong
    Dim p As LongLong
    Do Until miniSID = End_Of_Chain_SID
        Dim offset As LongLong
        offset = getOffsetByMiniFATSID(miniSID)
        If offset > &H7FFFFFFF Then
            GetStreamMiniFAT = "复合文档：偏移量超过 32 位限制"
            Exit Function
        End If
        cf.r.SeekFile CLng(offset), OriginF
        cf.r.Read b
        For i = 0 To cf.lShortSectorSize - 1
            If p >= cf.ArrDir(dirIndex).StreamSize Then Exit Do
            RetBytes(p) = b(i)
            p = p + 1
        Next
        miniSID = cf.MiniFAT(miniSID)
    Loop
End Function

Private Function getOffsetBySID(ByVal sid As Long) As LongLong
    getOffsetBySID = CFHEADER_SIZE + sid * cf.lSectorSize
    Debug.Print "getOffsetBySID(" & sid & "): " & getOffsetBySID
End Function

Private Function getOffsetByMiniFATSID(ByVal miniSID As Long) As LongLong
    Dim sid As Long
    sid = cf.ArrDir(0).StartingSectorID
    Dim offset As LongLong
    offset = miniSID
    Do Until offset < cf.ssNumPerSector
        offset = offset - cf.ssNumPerSector
        sid = cf.FAT(sid)
    Loop
    getOffsetByMiniFATSID = CFHEADER_SIZE + sid * cf.lSectorSize + offset * cf.lShortSectorSize
    Debug.Print "getOffsetByMiniFATSID(" & miniSID & "): " & getOffsetByMiniFATSID
End Function

Private Sub Class_Terminate()
    Erase cf.FAT, cf.DIFAT, cf.MiniFAT, cf.ArrDir
    Set cf.h = Nothing
End Sub

Private Function makeDir(SavePath As String, DirName As String)
    Dim arr
    Dim i As Long
    Dim tmp As String
    tmp = SavePath
    If VBA.Len(VBA.Dir(tmp, vbDirectory)) = 0 Then
        VBA.MkDir tmp
    End If
    arr = VBA.Split(DirName, Application.PathSeparator)
    For i = 0 To UBound(arr) - 1
        tmp = tmp & arr(i) & Application.PathSeparator
        If VBA.Len(VBA.Dir(tmp, vbDirectory)) = 0 Then
            VBA.MkDir tmp
        End If
    Next
    makeDir = tmp & arr(i)
End Function

Private Function ByteToFile(file_name As String, b() As Byte)
    Dim iFreefile As Integer
    iFreefile = VBA.FreeFile()
    Open file_name For Binary As iFreefile
    Put #iFreefile, 1, b
    Close iFreefile
End Function
