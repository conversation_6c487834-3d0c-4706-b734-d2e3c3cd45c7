using System;
using System.Collections;
using System.Collections.Generic;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Text;

[assembly: CompilationRelaxations(8)]
[assembly: RuntimeCompatibility(WrapNonExceptionThrows = true)]
[assembly: AssemblyVersion("0.0.0.0")]
namespace GUT.Architecture;

public class ParsingFailed : Exception
{
	public ulong? Offset { get; set; }

	public ulong? Length { get; set; }

	public ParsingFailed(string Message)
		: base(Message)
	{
		Offset = null;
		Length = null;
	}

	public ParsingFailed(string Message, ulong Offset)
		: base(Message)
	{
		this.Offset = Offset;
		Length = null;
	}

	public ParsingFailed(string Message, ulong Offset, ulong Length)
		: base(Message)
	{
		this.Offset = Offset;
		this.Length = Length;
	}
}
public class ParserNotApplicable : Exception
{
	public ParserNotApplicable(string Message)
		: base(Message)
	{
	}
}
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method, AllowMultiple = true)]
public class CanDetect : Attribute
{
	public string VulnerabilityID;

	public CanDetect(string VulnerabilityID)
	{
		this.VulnerabilityID = VulnerabilityID;
	}
}
public class GUTBase
{
	private Version _GUTArchitectureVersion = new Version(1, 22);

	public Version GUTArchitectureVersion => _GUTArchitectureVersion;
}
public class DataStructure : GUTBase
{
	private ulong _DataStructureOffset;

	private ulong _DataStructureLength;

	private List<ParsingNote> _ParsingNotes = new List<ParsingNote>();

	public ulong DataStructureLength
	{
		get
		{
			return _DataStructureLength;
		}
		set
		{
			if (value != _DataStructureLength)
			{
				_DataStructureLength = value;
			}
		}
	}

	public ulong DataStructureOffset
	{
		get
		{
			return _DataStructureOffset;
		}
		set
		{
			if (value != _DataStructureOffset)
			{
				_DataStructureOffset = value;
			}
		}
	}

	public DataStructure()
	{
	}

	public DataStructure(DataInByteArray Data)
	{
		DataStructureOffset = Data.CurrentPosition;
		ParseData(Data);
		DataStructureLength = Data.CurrentPosition - DataStructureOffset;
	}

	protected virtual void ParseData(DataInByteArray Data)
	{
		FieldInfo[] fieldsInTheCorrectOrder = GetFieldsInTheCorrectOrder();
		uint num = 0u;
		uint num2 = 0u;
		ulong? num3 = null;
		ulong num4 = 0uL;
		FieldInfo[] array = fieldsInTheCorrectOrder;
		foreach (FieldInfo fieldInfo in array)
		{
			if (fieldInfo == null)
			{
				continue;
			}
			object[] customAttributes = fieldInfo.GetCustomAttributes(inherit: false);
			ConstantLength constantLength = null;
			BeginBitField beginBitField = null;
			BitFieldSize bitFieldSize = null;
			DoNotAutoProcess doNotAutoProcess = null;
			object[] array2 = customAttributes;
			foreach (object obj in array2)
			{
				if (obj is ConstantLength)
				{
					constantLength = (ConstantLength)obj;
				}
				else if (obj is BeginBitField)
				{
					beginBitField = (BeginBitField)obj;
				}
				else if (obj is BitFieldSize)
				{
					bitFieldSize = (BitFieldSize)obj;
				}
				else if (obj is DoNotAutoProcess)
				{
					doNotAutoProcess = (DoNotAutoProcess)obj;
				}
			}
			if (doNotAutoProcess != null)
			{
				continue;
			}
			if (fieldInfo.FieldType.IsSubclassOf(typeof(DataItemWithVariableLength)))
			{
				if (num != 0)
				{
					AddParsingNote(ParsingNoteType.Error, "A DataStructure of type " + GetType().Name + " has an incomplete bitfield", DataStructureOffset);
				}
				if (constantLength != null)
				{
					object value = Activator.CreateInstance(fieldInfo.FieldType, Data, constantLength.Length);
					fieldInfo.SetValue(this, value);
				}
				else
				{
					object value2 = Activator.CreateInstance(fieldInfo.FieldType, Data);
					fieldInfo.SetValue(this, value2);
				}
			}
			else if (fieldInfo.FieldType.IsSubclassOf(typeof(DataItem)))
			{
				if (beginBitField != null)
				{
					if (num != 0)
					{
						AddParsingNote(ParsingNoteType.Error, "A DataStructure of type " + GetType().Name + " has tried to start a new bitfield without finishing the previous one", DataStructureOffset);
					}
					num = beginBitField.BytesInBitField;
					num2 = 0u;
					num4 = Data.CurrentPosition;
				}
				if (num != 0)
				{
					if (bitFieldSize == null)
					{
						AddParsingNote(ParsingNoteType.Error, "A DataStructure of type " + GetType().Name + " has an incomplete bitfield", DataStructureOffset);
						continue;
					}
					if (!num3.HasValue)
					{
						switch (num)
						{
						case 1u:
						{
							byte? b = Data.ReadUInt8();
							if (!((int?)b).HasValue)
							{
								AddParsingNote(ParsingNoteType.Error, "A DataStructure of type " + GetType().Name + " hit EOF while trying to read a bit field", DataStructureOffset);
								continue;
							}
							num3 = b.Value;
							break;
						}
						case 2u:
						{
							ushort? num6 = Data.ReadUInt16();
							if (!((int?)num6).HasValue)
							{
								AddParsingNote(ParsingNoteType.Error, "A DataStructure of type " + GetType().Name + " hit EOF while trying to read a bit field", DataStructureOffset);
								continue;
							}
							num3 = num6.Value;
							break;
						}
						case 4u:
						{
							uint? num5 = Data.ReadUInt32();
							if (!num5.HasValue)
							{
								AddParsingNote(ParsingNoteType.Error, "A DataStructure of type " + GetType().Name + " hit EOF while trying to read a bit field", DataStructureOffset);
								continue;
							}
							num3 = num5.Value;
							break;
						}
						default:
							AddParsingNote(ParsingNoteType.Error, "A DataStructure of type " + GetType().Name + " contains a bitfield that spans an unsupported number of bytes", DataStructureOffset);
							continue;
						}
					}
					ulong num7 = (ulong)(((int)num3.Value >> (int)num2) & (int)(Math.Pow(2.0, bitFieldSize.NumberOfBits) - 1.0));
					num2 += bitFieldSize.NumberOfBits;
					object value3 = Activator.CreateInstance(fieldInfo.FieldType, num7, num4, num);
					fieldInfo.SetValue(this, value3);
					if (num2 == num * 8)
					{
						num = 0u;
						num2 = 0u;
						num4 = 0uL;
						num3 = null;
					}
				}
				else
				{
					object value4 = Activator.CreateInstance(fieldInfo.FieldType, Data);
					fieldInfo.SetValue(this, value4);
				}
			}
			else if (fieldInfo.FieldType.IsSubclassOf(typeof(DataStructure)))
			{
				if (num != 0)
				{
					AddParsingNote(ParsingNoteType.Error, "A DataStructure of type " + GetType().Name + " has an incomplete bitfield", DataStructureOffset);
				}
				object value5 = Activator.CreateInstance(fieldInfo.FieldType, Data);
				fieldInfo.SetValue(this, value5);
			}
			else
			{
				if (!(fieldInfo.FieldType.Name == "List`1") || !fieldInfo.FieldType.IsGenericType)
				{
					continue;
				}
				if (num != 0)
				{
					AddParsingNote(ParsingNoteType.Error, "A DataStructure of type " + GetType().Name + " has an incomplete bitfield", DataStructureOffset);
				}
				Type[] genericArguments = fieldInfo.FieldType.GetGenericArguments();
				if (genericArguments.Length != 1)
				{
					continue;
				}
				if (genericArguments[0].Equals(typeof(DataStructure)))
				{
					AddParsingNote(ParsingNoteType.Error, "A DataStructure of type " + GetType().Name + " contains a List<DataStructure> member and has custom constructor.  This is not supported, because we cannot figure out what type of DataStructure should go in the list.", Data.CurrentPosition);
				}
				if (!genericArguments[0].IsSubclassOf(typeof(DataItem)) && !genericArguments[0].IsSubclassOf(typeof(DataStructure)))
				{
					continue;
				}
				if (constantLength == null)
				{
					AddParsingNote(ParsingNoteType.Error, "A DataStructure of type " + GetType().Name + " contains a List<foo> (where foo derives from DataItem or DataStructure) member and has ConstantLength attribute or custom constructor.", Data.CurrentPosition);
					continue;
				}
				string typeName = "System.Collections.Generic.List`1";
				Type type = Type.GetType(typeName).MakeGenericType(genericArguments);
				object obj2 = Activator.CreateInstance(type, (int)constantLength.Length);
				fieldInfo.SetValue(this, obj2);
				IList list = (IList)obj2;
				for (uint num8 = 0u; num8 < constantLength.Length; num8++)
				{
					object value6 = Activator.CreateInstance(genericArguments[0], Data);
					list.Add(value6);
				}
			}
		}
		if (num != 0)
		{
			AddParsingNote(ParsingNoteType.Error, "A DataStructure of type " + GetType().Name + " has an incomplete bitfield", DataStructureOffset);
		}
	}

	public virtual void BeginInitialize()
	{
		FieldInfo[] fieldsInTheCorrectOrder = GetFieldsInTheCorrectOrder();
		FieldInfo[] array = fieldsInTheCorrectOrder;
		foreach (FieldInfo fieldInfo in array)
		{
			if (fieldInfo == null)
			{
				continue;
			}
			object[] customAttributes = fieldInfo.GetCustomAttributes(inherit: false);
			ConstantLength constantLength = null;
			object[] array2 = customAttributes;
			foreach (object obj in array2)
			{
				if (obj is ConstantLength)
				{
					constantLength = (ConstantLength)obj;
				}
			}
			if (constantLength != null && fieldInfo.FieldType.Name == "List`1" && fieldInfo.FieldType.IsGenericType)
			{
				Type[] genericArguments = fieldInfo.FieldType.GetGenericArguments();
				if (genericArguments.Length == 1)
				{
					string typeName = "System.Collections.Generic.List`1";
					Type type = Type.GetType(typeName).MakeGenericType(genericArguments);
					object value = Activator.CreateInstance(type, (int)constantLength.Length);
					fieldInfo.SetValue(this, value);
				}
			}
		}
	}

	public virtual void EndInitialize()
	{
	}

	public void ClearParsingNotesAtThisLevel()
	{
		_ParsingNotes.Clear();
	}

	public List<ParsingNote> GetParsingNotes()
	{
		List<ParsingNote> list = new List<ParsingNote>();
		list.AddRange(_ParsingNotes);
		List<DataStructure> listOfSubDataStructures = GetListOfSubDataStructures();
		foreach (DataStructure item in listOfSubDataStructures)
		{
			if (item != null)
			{
				list.AddRange(item.GetParsingNotes());
			}
		}
		return list;
	}

	protected void AddParsingNote(ParsingNoteType Type, string Text)
	{
		_ParsingNotes.Add(new ParsingNote(Type, Text));
	}

	protected void AddParsingNote(ParsingNoteType Type, string Text, ulong Offset)
	{
		_ParsingNotes.Add(new ParsingNote(Type, Text, Offset));
	}

	protected void AddParsingNote(ParsingNoteType Type, string Text, ulong Offset, ulong Length)
	{
		_ParsingNotes.Add(new ParsingNote(Type, Text, Offset, Length));
	}

	protected void AddParsingNote(ParsingNoteType Type, string Text, string VulnerabilityID)
	{
		_ParsingNotes.Add(new ParsingNote(Type, Text, VulnerabilityID));
	}

	protected void AddParsingNote(ParsingNoteType Type, string Text, string VulnerabilityID, ulong Offset)
	{
		_ParsingNotes.Add(new ParsingNote(Type, Text, VulnerabilityID, Offset));
	}

	protected void AddParsingNote(ParsingNoteType Type, string Text, string VulnerabilityID, ulong Offset, ulong Length)
	{
		_ParsingNotes.Add(new ParsingNote(Type, Text, VulnerabilityID, Offset, Length));
	}

	public FieldInfo[] GetFieldsInTheCorrectOrder()
	{
		FieldInfo[] fields = GetType().GetFields();
		FieldInfo[] array = new FieldInfo[fields.Length];
		FieldInfo[] array2 = fields;
		foreach (FieldInfo fieldInfo in array2)
		{
			object[] customAttributes = fieldInfo.GetCustomAttributes(inherit: false);
			DoNotAutoProcess doNotAutoProcess = null;
			Order order = null;
			object[] array3 = customAttributes;
			foreach (object obj in array3)
			{
				if (obj is Order)
				{
					order = (Order)obj;
				}
				else if (obj is DoNotAutoProcess)
				{
					doNotAutoProcess = (DoNotAutoProcess)obj;
				}
			}
			if (doNotAutoProcess != null)
			{
				continue;
			}
			if (order == null)
			{
				if (fieldInfo.FieldType.IsAssignableFrom(typeof(DataItem)) || fieldInfo.FieldType.IsAssignableFrom(typeof(DataStructure)))
				{
					throw new ParsingFailed("The type " + GetType().Name + " has at least one member (" + fieldInfo.Name + ") without the Order attribute, and it does not implement a custom constructor or FillByteStream() method!");
				}
				if (fieldInfo.FieldType.Name == "List`1" && fieldInfo.FieldType.IsGenericType)
				{
					Type[] genericArguments = fieldInfo.FieldType.GetGenericArguments();
					if (genericArguments.Length == 1 && (genericArguments[0].Equals(typeof(DataStructure)) || genericArguments[0].IsAssignableFrom(typeof(DataItem)) || genericArguments[0].IsAssignableFrom(typeof(DataStructure))))
					{
						throw new ParsingFailed("The type " + GetType().Name + " has at least one member (" + fieldInfo.Name + ") without the Order attribute, and it does not implement a custom constructor or FillByteStream() method!");
					}
				}
			}
			else
			{
				if (order.OrderIndex >= (ulong)array.Length)
				{
					throw new ParsingFailed("The type " + GetType().Name + " has at least one member (" + fieldInfo.Name + ") with an Order attribute whose value is larger the the number of fields - don't skip numbers!");
				}
				if (array[(uint)order.OrderIndex] != null)
				{
					throw new ParsingFailed("The type " + GetType().Name + " has at two members with the same Order number (" + fieldInfo.Name + " is one of them)!");
				}
				array[(uint)order.OrderIndex] = fieldInfo;
			}
		}
		return array;
	}

	public List<DataStructure> GetListOfSubDataStructures()
	{
		List<DataStructure> list = new List<DataStructure>();
		FieldInfo[] fields = GetType().GetFields();
		FieldInfo[] array = fields;
		foreach (FieldInfo fieldInfo in array)
		{
			if (fieldInfo.FieldType.IsSubclassOf(typeof(DataStructure)))
			{
				DataStructure dataStructure = (DataStructure)fieldInfo.GetValue(this);
				if (dataStructure != null)
				{
					list.Add(dataStructure);
				}
			}
			if (!(fieldInfo.FieldType.Name == "List`1") || !fieldInfo.FieldType.IsGenericType)
			{
				continue;
			}
			Type[] genericArguments = fieldInfo.FieldType.GetGenericArguments();
			if (genericArguments.Length != 1)
			{
				continue;
			}
			if (genericArguments[0].Equals(typeof(DataStructure)))
			{
				List<DataStructure> list2 = (List<DataStructure>)fieldInfo.GetValue(this);
				if (list2 != null)
				{
					foreach (DataStructure item in list2)
					{
						list.Add(item);
					}
				}
			}
			if (!genericArguments[0].IsSubclassOf(typeof(DataStructure)))
			{
				continue;
			}
			List<DataStructure> listFromListOfDerivedClasses = GetListFromListOfDerivedClasses(fieldInfo.GetValue(this));
			if (listFromListOfDerivedClasses == null)
			{
				continue;
			}
			foreach (DataStructure item2 in listFromListOfDerivedClasses)
			{
				list.Add(item2);
			}
		}
		return list;
	}

	public static List<DataStructure> GetListFromListOfDerivedClasses(object DerivedList)
	{
		if (DerivedList == null)
		{
			return null;
		}
		List<DataStructure> list = new List<DataStructure>();
		IEnumerable enumerable = DerivedList as IEnumerable;
		foreach (object item in enumerable)
		{
			list.Add(item as DataStructure);
		}
		return list;
	}

	public override string ToString()
	{
		return "";
	}
}
public abstract class DataFormat : DataStructure
{
	private ulong _TotalSizeOfData;

	public ulong TotalSizeOfData => _TotalSizeOfData;

	public void Parse(byte[] Data)
	{
		try
		{
			ClearAnyExistingData();
			DataInByteArray dataInByteArray = new DataInByteArray(Data);
			_TotalSizeOfData = dataInByteArray.Length;
			base.DataStructureOffset = dataInByteArray.CurrentPosition;
			ParseData(dataInByteArray);
			base.DataStructureLength = dataInByteArray.CurrentPosition - base.DataStructureOffset;
		}
		catch (Exception innerException)
		{
			while (innerException.InnerException != null)
			{
				innerException = innerException.InnerException;
			}
			if (innerException is ParsingFailed)
			{
				ParsingFailed parsingFailed = (ParsingFailed)innerException;
				if (parsingFailed.Offset.HasValue && !parsingFailed.Length.HasValue)
				{
					AddParsingNote(ParsingNoteType.Error, parsingFailed.Message, parsingFailed.Offset.Value);
				}
				else if (parsingFailed.Offset.HasValue && parsingFailed.Length.HasValue)
				{
					AddParsingNote(ParsingNoteType.Error, parsingFailed.Message, parsingFailed.Offset.Value, parsingFailed.Length.Value);
				}
				else
				{
					AddParsingNote(ParsingNoteType.Error, parsingFailed.Message);
				}
			}
			else if (innerException is ParserNotApplicable)
			{
				AddParsingNote(ParsingNoteType.ParserNotApplicable, innerException.Message);
			}
			else
			{
				AddParsingNote(ParsingNoteType.Error, "An exception was thrown during parsing.\n\n" + innerException.GetType().Name + "\n\n" + innerException.Message + "\n\n" + innerException.StackTrace);
			}
		}
	}

	public void Detect()
	{
		try
		{
			RunDetectionLogic();
		}
		catch (Exception innerException)
		{
			while (!(innerException is ParsingFailed) && innerException.InnerException != null)
			{
				innerException = innerException.InnerException;
			}
			if (innerException is ParsingFailed)
			{
				AddParsingNote(ParsingNoteType.Error, innerException.Message);
			}
			else
			{
				AddParsingNote(ParsingNoteType.Error, "An exception was thrown during detection.\n\n" + innerException.Message + "\n\n" + innerException.StackTrace);
			}
		}
		MethodInfo[] methods = GetType().GetMethods();
		MethodInfo[] array = methods;
		foreach (MethodInfo methodInfo in array)
		{
			if (!IsADetectionMethod(methodInfo))
			{
				continue;
			}
			try
			{
				methodInfo.Invoke(this, null);
			}
			catch (Exception innerException2)
			{
				while (!(innerException2 is ParsingFailed) && innerException2.InnerException != null)
				{
					innerException2 = innerException2.InnerException;
				}
				if (innerException2 is ParsingFailed)
				{
					AddParsingNote(ParsingNoteType.Error, innerException2.Message);
				}
				else
				{
					AddParsingNote(ParsingNoteType.Error, "An exception was thrown during detection.\n\n" + innerException2.Message + "\n\n" + innerException2.StackTrace);
				}
			}
		}
	}

	protected virtual void RunDetectionLogic()
	{
	}

	private void ClearAnyExistingData()
	{
		ClearParsingNotesAtThisLevel();
		FieldInfo[] fields = GetType().GetFields();
		FieldInfo[] array = fields;
		foreach (FieldInfo fieldInfo in array)
		{
			if (fieldInfo.FieldType.IsSubclassOf(typeof(DataItem)) || fieldInfo.FieldType.IsSubclassOf(typeof(DataStructure)))
			{
				fieldInfo.SetValue(this, null);
			}
			if (fieldInfo.FieldType.Name == "List`1" && fieldInfo.FieldType.IsGenericType)
			{
				Type[] genericArguments = fieldInfo.FieldType.GetGenericArguments();
				if (genericArguments.Length == 1 && (genericArguments[0].Equals(typeof(DataStructure)) || genericArguments[0].IsSubclassOf(typeof(DataItem)) || genericArguments[0].IsSubclassOf(typeof(DataStructure))))
				{
					fieldInfo.SetValue(this, null);
				}
			}
		}
	}

	private bool IsADetectionMethod(MethodInfo Method)
	{
		if (Method.ReturnType != typeof(void))
		{
			return false;
		}
		if (Method.GetParameters().Length != 0)
		{
			return false;
		}
		object[] customAttributes = Method.GetCustomAttributes(typeof(CanDetect), inherit: false);
		if (customAttributes.Length != 0)
		{
			return true;
		}
		return false;
	}
}
public enum EndiannessOverrideEnum
{
	None,
	ForceLittleEndian,
	ForceBigEndian
}
public class OffsetMapping : IComparable
{
	private ulong _OffsetInStoredByteArray;

	private ulong _AdditionalOffsetInActualData;

	public ulong OffsetInStoredByteArray
	{
		get
		{
			return _OffsetInStoredByteArray;
		}
		set
		{
			_OffsetInStoredByteArray = value;
		}
	}

	public ulong AdditionalOffsetInActualData
	{
		get
		{
			return _AdditionalOffsetInActualData;
		}
		set
		{
			_AdditionalOffsetInActualData = value;
		}
	}

	public OffsetMapping(ulong offsetInStoredByteArray, ulong additionalOffsetInActualData)
	{
		_OffsetInStoredByteArray = offsetInStoredByteArray;
		_AdditionalOffsetInActualData = additionalOffsetInActualData;
	}

	int IComparable.CompareTo(object obj)
	{
		if (obj is OffsetMapping)
		{
			OffsetMapping offsetMapping = (OffsetMapping)obj;
			return (int)(OffsetInStoredByteArray - offsetMapping.OffsetInStoredByteArray);
		}
		throw new ArgumentException();
	}
}
public class DataInByteArray : GUTBase
{
	private EndiannessOverrideEnum _EndiannessOveride;

	private ulong _OffsetOfBeginningOfData;

	private List<OffsetMapping> _OffsetMappings;

	private byte[] _Data;

	private ulong _CurrentPosition;

	public EndiannessOverrideEnum EndiannessOverride
	{
		get
		{
			return _EndiannessOveride;
		}
		set
		{
			_EndiannessOveride = value;
		}
	}

	public ulong OffsetOfBeginningOfData => _OffsetOfBeginningOfData;

	public List<OffsetMapping> OffsetMappings
	{
		get
		{
			return _OffsetMappings;
		}
		set
		{
			_OffsetMappings = value;
			_OffsetMappings.Sort();
		}
	}

	public byte[] Data => _Data;

	public ulong CurrentPosition
	{
		get
		{
			return _CurrentPosition + _OffsetOfBeginningOfData + CalculateTotalRealOffsetBasedOnByteArrayOffset(_CurrentPosition);
		}
		set
		{
			if (!Seek(value))
			{
				throw new ArgumentOutOfRangeException("The requested position is not present in the data");
			}
		}
	}

	public ulong Length => (ulong)_Data.Length;

	public bool HasDataLeftToRead => _CurrentPosition < Length;

	public DataInByteArray(byte[] Data)
	{
		_Data = Data;
		_CurrentPosition = 0uL;
	}

	public DataInByteArray(byte[] Data, ulong offsetOfBeginningOfData)
	{
		_Data = Data;
		_CurrentPosition = 0uL;
		_OffsetOfBeginningOfData = offsetOfBeginningOfData;
	}

	private ulong CalculateTotalRealOffsetBasedOnByteArrayOffset(ulong byteArrayOffset)
	{
		if (_OffsetMappings == null || _OffsetMappings.Count == 0)
		{
			return 0uL;
		}
		if (byteArrayOffset < _OffsetMappings[0].OffsetInStoredByteArray)
		{
			return 0uL;
		}
		ulong result = 0uL;
		for (int i = 0; i < _OffsetMappings.Count; i++)
		{
			if (byteArrayOffset >= _OffsetMappings[i].OffsetInStoredByteArray)
			{
				result = _OffsetMappings[i].AdditionalOffsetInActualData;
			}
		}
		return result;
	}

	private bool CalculateByteArrayOffsetBasedOnRealOffset(ulong realOffset, out ulong byteArrayOffset)
	{
		if (_OffsetMappings == null)
		{
			byteArrayOffset = realOffset;
			return true;
		}
		ulong num = 0uL;
		ulong num2 = 0uL;
		ulong num3 = 0uL;
		for (int i = 0; i < _OffsetMappings.Count; i++)
		{
			num3 = _OffsetMappings[i].OffsetInStoredByteArray - num;
			if (realOffset >= num2 && realOffset < num2 + num3)
			{
				byteArrayOffset = num + (realOffset - num2);
				return true;
			}
			num = _OffsetMappings[i].OffsetInStoredByteArray;
			num2 = num + _OffsetMappings[i].AdditionalOffsetInActualData;
		}
		num3 = Length - num;
		if (realOffset >= num2 && realOffset < num2 + num3)
		{
			byteArrayOffset = num + (realOffset - num2);
			return true;
		}
		byteArrayOffset = 0uL;
		return false;
	}

	public bool Seek(ulong NewPosition)
	{
		if (NewPosition < _OffsetOfBeginningOfData)
		{
			return false;
		}
		ulong byteArrayOffset = 0uL;
		if (!CalculateByteArrayOffsetBasedOnRealOffset(NewPosition - _OffsetOfBeginningOfData, out byteArrayOffset))
		{
			return false;
		}
		if (byteArrayOffset < Length)
		{
			_CurrentPosition = byteArrayOffset;
			return true;
		}
		return false;
	}

	public sbyte? ReadInt8()
	{
		sbyte? b = PeekInt8();
		if (!((int?)b).HasValue)
		{
			_CurrentPosition = Length;
		}
		else
		{
			_CurrentPosition++;
		}
		return b;
	}

	public sbyte? PeekInt8()
	{
		return PeekInt8(0uL);
	}

	public sbyte? PeekInt8(ulong BytesToSkipFirst)
	{
		if (_CurrentPosition + BytesToSkipFirst + 1 > Length)
		{
			return null;
		}
		return (sbyte)_Data[_CurrentPosition + BytesToSkipFirst];
	}

	public short? ReadInt16()
	{
		short? num = PeekInt16();
		if (!((int?)num).HasValue)
		{
			_CurrentPosition = Length;
		}
		else
		{
			_CurrentPosition += 2uL;
		}
		return num;
	}

	public short? PeekInt16()
	{
		return PeekInt16(0uL);
	}

	public short? PeekInt16(ulong BytesToSkipFirst)
	{
		if (_CurrentPosition + BytesToSkipFirst + 2 > Length)
		{
			return null;
		}
		if (_EndiannessOveride == EndiannessOverrideEnum.ForceBigEndian)
		{
			return BitConverter.ToInt16(ReadBytesAndSwapOrder(_CurrentPosition + BytesToSkipFirst, 2uL), 0);
		}
		return BitConverter.ToInt16(_Data, (int)(_CurrentPosition + BytesToSkipFirst));
	}

	public int? ReadInt32()
	{
		int? result = PeekInt32();
		if (!result.HasValue)
		{
			_CurrentPosition = Length;
		}
		else
		{
			_CurrentPosition += 4uL;
		}
		return result;
	}

	public int? PeekInt32()
	{
		return PeekInt32(0uL);
	}

	public int? PeekInt32(ulong BytesToSkipFirst)
	{
		if (_CurrentPosition + BytesToSkipFirst + 4 > Length)
		{
			return null;
		}
		if (_EndiannessOveride == EndiannessOverrideEnum.ForceBigEndian)
		{
			return BitConverter.ToInt32(ReadBytesAndSwapOrder(_CurrentPosition + BytesToSkipFirst, 4uL), 0);
		}
		return BitConverter.ToInt32(_Data, (int)(_CurrentPosition + BytesToSkipFirst));
	}

	public long? ReadInt64()
	{
		long? result = PeekInt64();
		if (!result.HasValue)
		{
			_CurrentPosition = Length;
		}
		else
		{
			_CurrentPosition += 8uL;
		}
		return result;
	}

	public long? PeekInt64()
	{
		return PeekInt64(0uL);
	}

	public long? PeekInt64(ulong BytesToSkipFirst)
	{
		if (_CurrentPosition + BytesToSkipFirst + 8 > Length)
		{
			return null;
		}
		if (_EndiannessOveride == EndiannessOverrideEnum.ForceBigEndian)
		{
			return BitConverter.ToInt64(ReadBytesAndSwapOrder(_CurrentPosition + BytesToSkipFirst, 8uL), 0);
		}
		return BitConverter.ToInt64(_Data, (int)(_CurrentPosition + BytesToSkipFirst));
	}

	public byte? ReadUInt8()
	{
		byte? b = PeekUInt8();
		if (!((int?)b).HasValue)
		{
			_CurrentPosition = Length;
		}
		else
		{
			_CurrentPosition++;
		}
		return b;
	}

	public byte? PeekUInt8()
	{
		return PeekUInt8(0uL);
	}

	public byte? PeekUInt8(ulong BytesToSkipFirst)
	{
		if (_CurrentPosition + BytesToSkipFirst + 1 > Length)
		{
			return null;
		}
		return _Data[_CurrentPosition + BytesToSkipFirst];
	}

	public ushort? ReadUInt16()
	{
		ushort? num = PeekUInt16();
		if (!((int?)num).HasValue)
		{
			_CurrentPosition = Length;
		}
		else
		{
			_CurrentPosition += 2uL;
		}
		return num;
	}

	public ushort? PeekUInt16()
	{
		return PeekUInt16(0uL);
	}

	public ushort? PeekUInt16(ulong BytesToSkipFirst)
	{
		if (_CurrentPosition + BytesToSkipFirst + 2 > Length)
		{
			return null;
		}
		if (_EndiannessOveride == EndiannessOverrideEnum.ForceBigEndian)
		{
			return BitConverter.ToUInt16(ReadBytesAndSwapOrder(_CurrentPosition + BytesToSkipFirst, 2uL), 0);
		}
		return BitConverter.ToUInt16(_Data, (int)(_CurrentPosition + BytesToSkipFirst));
	}

	public uint? ReadUInt32()
	{
		uint? result = PeekUInt32();
		if (!result.HasValue)
		{
			_CurrentPosition = Length;
		}
		else
		{
			_CurrentPosition += 4uL;
		}
		return result;
	}

	public uint? PeekUInt32()
	{
		return PeekUInt32(0uL);
	}

	public uint? PeekUInt32(ulong BytesToSkipFirst)
	{
		if (_CurrentPosition + BytesToSkipFirst + 4 > Length)
		{
			return null;
		}
		if (_EndiannessOveride == EndiannessOverrideEnum.ForceBigEndian)
		{
			return BitConverter.ToUInt32(ReadBytesAndSwapOrder(_CurrentPosition + BytesToSkipFirst, 4uL), 0);
		}
		return BitConverter.ToUInt32(_Data, (int)(_CurrentPosition + BytesToSkipFirst));
	}

	public ulong? ReadUInt64()
	{
		ulong? result = PeekUInt64();
		if (!result.HasValue)
		{
			_CurrentPosition = Length;
		}
		else
		{
			_CurrentPosition += 8uL;
		}
		return result;
	}

	public ulong? PeekUInt64()
	{
		return PeekUInt64(0uL);
	}

	public ulong? PeekUInt64(ulong BytesToSkipFirst)
	{
		if (_CurrentPosition + BytesToSkipFirst + 8 > Length)
		{
			return null;
		}
		if (_EndiannessOveride == EndiannessOverrideEnum.ForceBigEndian)
		{
			return BitConverter.ToUInt64(ReadBytesAndSwapOrder(_CurrentPosition + BytesToSkipFirst, 8uL), 0);
		}
		return BitConverter.ToUInt64(_Data, (int)(_CurrentPosition + BytesToSkipFirst));
	}

	public sbyte? ReadBEInt8()
	{
		sbyte? b = PeekBEInt8();
		if (!((int?)b).HasValue)
		{
			_CurrentPosition = Length;
		}
		else
		{
			_CurrentPosition++;
		}
		return b;
	}

	public sbyte? PeekBEInt8()
	{
		return PeekBEInt8(0uL);
	}

	public sbyte? PeekBEInt8(ulong BytesToSkipFirst)
	{
		if (_CurrentPosition + BytesToSkipFirst + 1 > Length)
		{
			return null;
		}
		return (sbyte)_Data[_CurrentPosition + BytesToSkipFirst];
	}

	public short? ReadBEInt16()
	{
		short? num = PeekBEInt16();
		if (!((int?)num).HasValue)
		{
			_CurrentPosition = Length;
		}
		else
		{
			_CurrentPosition += 2uL;
		}
		return num;
	}

	public short? PeekBEInt16()
	{
		return PeekBEInt16(0uL);
	}

	public short? PeekBEInt16(ulong BytesToSkipFirst)
	{
		if (_CurrentPosition + BytesToSkipFirst + 2 > Length)
		{
			return null;
		}
		if (_EndiannessOveride == EndiannessOverrideEnum.ForceLittleEndian)
		{
			return BitConverter.ToInt16(_Data, (int)(_CurrentPosition + BytesToSkipFirst));
		}
		return BitConverter.ToInt16(ReadBytesAndSwapOrder(_CurrentPosition + BytesToSkipFirst, 2uL), 0);
	}

	public int? ReadBEInt32()
	{
		int? result = PeekBEInt32();
		if (!result.HasValue)
		{
			_CurrentPosition = Length;
		}
		else
		{
			_CurrentPosition += 4uL;
		}
		return result;
	}

	public int? PeekBEInt32()
	{
		return PeekBEInt32(0uL);
	}

	public int? PeekBEInt32(ulong BytesToSkipFirst)
	{
		if (_CurrentPosition + BytesToSkipFirst + 4 > Length)
		{
			return null;
		}
		if (_EndiannessOveride == EndiannessOverrideEnum.ForceLittleEndian)
		{
			return BitConverter.ToInt32(_Data, (int)(_CurrentPosition + BytesToSkipFirst));
		}
		return BitConverter.ToInt32(ReadBytesAndSwapOrder(_CurrentPosition + BytesToSkipFirst, 4uL), 0);
	}

	public long? ReadBEInt64()
	{
		long? result = PeekBEInt64();
		if (!result.HasValue)
		{
			_CurrentPosition = Length;
		}
		else
		{
			_CurrentPosition += 8uL;
		}
		return result;
	}

	public long? PeekBEInt64()
	{
		return PeekBEInt64(0uL);
	}

	public long? PeekBEInt64(ulong BytesToSkipFirst)
	{
		if (_CurrentPosition + BytesToSkipFirst + 8 > Length)
		{
			return null;
		}
		if (_EndiannessOveride == EndiannessOverrideEnum.ForceLittleEndian)
		{
			return BitConverter.ToInt64(_Data, (int)(_CurrentPosition + BytesToSkipFirst));
		}
		return BitConverter.ToInt64(ReadBytesAndSwapOrder(_CurrentPosition + BytesToSkipFirst, 8uL), 0);
	}

	public byte? ReadBEUInt8()
	{
		byte? b = PeekBEUInt8();
		if (!((int?)b).HasValue)
		{
			_CurrentPosition = Length;
		}
		else
		{
			_CurrentPosition++;
		}
		return b;
	}

	public byte? PeekBEUInt8()
	{
		return PeekBEUInt8(0uL);
	}

	public byte? PeekBEUInt8(ulong BytesToSkipFirst)
	{
		if (_CurrentPosition + BytesToSkipFirst + 1 > Length)
		{
			return null;
		}
		return _Data[_CurrentPosition + BytesToSkipFirst];
	}

	public ushort? ReadBEUInt16()
	{
		ushort? num = PeekBEUInt16();
		if (!((int?)num).HasValue)
		{
			_CurrentPosition = Length;
		}
		else
		{
			_CurrentPosition += 2uL;
		}
		return num;
	}

	public ushort? PeekBEUInt16()
	{
		return PeekBEUInt16(0uL);
	}

	public ushort? PeekBEUInt16(ulong BytesToSkipFirst)
	{
		if (_CurrentPosition + BytesToSkipFirst + 2 > Length)
		{
			return null;
		}
		if (_EndiannessOveride == EndiannessOverrideEnum.ForceLittleEndian)
		{
			return BitConverter.ToUInt16(_Data, (int)(_CurrentPosition + BytesToSkipFirst));
		}
		return BitConverter.ToUInt16(ReadBytesAndSwapOrder(_CurrentPosition + BytesToSkipFirst, 2uL), 0);
	}

	public uint? ReadBEUInt32()
	{
		uint? result = PeekBEUInt32();
		if (!result.HasValue)
		{
			_CurrentPosition = Length;
		}
		else
		{
			_CurrentPosition += 4uL;
		}
		return result;
	}

	public uint? PeekBEUInt32()
	{
		return PeekBEUInt32(0uL);
	}

	public uint? PeekBEUInt32(ulong BytesToSkipFirst)
	{
		if (_CurrentPosition + BytesToSkipFirst + 4 > Length)
		{
			return null;
		}
		if (_EndiannessOveride == EndiannessOverrideEnum.ForceLittleEndian)
		{
			return BitConverter.ToUInt32(_Data, (int)(_CurrentPosition + BytesToSkipFirst));
		}
		return BitConverter.ToUInt32(ReadBytesAndSwapOrder(_CurrentPosition + BytesToSkipFirst, 4uL), 0);
	}

	public ulong? ReadBEUInt64()
	{
		ulong? result = PeekBEUInt64();
		if (!result.HasValue)
		{
			_CurrentPosition = Length;
		}
		else
		{
			_CurrentPosition += 8uL;
		}
		return result;
	}

	public ulong? PeekBEUInt64()
	{
		return PeekBEUInt64(0uL);
	}

	public ulong? PeekBEUInt64(ulong BytesToSkipFirst)
	{
		if (_CurrentPosition + BytesToSkipFirst + 8 > Length)
		{
			return null;
		}
		if (_EndiannessOveride == EndiannessOverrideEnum.ForceLittleEndian)
		{
			return BitConverter.ToUInt64(_Data, (int)(_CurrentPosition + BytesToSkipFirst));
		}
		return BitConverter.ToUInt64(ReadBytesAndSwapOrder(_CurrentPosition + BytesToSkipFirst, 8uL), 0);
	}

	public sbyte[] ReadByteArray(ulong Size)
	{
		sbyte[] array = PeekByteArray(Size);
		if (array == null)
		{
			_CurrentPosition = Length;
		}
		else
		{
			_CurrentPosition += Size;
		}
		return array;
	}

	public sbyte[] PeekByteArray(ulong Size)
	{
		return PeekByteArray(Size, 0uL);
	}

	public sbyte[] PeekByteArray(ulong Size, ulong BytesToSkipFirst)
	{
		if ((ulong)(-1L - (long)Size) < _CurrentPosition + BytesToSkipFirst)
		{
			return null;
		}
		if (_CurrentPosition + BytesToSkipFirst + Size > Length)
		{
			return null;
		}
		sbyte[] array = new sbyte[Size];
		for (ulong num = 0uL; num < Size; num++)
		{
			array[num] = (sbyte)_Data[_CurrentPosition + BytesToSkipFirst + num];
		}
		return array;
	}

	public byte[] ReadUByteArray(ulong Size)
	{
		byte[] array = PeekUByteArray(Size);
		if (array == null)
		{
			_CurrentPosition = Length;
		}
		else
		{
			_CurrentPosition += Size;
		}
		return array;
	}

	public byte[] PeekUByteArray(ulong Size)
	{
		return PeekUByteArray(Size, 0uL);
	}

	public byte[] PeekUByteArray(ulong Size, ulong BytesToSkipFirst)
	{
		if (_CurrentPosition + BytesToSkipFirst + Size > Length)
		{
			return null;
		}
		byte[] array = new byte[Size];
		for (ulong num = 0uL; num < Size; num++)
		{
			array[num] = _Data[_CurrentPosition + BytesToSkipFirst + num];
		}
		return array;
	}

	public string ReadUnicodeString(ulong NumberOfCharacters)
	{
		string text = PeekUnicodeString(NumberOfCharacters);
		if (text == null)
		{
			_CurrentPosition = Length;
		}
		else
		{
			_CurrentPosition += NumberOfCharacters * 2;
		}
		return text;
	}

	public string PeekUnicodeString(ulong NumberOfCharacters)
	{
		return PeekUnicodeString(NumberOfCharacters, 0uL);
	}

	public string PeekUnicodeString(ulong NumberOfCharacters, ulong BytesToSkipFirst)
	{
		if (_CurrentPosition + NumberOfCharacters * 2 + BytesToSkipFirst > Length)
		{
			return null;
		}
		string text = "";
		UnicodeEncoding unicodeEncoding = new UnicodeEncoding();
		return unicodeEncoding.GetString(_Data, (int)_CurrentPosition + (int)BytesToSkipFirst, (int)(NumberOfCharacters * 2));
	}

	public string ReadASCIIString(ulong NumberOfCharacters)
	{
		string text = PeekASCIIString(NumberOfCharacters);
		if (text == null)
		{
			_CurrentPosition = Length;
		}
		else
		{
			_CurrentPosition += NumberOfCharacters;
		}
		return text;
	}

	public string PeekASCIIString(ulong NumberOfCharacters)
	{
		return PeekASCIIString(NumberOfCharacters, 0uL);
	}

	public string PeekASCIIString(ulong NumberOfCharacters, ulong BytesToSkipFirst)
	{
		if (_CurrentPosition + NumberOfCharacters + BytesToSkipFirst > Length)
		{
			return null;
		}
		string text = "";
		ASCIIEncoding aSCIIEncoding = new ASCIIEncoding();
		return aSCIIEncoding.GetString(_Data, (int)_CurrentPosition + (int)BytesToSkipFirst, (int)NumberOfCharacters);
	}

	public static byte[] SwapByteOrder(byte[] OriginalData)
	{
		byte[] array = new byte[OriginalData.Length];
		for (int i = 0; i < array.Length; i++)
		{
			array[array.Length - 1 - i] = OriginalData[i];
		}
		return array;
	}

	private byte[] ReadBytesAndSwapOrder(ulong Offset, ulong Length)
	{
		byte[] array = new byte[Length];
		for (ulong num = 0uL; num < Length; num++)
		{
			array[num] = _Data[Offset + num];
		}
		return SwapByteOrder(array);
	}
}
public abstract class DataItem : GUTBase
{
	public ulong Offset { get; set; }

	public ulong Length { get; set; }

	public bool ParsedSuccessfully { get; set; }

	public abstract object GetValue();

	public abstract ulong? GetValueAsNullableUInt64();

	public static List<DataItem> GetListFromListOfDerivedClasses(object DerivedList)
	{
		if (DerivedList == null)
		{
			return null;
		}
		List<DataItem> list = new List<DataItem>();
		IEnumerable enumerable = DerivedList as IEnumerable;
		foreach (object item in enumerable)
		{
			list.Add(item as DataItem);
		}
		return list;
	}
}
public abstract class DataItemWithVariableLength : DataItem
{
}
public class DataItem_Int8 : DataItem
{
	public sbyte? Value { get; set; }

	public override object GetValue()
	{
		return Value;
	}

	public DataItem_Int8(sbyte? StartingValue)
	{
		Value = StartingValue;
	}

	public DataItem_Int8(sbyte? StartingValue, ulong Offset, ulong Length)
	{
		Value = StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_Int8(ulong StartingValue, ulong Offset, ulong Length)
	{
		Value = (sbyte)StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_Int8(DataInByteArray DataSource)
	{
		base.Offset = DataSource.CurrentPosition;
		sbyte? b = DataSource.ReadInt8();
		if (((int?)b).HasValue)
		{
			base.ParsedSuccessfully = true;
			base.Length = 1uL;
			Value = b.Value;
		}
		else
		{
			base.ParsedSuccessfully = false;
			base.Length = 0uL;
			Value = null;
		}
	}

	public override ulong? GetValueAsNullableUInt64()
	{
		return (ulong?)Value;
	}
}
public class DataItem_Int16 : DataItem
{
	public short? Value { get; set; }

	public override object GetValue()
	{
		return Value;
	}

	public DataItem_Int16(short? StartingValue)
	{
		Value = StartingValue;
	}

	public DataItem_Int16(short? StartingValue, ulong Offset, ulong Length)
	{
		Value = StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_Int16(ulong StartingValue, ulong Offset, ulong Length)
	{
		Value = (short)StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_Int16(DataInByteArray DataSource)
	{
		base.Offset = DataSource.CurrentPosition;
		short? num = DataSource.ReadInt16();
		if (((int?)num).HasValue)
		{
			base.ParsedSuccessfully = true;
			base.Length = 2uL;
			Value = num.Value;
		}
		else
		{
			base.ParsedSuccessfully = false;
			base.Length = 0uL;
			Value = null;
		}
	}

	public override ulong? GetValueAsNullableUInt64()
	{
		return (ulong?)Value;
	}
}
public class DataItem_Int32 : DataItem
{
	public int? Value { get; set; }

	public override object GetValue()
	{
		return Value;
	}

	public DataItem_Int32(int? StartingValue)
	{
		Value = StartingValue;
	}

	public DataItem_Int32(short? StartingValue, ulong Offset, ulong Length)
	{
		Value = StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_Int32(ulong StartingValue, ulong Offset, ulong Length)
	{
		Value = (int)StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_Int32(DataInByteArray DataSource)
	{
		base.Offset = DataSource.CurrentPosition;
		int? num = DataSource.ReadInt32();
		if (num.HasValue)
		{
			base.ParsedSuccessfully = true;
			base.Length = 4uL;
			Value = num.Value;
		}
		else
		{
			base.ParsedSuccessfully = false;
			base.Length = 0uL;
			Value = null;
		}
	}

	public override ulong? GetValueAsNullableUInt64()
	{
		return (ulong?)Value;
	}
}
public class DataItem_Int64 : DataItem
{
	public long? Value { get; set; }

	public override object GetValue()
	{
		return Value;
	}

	public DataItem_Int64(long? StartingValue)
	{
		Value = StartingValue;
	}

	public DataItem_Int64(long? StartingValue, ulong Offset, ulong Length)
	{
		Value = StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_Int64(ulong StartingValue, ulong Offset, ulong Length)
	{
		Value = (long)StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_Int64(DataInByteArray DataSource)
	{
		base.Offset = DataSource.CurrentPosition;
		long? num = DataSource.ReadInt64();
		if (num.HasValue)
		{
			base.ParsedSuccessfully = true;
			base.Length = 8uL;
			Value = num.Value;
		}
		else
		{
			base.ParsedSuccessfully = false;
			base.Length = 0uL;
			Value = null;
		}
	}

	public override ulong? GetValueAsNullableUInt64()
	{
		return (ulong?)Value;
	}
}
public class DataItem_UInt8 : DataItem
{
	public byte? Value { get; set; }

	public override object GetValue()
	{
		return Value;
	}

	public DataItem_UInt8(byte? StartingValue)
	{
		Value = StartingValue;
	}

	public DataItem_UInt8(byte? StartingValue, ulong Offset, ulong Length)
	{
		Value = StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_UInt8(ulong StartingValue, ulong Offset, ulong Length)
	{
		Value = (byte)StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_UInt8(DataInByteArray DataSource)
	{
		base.Offset = DataSource.CurrentPosition;
		byte? b = DataSource.ReadUInt8();
		if (((int?)b).HasValue)
		{
			base.ParsedSuccessfully = true;
			base.Length = 1uL;
			Value = b.Value;
		}
		else
		{
			base.ParsedSuccessfully = false;
			base.Length = 0uL;
			Value = null;
		}
	}

	public override ulong? GetValueAsNullableUInt64()
	{
		return Value;
	}
}
public class DataItem_UInt16 : DataItem
{
	public ushort? Value { get; set; }

	public override object GetValue()
	{
		return Value;
	}

	public DataItem_UInt16(ushort? StartingValue)
	{
		Value = StartingValue;
	}

	public DataItem_UInt16(ushort? StartingValue, ulong Offset, ulong Length)
	{
		Value = StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_UInt16(ulong StartingValue, ulong Offset, ulong Length)
	{
		Value = (ushort)StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_UInt16(DataInByteArray DataSource)
	{
		base.Offset = DataSource.CurrentPosition;
		ushort? num = DataSource.ReadUInt16();
		if (((int?)num).HasValue)
		{
			base.ParsedSuccessfully = true;
			base.Length = 2uL;
			Value = num.Value;
		}
		else
		{
			base.ParsedSuccessfully = false;
			base.Length = 0uL;
			Value = null;
		}
	}

	public override ulong? GetValueAsNullableUInt64()
	{
		return Value;
	}
}
public class DataItem_UInt32 : DataItem
{
	public uint? Value { get; set; }

	public override object GetValue()
	{
		return Value;
	}

	public DataItem_UInt32(uint? StartingValue)
	{
		Value = StartingValue;
	}

	public DataItem_UInt32(uint? StartingValue, ulong Offset, ulong Length)
	{
		Value = StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_UInt32(ulong StartingValue, ulong Offset, ulong Length)
	{
		Value = (uint)StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_UInt32(DataInByteArray DataSource)
	{
		base.Offset = DataSource.CurrentPosition;
		uint? num = DataSource.ReadUInt32();
		if (num.HasValue)
		{
			base.ParsedSuccessfully = true;
			base.Length = 4uL;
			Value = num.Value;
		}
		else
		{
			base.ParsedSuccessfully = false;
			base.Length = 0uL;
			Value = null;
		}
	}

	public override ulong? GetValueAsNullableUInt64()
	{
		return Value;
	}
}
public class DataItem_UInt64 : DataItem
{
	public ulong? Value { get; set; }

	public override object GetValue()
	{
		return Value;
	}

	public DataItem_UInt64(ulong? StartingValue)
	{
		Value = StartingValue;
	}

	public DataItem_UInt64(ulong? StartingValue, ulong Offset, ulong Length)
	{
		Value = StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_UInt64(ulong StartingValue, ulong Offset, ulong Length)
	{
		Value = StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_UInt64(DataInByteArray DataSource)
	{
		base.Offset = DataSource.CurrentPosition;
		ulong? num = DataSource.ReadUInt64();
		if (num.HasValue)
		{
			base.ParsedSuccessfully = true;
			base.Length = 8uL;
			Value = num.Value;
		}
		else
		{
			base.ParsedSuccessfully = false;
			base.Length = 0uL;
			Value = null;
		}
	}

	public override ulong? GetValueAsNullableUInt64()
	{
		return Value;
	}
}
public class DataItem_BEInt8 : DataItem
{
	public sbyte? Value { get; set; }

	public override object GetValue()
	{
		return Value;
	}

	public DataItem_BEInt8(sbyte? StartingValue)
	{
		Value = StartingValue;
	}

	public DataItem_BEInt8(sbyte? StartingValue, ulong Offset, ulong Length)
	{
		Value = StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_BEInt8(ulong StartingValue, ulong Offset, ulong Length)
	{
		Value = (sbyte)StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_BEInt8(DataInByteArray DataSource)
	{
		base.Offset = DataSource.CurrentPosition;
		sbyte? b = DataSource.ReadBEInt8();
		if (((int?)b).HasValue)
		{
			base.ParsedSuccessfully = true;
			base.Length = 1uL;
			Value = b.Value;
		}
		else
		{
			base.ParsedSuccessfully = false;
			base.Length = 0uL;
			Value = null;
		}
	}

	public override ulong? GetValueAsNullableUInt64()
	{
		return (ulong?)Value;
	}
}
public class DataItem_BEInt16 : DataItem
{
	public short? Value { get; set; }

	public override object GetValue()
	{
		return Value;
	}

	public DataItem_BEInt16(short? StartingValue)
	{
		Value = StartingValue;
	}

	public DataItem_BEInt16(short? StartingValue, ulong Offset, ulong Length)
	{
		Value = StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_BEInt16(ulong StartingValue, ulong Offset, ulong Length)
	{
		Value = (short)StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_BEInt16(DataInByteArray DataSource)
	{
		base.Offset = DataSource.CurrentPosition;
		short? num = DataSource.ReadBEInt16();
		if (((int?)num).HasValue)
		{
			base.ParsedSuccessfully = true;
			base.Length = 2uL;
			Value = num.Value;
		}
		else
		{
			base.ParsedSuccessfully = false;
			base.Length = 0uL;
			Value = null;
		}
	}

	public override ulong? GetValueAsNullableUInt64()
	{
		return (ulong?)Value;
	}
}
public class DataItem_BEInt32 : DataItem
{
	public int? Value { get; set; }

	public override object GetValue()
	{
		return Value;
	}

	public DataItem_BEInt32(int? StartingValue)
	{
		Value = StartingValue;
	}

	public DataItem_BEInt32(short? StartingValue, ulong Offset, ulong Length)
	{
		Value = StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_BEInt32(ulong StartingValue, ulong Offset, ulong Length)
	{
		Value = (int)StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_BEInt32(DataInByteArray DataSource)
	{
		base.Offset = DataSource.CurrentPosition;
		int? num = DataSource.ReadBEInt32();
		if (num.HasValue)
		{
			base.ParsedSuccessfully = true;
			base.Length = 4uL;
			Value = num.Value;
		}
		else
		{
			base.ParsedSuccessfully = false;
			base.Length = 0uL;
			Value = null;
		}
	}

	public override ulong? GetValueAsNullableUInt64()
	{
		return (ulong?)Value;
	}
}
public class DataItem_BEInt64 : DataItem
{
	public long? Value { get; set; }

	public override object GetValue()
	{
		return Value;
	}

	public DataItem_BEInt64(long? StartingValue)
	{
		Value = StartingValue;
	}

	public DataItem_BEInt64(long? StartingValue, ulong Offset, ulong Length)
	{
		Value = StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_BEInt64(ulong StartingValue, ulong Offset, ulong Length)
	{
		Value = (long)StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_BEInt64(DataInByteArray DataSource)
	{
		base.Offset = DataSource.CurrentPosition;
		long? num = DataSource.ReadBEInt64();
		if (num.HasValue)
		{
			base.ParsedSuccessfully = true;
			base.Length = 8uL;
			Value = num.Value;
		}
		else
		{
			base.ParsedSuccessfully = false;
			base.Length = 0uL;
			Value = null;
		}
	}

	public override ulong? GetValueAsNullableUInt64()
	{
		return (ulong?)Value;
	}
}
public class DataItem_BEUInt8 : DataItem
{
	public byte? Value { get; set; }

	public override object GetValue()
	{
		return Value;
	}

	public DataItem_BEUInt8(byte? StartingValue)
	{
		Value = StartingValue;
	}

	public DataItem_BEUInt8(byte? StartingValue, ulong Offset, ulong Length)
	{
		Value = StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_BEUInt8(ulong StartingValue, ulong Offset, ulong Length)
	{
		Value = (byte)StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_BEUInt8(DataInByteArray DataSource)
	{
		base.Offset = DataSource.CurrentPosition;
		byte? b = DataSource.ReadBEUInt8();
		if (((int?)b).HasValue)
		{
			base.ParsedSuccessfully = true;
			base.Length = 1uL;
			Value = b.Value;
		}
		else
		{
			base.ParsedSuccessfully = false;
			base.Length = 0uL;
			Value = null;
		}
	}

	public override ulong? GetValueAsNullableUInt64()
	{
		return Value;
	}
}
public class DataItem_BEUInt16 : DataItem
{
	public ushort? Value { get; set; }

	public override object GetValue()
	{
		return Value;
	}

	public DataItem_BEUInt16(ushort? StartingValue)
	{
		Value = StartingValue;
	}

	public DataItem_BEUInt16(ushort? StartingValue, ulong Offset, ulong Length)
	{
		Value = StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_BEUInt16(ulong StartingValue, ulong Offset, ulong Length)
	{
		Value = (ushort)StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_BEUInt16(DataInByteArray DataSource)
	{
		base.Offset = DataSource.CurrentPosition;
		ushort? num = DataSource.ReadBEUInt16();
		if (((int?)num).HasValue)
		{
			base.ParsedSuccessfully = true;
			base.Length = 2uL;
			Value = num.Value;
		}
		else
		{
			base.ParsedSuccessfully = false;
			base.Length = 0uL;
			Value = null;
		}
	}

	public override ulong? GetValueAsNullableUInt64()
	{
		return Value;
	}
}
public class DataItem_BEUInt32 : DataItem
{
	public uint? Value { get; set; }

	public override object GetValue()
	{
		return Value;
	}

	public DataItem_BEUInt32(uint? StartingValue)
	{
		Value = StartingValue;
	}

	public DataItem_BEUInt32(uint? StartingValue, ulong Offset, ulong Length)
	{
		Value = StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_BEUInt32(ulong StartingValue, ulong Offset, ulong Length)
	{
		Value = (uint)StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_BEUInt32(DataInByteArray DataSource)
	{
		base.Offset = DataSource.CurrentPosition;
		uint? num = DataSource.ReadBEUInt32();
		if (num.HasValue)
		{
			base.ParsedSuccessfully = true;
			base.Length = 4uL;
			Value = num.Value;
		}
		else
		{
			base.ParsedSuccessfully = false;
			base.Length = 0uL;
			Value = null;
		}
	}

	public override ulong? GetValueAsNullableUInt64()
	{
		return Value;
	}
}
public class DataItem_BEUInt64 : DataItem
{
	public ulong? Value { get; set; }

	public override object GetValue()
	{
		return Value;
	}

	public DataItem_BEUInt64(ulong? StartingValue)
	{
		Value = StartingValue;
	}

	public DataItem_BEUInt64(ulong? StartingValue, ulong Offset, ulong Length)
	{
		Value = StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_BEUInt64(ulong StartingValue, ulong Offset, ulong Length)
	{
		Value = StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_BEUInt64(DataInByteArray DataSource)
	{
		base.Offset = DataSource.CurrentPosition;
		ulong? num = DataSource.ReadBEUInt64();
		if (num.HasValue)
		{
			base.ParsedSuccessfully = true;
			base.Length = 8uL;
			Value = num.Value;
		}
		else
		{
			base.ParsedSuccessfully = false;
			base.Length = 0uL;
			Value = null;
		}
	}

	public override ulong? GetValueAsNullableUInt64()
	{
		return Value;
	}
}
public class DataItem_ByteArray : DataItemWithVariableLength
{
	public sbyte[] Value { get; set; }

	public override object GetValue()
	{
		return Value;
	}

	public DataItem_ByteArray(sbyte[] StartingValue)
	{
		Value = StartingValue;
	}

	public DataItem_ByteArray(sbyte[] StartingValue, ulong Offset, ulong Length)
	{
		Value = StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_ByteArray(DataInByteArray DataSource)
	{
		throw new ParsingFailed("A DataItem_ByteArray at " + DataSource.CurrentPosition + " was being autoprocessed, but didn't have a ConstantLength attribute");
	}

	public DataItem_ByteArray(DataInByteArray DataSource, ulong Size)
	{
		base.Offset = DataSource.CurrentPosition;
		sbyte[] array = DataSource.ReadByteArray(Size);
		if (array != null)
		{
			base.ParsedSuccessfully = true;
			base.Length = Size;
			Value = array;
		}
		else
		{
			base.ParsedSuccessfully = false;
			base.Length = 0uL;
			Value = null;
		}
	}

	public override ulong? GetValueAsNullableUInt64()
	{
		return null;
	}
}
public class DataItem_UByteArray : DataItemWithVariableLength
{
	public byte[] Value { get; set; }

	public override object GetValue()
	{
		return Value;
	}

	public DataItem_UByteArray(byte[] StartingValue)
	{
		Value = StartingValue;
	}

	public DataItem_UByteArray(byte[] StartingValue, ulong Offset, ulong Length)
	{
		Value = StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_UByteArray(DataInByteArray DataSource)
	{
		throw new ParsingFailed("A DataItem_UByteArray at " + DataSource.CurrentPosition + " was being autoprocessed, but didn't have a ConstantLength attribute");
	}

	public DataItem_UByteArray(DataInByteArray DataSource, ulong Size)
	{
		base.Offset = DataSource.CurrentPosition;
		byte[] array = DataSource.ReadUByteArray(Size);
		if (array != null)
		{
			base.ParsedSuccessfully = true;
			base.Length = Size;
			Value = array;
		}
		else
		{
			base.ParsedSuccessfully = false;
			base.Length = 0uL;
			Value = null;
		}
	}

	public override ulong? GetValueAsNullableUInt64()
	{
		return null;
	}
}
public class DataItem_UnicodeString : DataItemWithVariableLength
{
	private UnicodeEncoding _Encoding = new UnicodeEncoding();

	public byte[] ValueAsByteArray { get; set; }

	public string Value
	{
		get
		{
			if (ValueAsByteArray == null)
			{
				return null;
			}
			return _Encoding.GetString(ValueAsByteArray);
		}
		set
		{
			ValueAsByteArray = ((value != null) ? _Encoding.GetBytes(value) : null);
		}
	}

	public override object GetValue()
	{
		return Value;
	}

	public DataItem_UnicodeString(string StartingValue)
	{
		ValueAsByteArray = _Encoding.GetBytes(StartingValue);
	}

	public DataItem_UnicodeString(string StartingValue, ulong Offset, ulong Length)
	{
		ValueAsByteArray = _Encoding.GetBytes(StartingValue);
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_UnicodeString(DataInByteArray DataSource)
	{
		base.Offset = DataSource.CurrentPosition;
		ulong num = 0uL;
		ushort? num2;
		ushort? num3;
		do
		{
			num2 = DataSource.PeekUInt16(num);
			num += 2;
			if (!((int?)num2).HasValue)
			{
				break;
			}
			num3 = num2;
		}
		while (num3.GetValueOrDefault() != 0 || !num3.HasValue);
		if (((int?)num2).HasValue)
		{
			Value = DataSource.ReadUnicodeString(num / 2);
		}
		base.ParsedSuccessfully = Value != null;
		base.Length = ((Value != null) ? ((ulong)Value.Length * 2uL) : 0);
	}

	public DataItem_UnicodeString(DataInByteArray DataSource, ulong NumberOfCharacters)
	{
		base.Offset = DataSource.CurrentPosition;
		Value = DataSource.ReadUnicodeString(NumberOfCharacters);
		base.ParsedSuccessfully = Value != null;
		base.Length = ((Value != null) ? ((ulong)Value.Length * 2uL) : 0);
	}

	public override ulong? GetValueAsNullableUInt64()
	{
		return null;
	}
}
public class DataItem_ASCIIString : DataItemWithVariableLength
{
	private ASCIIEncoding _Encoding = new ASCIIEncoding();

	public byte[] ValueAsByteArray { get; set; }

	public string Value
	{
		get
		{
			if (ValueAsByteArray == null)
			{
				return null;
			}
			return _Encoding.GetString(ValueAsByteArray);
		}
		set
		{
			ValueAsByteArray = ((value != null) ? _Encoding.GetBytes(value) : null);
		}
	}

	public override object GetValue()
	{
		return Value;
	}

	public DataItem_ASCIIString(string StartingValue)
	{
		ValueAsByteArray = _Encoding.GetBytes(StartingValue);
	}

	public DataItem_ASCIIString(string StartingValue, ulong Offset, ulong Length)
	{
		ValueAsByteArray = _Encoding.GetBytes(StartingValue);
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_ASCIIString(DataInByteArray DataSource)
	{
		base.Offset = DataSource.CurrentPosition;
		ulong num = 0uL;
		byte? b;
		byte? b2;
		do
		{
			b = DataSource.PeekUInt8(num);
			num++;
			if (!((int?)b).HasValue)
			{
				break;
			}
			b2 = b;
		}
		while (b2.GetValueOrDefault() != 0 || !b2.HasValue);
		if (((int?)b).HasValue)
		{
			Value = DataSource.ReadASCIIString(num);
		}
		base.ParsedSuccessfully = Value != null;
		base.Length = (ulong)((Value != null) ? Value.Length : 0);
	}

	public DataItem_ASCIIString(DataInByteArray DataSource, ulong NumberOfCharacters)
	{
		base.Offset = DataSource.CurrentPosition;
		Value = DataSource.ReadASCIIString(NumberOfCharacters);
		base.ParsedSuccessfully = Value != null;
		base.Length = (ulong)((Value != null) ? Value.Length : 0);
	}

	public override ulong? GetValueAsNullableUInt64()
	{
		return null;
	}
}
public class DataItem_GUID : DataItem
{
	public Guid? Value { get; set; }

	public override object GetValue()
	{
		return Value;
	}

	public DataItem_GUID(Guid StartingValue)
	{
		Value = StartingValue;
	}

	public DataItem_GUID(Guid StartingValue, ulong Offset, ulong Length)
	{
		Value = StartingValue;
		base.Offset = Offset;
		base.Length = Length;
	}

	public DataItem_GUID(DataInByteArray DataSource)
	{
		base.Offset = DataSource.CurrentPosition;
		byte[] array = DataSource.ReadUByteArray(16uL);
		if (array != null)
		{
			base.ParsedSuccessfully = true;
			base.Length = 16uL;
			Value = new Guid(array);
		}
		else
		{
			base.ParsedSuccessfully = false;
			base.Length = 0uL;
			Value = null;
		}
	}

	public override ulong? GetValueAsNullableUInt64()
	{
		return null;
	}

	public override string ToString()
	{
		if (Value.HasValue)
		{
			return Value.ToString();
		}
		return string.Empty;
	}
}
[AttributeUsage(AttributeTargets.Field)]
public class SuppressDisplay : Attribute
{
}
[AttributeUsage(AttributeTargets.Field)]
public class ConstantLength : Attribute
{
	public ulong Length;

	public ConstantLength(ulong Length)
	{
		this.Length = Length;
	}
}
[AttributeUsage(AttributeTargets.Field)]
public class BeginBitField : Attribute
{
	public uint BytesInBitField;

	public BeginBitField(uint BytesInBitField)
	{
		this.BytesInBitField = BytesInBitField;
	}
}
[AttributeUsage(AttributeTargets.Field)]
public class BitFieldSize : Attribute
{
	public uint NumberOfBits;

	public BitFieldSize(uint NumberOfBits)
	{
		this.NumberOfBits = NumberOfBits;
	}
}
[AttributeUsage(AttributeTargets.Field)]
public class Order : Attribute
{
	public ulong OrderIndex;

	public Order(ulong OrderIndex)
	{
		this.OrderIndex = OrderIndex;
	}
}
[AttributeUsage(AttributeTargets.Field)]
public class DoNotAutoProcess : Attribute
{
}
public enum ParsingNoteType
{
	ParserNotApplicable,
	Comment,
	Warning,
	Error,
	PossiblyMalicious,
	DefinitelyMalicious
}
public class ParsingNote : GUTBase
{
	private ParsingNoteType _Type;

	private string _Text;

	private string _VulnerabilityID;

	private ulong? _Offset = null;

	private ulong? _Length = null;

	public ParsingNoteType Type
	{
		get
		{
			return _Type;
		}
		set
		{
			_Type = value;
		}
	}

	public string Text
	{
		get
		{
			return _Text;
		}
		set
		{
			_Text = value;
		}
	}

	public string VulnerabilityID
	{
		get
		{
			return _VulnerabilityID;
		}
		set
		{
			_VulnerabilityID = value;
		}
	}

	public ulong? Offset
	{
		get
		{
			return _Offset;
		}
		set
		{
			_Offset = value;
		}
	}

	public ulong? Length
	{
		get
		{
			return _Length;
		}
		set
		{
			_Length = value;
		}
	}

	public ParsingNote(ParsingNoteType Type, string Text)
	{
		_Type = Type;
		_Text = Text;
	}

	public ParsingNote(ParsingNoteType Type, string Text, ulong Offset)
	{
		_Type = Type;
		_Text = Text;
		_Offset = Offset;
	}

	public ParsingNote(ParsingNoteType Type, string Text, ulong Offset, ulong Length)
	{
		_Type = Type;
		_Text = Text;
		_Offset = Offset;
		_Length = Length;
	}

	public ParsingNote(ParsingNoteType Type, string Text, string VulnerabilityID)
	{
		_Type = Type;
		_Text = Text;
		_VulnerabilityID = VulnerabilityID;
	}

	public ParsingNote(ParsingNoteType Type, string Text, string VulnerabilityID, ulong Offset)
	{
		_Type = Type;
		_Text = Text;
		_VulnerabilityID = VulnerabilityID;
		_Offset = Offset;
	}

	public ParsingNote(ParsingNoteType Type, string Text, string VulnerabilityID, ulong Offset, ulong Length)
	{
		_Type = Type;
		_Text = Text;
		_VulnerabilityID = VulnerabilityID;
		_Offset = Offset;
		_Length = Length;
	}
}
