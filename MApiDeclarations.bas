Attribute VB_Name = "MApiDeclarations"
Option Explicit

' API声明模块 - 包含所有必要的Windows API声明

#If VBA7 Then
    ' 64位兼容的API声明
    Private Declare PtrSafe Sub CopyMemory Lib "kernel32" Alias "RtlMoveMemory" (Destination As Any, Source As Any, ByVal Length As LongPtr)
#Else
    ' 32位API声明
    Private Declare Sub CopyMemory Lib "kernel32" Alias "RtlMoveMemory" (Destination As Any, Source As Any, ByVal Length As Long)
#End If

' 公共包装函数，供其他模块调用
#If VBA7 Then
Public Sub CopyMemoryWrapper(Destination As Any, Source As Any, ByVal Length As LongPtr)
    CopyMemory Destination, Source, Length
End Sub
#Else
Public Sub CopyMemoryWrapper(Destination As Any, Source As Any, ByVal Length As Long)
    CopyMemory Destination, Source, Length
End Sub
#End If
