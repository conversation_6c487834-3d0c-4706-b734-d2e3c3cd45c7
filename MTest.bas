Attribute VB_Name = "MTest"
Option Explicit

' 测试模块 - 包含您的测试代码

Private Sub Test_CBytes_PrintDirs()
    Dim f As CFile
    Set f = NewCFile()
    If f.OpenFile("D:\十六进制研究binary-Excel对照Biff\offvis\工作簿2(已自动还原).xls") <> True Then
        Debug.Print "Failed to open file"
        Exit Sub
    End If
    Dim b() As Byte
    b = f.ReadAll()
    f.CloseFile
    Set f = Nothing
    Dim cb As CBytes
    Set cb = NewCBytes
    cb.SetData = b
    Dim cf As CCompoundFile
    Set cf = NewCCompoundFile
    Dim ret As String
    ret = cf.Parse(cb)
    If VBA.Len(ret) Then
        Debug.Print ret
        Exit Sub
    End If
    Dim fs() As String
    fs = cf.DirsName()
    Dim i As Long
    For i = 0 To UBound(fs)
        Debug.Print i, fs(i)
    Next
    Dim sb() As Byte
    ret = cf.GetStream("Root Entry\_VBA_PROJECT_CUR\PROJECT", sb)
    If VBA.Len(ret) Then
        Debug.Print ret
    End If
    Debug.Print VBA.StrConv(sb, vbUnicode)
    Set cf = Nothing
    Set cb = Nothing
End Sub
