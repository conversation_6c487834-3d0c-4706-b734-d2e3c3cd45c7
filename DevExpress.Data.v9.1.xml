<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DevExpress.Data.v9.1</name>
    </assembly>
    <members>
        <member name="T:DevExpress.XtraEditors.DXErrorProvider.ErrorType">

            <summary>
                <para>Enumerates error icon types.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraEditors.DXErrorProvider.ErrorType.Critical">
            <summary>
                <para>The 'Critical Error' icon: 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.DXErrorProvider.ErrorType.Default">
            <summary>
                <para>The default error icon specified by the <see cref="P:DevExpress.XtraEditors.BaseEdit.ErrorIcon"/> property: 

<para>
The <see cref="E:DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider.GetErrorIcon"/> event is not raised when an error of the <b>Default</b> type is assigned to an editor.
</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.DXErrorProvider.ErrorType.Information">
            <summary>
                <para>The 'Information' icon: 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.DXErrorProvider.ErrorType.None">
            <summary>
                <para>Indicates that no error is associated with an editor or a cell.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.DXErrorProvider.ErrorType.User1">
            <summary>
                <para>A user-defined icon to be provided via the <see cref="E:DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider.GetErrorIcon"/> event.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.DXErrorProvider.ErrorType.User2">
            <summary>
                <para>A user-defined icon to be provided via the <see cref="E:DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider.GetErrorIcon"/> event.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.DXErrorProvider.ErrorType.User3">
            <summary>
                <para>A user-defined icon to be provided via the <see cref="E:DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider.GetErrorIcon"/> event.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.DXErrorProvider.ErrorType.User4">
            <summary>
                <para>A user-defined icon to be provided via the <see cref="E:DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider.GetErrorIcon"/> event.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.DXErrorProvider.ErrorType.User5">
            <summary>
                <para>A user-defined icon to be provided via the <see cref="E:DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider.GetErrorIcon"/> event.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.DXErrorProvider.ErrorType.User6">
            <summary>
                <para>A user-defined icon to be provided via the <see cref="E:DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider.GetErrorIcon"/> event.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.DXErrorProvider.ErrorType.User7">
            <summary>
                <para>A user-defined icon to be provided via the <see cref="E:DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider.GetErrorIcon"/> event.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.DXErrorProvider.ErrorType.User8">
            <summary>
                <para>A user-defined icon to be provided via the <see cref="E:DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider.GetErrorIcon"/> event.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.DXErrorProvider.ErrorType.User9">
            <summary>
                <para>A user-defined icon to be provided via the <see cref="E:DevExpress.XtraEditors.DXErrorProvider.DXErrorProvider.GetErrorIcon"/> event.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.DXErrorProvider.ErrorType.Warning">
            <summary>
                <para>The 'Warning' icon: 
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraSpellChecker.SpellCheckOperation">

            <summary>
                <para>Lists implemented spell checker operations.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraSpellChecker.SpellCheckOperation.AddToDictionary">
            <summary>
                <para>Replaces a word in the text with the user input, and adds the replacement to the custom dictionary.


</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraSpellChecker.SpellCheckOperation.Cancel">
            <summary>
                <para>Stops the spelling checker operation, resets the checking strategy and closes the spelling form. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraSpellChecker.SpellCheckOperation.Change">
            <summary>
                <para>Replaces the current word in the text being checked with the suggested word.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraSpellChecker.SpellCheckOperation.ChangeAll">
            <summary>
                <para>Replaces all occurences of the current word in the text being checked with the suggested word.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraSpellChecker.SpellCheckOperation.Custom">
            <summary>
                <para>This enumeration member is intended for internal use only.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraSpellChecker.SpellCheckOperation.Delete">
            <summary>
                <para>Deletes the current word from the text.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraSpellChecker.SpellCheckOperation.Ignore">
            <summary>
                <para>Disregards the current word and proceeds to the next one.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraSpellChecker.SpellCheckOperation.IgnoreAll">
            <summary>
                <para>Disregards the current word and all its occurences in the text.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraSpellChecker.SpellCheckOperation.None">
            <summary>
                <para>This enumeration member is intended for internal use only. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraSpellChecker.SpellCheckOperation.Options">
            <summary>
                <para>Invokes the spelling options form. After closing the form, re-checks the text with new spelling options starting with the last checked word.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraSpellChecker.SpellCheckOperation.Recheck">
            <summary>
                <para>Forces the spell checker to check the last checked word again.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraSpellChecker.SpellCheckOperation.SilentChange">
            <summary>
                <para>Replaces the current word in the text being checked with the suggested word. For internal use.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraSpellChecker.SpellCheckOperation.SilentIgnore">
            <summary>
                <para>Disregards the current word and proceeds to the next one. For internal use.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraSpellChecker.SpellCheckOperation.Undo">
            <summary>
                <para>Cancels the effect of the last operation, restoring the text to its previous state.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpo.DB.SortingColumn">

            <summary>
                <para>Represents a sorting column in a query.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.SortingColumn.#ctor(System.String,System.String,DevExpress.Xpo.DB.SortingDirection)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.SortingColumn"/> class with the specified settings.
</para>
            </summary>
            <param name="columnName">
		A <see cref="T:System.String"/> value that specifies the name of the sorted column in a query.

            </param>
            <param name="nodeAlias">
		A <see cref="T:System.String"/> value that specifies the alias name of the table in the query.

            </param>
            <param name="direction">
		A <see cref="T:DevExpress.Xpo.DB.SortingDirection"/> enumeration value which specifies the column's sort order. This value is assigned to the <see cref="P:DevExpress.Xpo.DB.SortingColumn.Direction"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.SortingColumn.#ctor(DevExpress.Data.Filtering.CriteriaOperator,DevExpress.Xpo.DB.SortingDirection)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.SortingColumn"/> class with the specified property and sort direction.
</para>
            </summary>
            <param name="property">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant representing the query operand that identifies the sorted column in a query. This value is assigned to the <see cref="P:DevExpress.Xpo.DB.SortingColumn.Property"/> property.

            </param>
            <param name="direction">
		A <see cref="T:DevExpress.Xpo.DB.SortingDirection"/> enumeration value which specifies the column's sort order. This value is assigned to the <see cref="P:DevExpress.Xpo.DB.SortingColumn.Direction"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.SortingColumn.#ctor">
            <summary>
                <para>Initializes a new <see cref="T:DevExpress.Xpo.DB.SortingColumn"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpo.DB.SortingColumn.Direction">
            <summary>
                <para>Gets or sets the column's sort order.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpo.DB.SortingDirection"/> enumeration value which specifies the column's sort order.
</value>


        </member>
        <member name="M:DevExpress.Xpo.DB.SortingColumn.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		A SortingColumn object to compare with the current object.

            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.

</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.SortingColumn.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current SortingColumn object.

</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="P:DevExpress.Xpo.DB.SortingColumn.Property">
            <summary>
                <para>Gets or set the query operand that identifies the sorted column in a query.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant representing the query operand that identifies the sorted column in a query.
</value>


        </member>
        <member name="T:DevExpress.XtraPrinting.VerticalContentSplitting">

            <summary>
                <para>Specifies how content bricks are split if they are partially positioned outside the right page margin.

</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraPrinting.VerticalContentSplitting.Exact">
            <summary>
                <para>In this case all content bricks, which are outside the right page margin, will be split across two pages in place of a right margin line.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraPrinting.VerticalContentSplitting.Smart">
            <summary>
                <para>In this case all content bricks, which are outside the right page margin, will be entirely moved to the next page, so the appearance will be more professional.


</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpo.DB.QuerySortingCollection">

            <summary>
                <para>Represents a collection of <see cref="T:DevExpress.Xpo.DB.SortingColumn"/> objects.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.QuerySortingCollection.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.QuerySortingCollection"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpo.DB.QuerySortingCollection.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		A QuerySortingCollection object to compare with the current object.

            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.

</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.QuerySortingCollection.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current QuerySortingCollection object.

</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="T:DevExpress.Xpo.DB.QueryParameterCollection">

            <summary>
                <para>Represents a collection of <see cref="T:DevExpress.Data.Filtering.OperandValue"/> objects.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.QueryParameterCollection.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.QueryParameterCollection"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpo.DB.QueryParameterCollection.#ctor(DevExpress.Data.Filtering.OperandValue[])">
            <summary>
                <para>Initializes and populates a new instance of the <see cref="T:DevExpress.Xpo.DB.QueryParameterCollection"/> class.
</para>
            </summary>
            <param name="parameters">
		An array of <see cref="T:DevExpress.Data.Filtering.OperandValue"/> objects that are added to the collection.

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.QueryParameterCollection.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		A QueryParameterCollection object to compare with the current object.

            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.

</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.QueryParameterCollection.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current QueryParameterCollection object.

</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="T:DevExpress.Xpo.DB.QueryOperandCollection">

            <summary>
                <para>Represents a collection of <see cref="T:DevExpress.Xpo.DB.QueryOperand"/> objects.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.QueryOperandCollection.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.QueryOperandCollection"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpo.DB.QueryOperandCollection.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		A QueryOperandCollection object to compare with the current object.

            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.

</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.QueryOperandCollection.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current QueryOperandCollection object.

</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="T:DevExpress.Xpo.DB.QueryOperand">

            <summary>
                <para>Represents a query operand.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.QueryOperand.#ctor(System.String,System.String)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.QueryOperand"/> class with the specified column name and alias name.
</para>
            </summary>
            <param name="columnName">
		A <see cref="T:System.String"/> value that specifies the column's name. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.QueryOperand.ColumnName"/> field.

            </param>
            <param name="nodeAlias">
		A <see cref="T:System.String"/> value that specifies the alias name of the table in a query. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.QueryOperand.NodeAlias"/> field.

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.QueryOperand.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.QueryOperand"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpo.DB.QueryOperand.#ctor(DevExpress.Xpo.DB.DBColumn,System.String)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.QueryOperand"/> class with the specified column and alias name.
</para>
            </summary>
            <param name="column">
		A <see cref="T:DevExpress.Xpo.DB.DBColumn"/> object representing the column that the query operand corresponds to. The name of this column is assigned to the <see cref="F:DevExpress.Xpo.DB.QueryOperand.ColumnName"/> property.

            </param>
            <param name="nodeAlias">
		A <see cref="T:System.String"/> value that specifies the alias name of the table in a query. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.QueryOperand.NodeAlias"/> field.

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.QueryOperand.#ctor(System.String,System.String,DevExpress.Xpo.DB.DBColumnType)">
            <summary>
                <para>Initializes a new instance of the QueryOperand class with the specified settings.
</para>
            </summary>
            <param name="columnName">
		A <see cref="T:System.String"/> value that specifies the column's name. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.QueryOperand.ColumnName"/> field.

            </param>
            <param name="nodeAlias">
		A <see cref="T:System.String"/> value that specifies the alias name of the table in a query. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.QueryOperand.NodeAlias"/> field.

            </param>
            <param name="columnType">
		@nbsp

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.QueryOperand.Accept(DevExpress.Data.Filtering.ICriteriaVisitor)">
            <summary>
                <para>Invokes an appropriate overload of the visitor's Visit method.
</para>
            </summary>
            <param name="visitor">
		A visitor that implements the <see cref="T:DevExpress.Data.Filtering.ICriteriaVisitor"/> interface.

            </param>
            <returns>An object returned by the specified visitor's Visit method.
</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.QueryOperand.Clone">
            <summary>
                <para>Creates a copy of the current QueryOperand instance.
</para>
            </summary>
            <returns>A QueryOperand object which represents an exact copy of the current object.
</returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.QueryOperand.ColumnName">
            <summary>
                <para>Specifies the column's name
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.QueryOperand.ColumnType">
            <summary>
                <para>The column type.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.QueryOperand.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		A QueryOperand object to compare with the current object.

            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.

</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.QueryOperand.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current QueryOperand object.

</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.QueryOperand.NodeAlias">
            <summary>
                <para>Specifies the alias name of the table in a query.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpo.DB.JoinNodeCollection">

            <summary>
                <para>Represents a collection of <see cref="T:DevExpress.Xpo.DB.JoinNode"/> objects.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.JoinNodeCollection.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.JoinNodeCollection"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpo.DB.JoinNodeCollection.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		A JoinNodeCollection object to compare with the current object.

            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.

</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.JoinNodeCollection.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current JoinNodeCollection object.

</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.JoinNodeCollection.ToString">
            <summary>
                <para>Returns a string that represents the current object.
</para>
            </summary>
            <returns>A <see cref="T:System.String"/> that represents the current JoinNodeCollection object.
</returns>


        </member>
        <member name="T:DevExpress.Xpo.DB.JoinNode">

            <summary>
                <para>Serves as a base for the <see cref="T:DevExpress.Xpo.DB.BaseStatement"/> class.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.JoinNode.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.JoinNode"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpo.DB.JoinNode.#ctor(DevExpress.Xpo.DB.DBTable,System.String,DevExpress.Xpo.DB.JoinType)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.JoinNode"/> class with default settings.
</para>
            </summary>
            <param name="table">
		A <see cref="T:DevExpress.Xpo.DB.DBTable"/> object that represent the table.

            </param>
            <param name="alias">
		A <see cref="T:System.String"/> value that identifies the table. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.JoinNode.Alias"/> property.

            </param>
            <param name="type">
		A <see cref="T:DevExpress.Xpo.DB.JoinType"/> enumeration value that specifies the join's type. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.JoinNode.Type"/> property.

            </param>


        </member>
        <member name="F:DevExpress.Xpo.DB.JoinNode.Alias">
            <summary>
                <para>A <see cref="T:System.String"/> value that identifies the table.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.JoinNode.Condition">
            <summary>
                <para>Specifies the criteria expression.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.JoinNode.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		A JoinNode object to compare with the current object.

            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.

</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.JoinNode.GetColumn(System.String)">
            <summary>
                <para>Returns the column with the specified name.
</para>
            </summary>
            <param name="name">
		A <see cref="T:System.String"/> value that specifies the column's name.

            </param>
            <returns>A <see cref="T:DevExpress.Xpo.DB.DBColumn"/> object that represents the table column.
</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.JoinNode.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current JoinNode object.

</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.JoinNode.SubNodes">
            <summary>
                <para>Provides access to the collection of sub nodes that is represented by a <see cref="T:DevExpress.Xpo.DB.JoinNodeCollection"/> object.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.JoinNode.TableName">
            <summary>
                <para>Specifies the table's name. This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.JoinNode.ToString">
            <summary>
                <para>Returns a string that represents the current object.
</para>
            </summary>
            <returns>A <see cref="T:System.String"/> that represents the current JoinNode object.
</returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.JoinNode.Type">
            <summary>
                <para>A <see cref="T:DevExpress.Xpo.DB.JoinType"/> enumeration value that specifies the join's type.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpo.DB.DBTableMultiColumnGadget">

            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpo.DB.DBTableMultiColumnGadget.Columns">
            <summary>
                <para>A collection of strings that specify the columns that function as primary keys for a table. This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <returns></returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.DBTableMultiColumnGadget.Name">
            <summary>
                <para>This member supports the XPO Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <returns></returns>


        </member>
        <member name="T:DevExpress.Xpo.DB.DBTable">

            <summary>
                <para>Represents a table that stores a persistent object's data.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.DBTable.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.DBTable"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpo.DB.DBTable.#ctor(System.String)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.DBTable"/> class with the specified name.
</para>
            </summary>
            <param name="name">
		A <see cref="T:System.String"/> value that specifies the table's name. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.DBTable.Name"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.DBTable.AddColumn(DevExpress.Xpo.DB.DBColumn)">
            <summary>
                <para>Appends a column to the <see cref="P:DevExpress.Xpo.DB.DBTable.Columns"/> collection.
</para>
            </summary>
            <param name="column">
		A <see cref="T:DevExpress.Xpo.DB.DBColumn"/> object that represents the column to be added to the collection.

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.DBTable.AddForeignKey(DevExpress.Xpo.DB.DBForeignKey)">
            <summary>
                <para>Appends a <see cref="T:DevExpress.Xpo.DB.DBForeignKey"/> object to the <see cref="P:DevExpress.Xpo.DB.DBTable.ForeignKeys"/> collection.
</para>
            </summary>
            <param name="fk">
		A <see cref="T:DevExpress.Xpo.DB.DBForeignKey"/> object to be added to the collection.

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.DBTable.AddIndex(DevExpress.Xpo.DB.DBIndex)">
            <summary>
                <para>Appends a <see cref="T:DevExpress.Xpo.DB.DBIndex"/> object to the <see cref="P:DevExpress.Xpo.DB.DBTable.Indexes"/> collection.
</para>
            </summary>
            <param name="index">
		A <see cref="T:DevExpress.Xpo.DB.DBIndex"/> object to be added to the collection.

            </param>


        </member>
        <member name="P:DevExpress.Xpo.DB.DBTable.Columns">
            <summary>
                <para>Provedes access to the current DBTable's column collection.
</para>
            </summary>
            <value>A list of <see cref="T:DevExpress.Xpo.DB.DBColumn"/> objects that represent the columns defined in the current <b>DBTable</b>.
</value>


        </member>
        <member name="M:DevExpress.Xpo.DB.DBTable.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		A DBTable object to compare with the current object.

            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.

</returns>


        </member>
        <member name="P:DevExpress.Xpo.DB.DBTable.ForeignKeys">
            <summary>
                <para>Provedes access to the current DBTable's foreign keys collection.
</para>
            </summary>
            <value>A list of <see cref="T:DevExpress.Xpo.DB.DBForeignKey"/> objects that represent the foreign keys defined in the current <b>DBTable</b>.
</value>


        </member>
        <member name="M:DevExpress.Xpo.DB.DBTable.GetColumn(System.String)">
            <summary>
                <para>Creates a new column with the specified name and appends it to the <see cref="P:DevExpress.Xpo.DB.DBTable.Columns"/> collection.
</para>
            </summary>
            <param name="columnName">
		A <see cref="T:System.String"/> value that specifies the column's name. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.DBColumn.Name"/> property.

            </param>
            <returns>A <see cref="T:DevExpress.Xpo.DB.DBColumn"/> object that represents the new column.
</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.DBTable.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current DBTable object.

</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="P:DevExpress.Xpo.DB.DBTable.Indexes">
            <summary>
                <para>Provedes access to the current DBTable's indexes collection.
</para>
            </summary>
            <value>A list of <see cref="T:DevExpress.Xpo.DB.DBIndex"/> objects that represent the indexes defined for the current <b>DBTable</b>.
</value>


        </member>
        <member name="M:DevExpress.Xpo.DB.DBTable.IsForeignKeyIncluded(DevExpress.Xpo.DB.DBForeignKey)">
            <summary>
                <para>Indicates whether the <see cref="P:DevExpress.Xpo.DB.DBTable.ForeignKeys"/> collection contains the specified <see cref="T:DevExpress.Xpo.DB.DBForeignKey"/> object.
</para>
            </summary>
            <param name="fk">
		A <see cref="T:DevExpress.Xpo.DB.DBForeignKey"/> object to locate in the collection.

            </param>
            <returns><b>true</b> if the collection contains the specified object; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.DBTable.IsIndexIncluded(DevExpress.Xpo.DB.DBIndex)">
            <summary>
                <para>Indicates whether the <see cref="P:DevExpress.Xpo.DB.DBTable.Indexes"/> collection contains the specified <see cref="T:DevExpress.Xpo.DB.DBIndex"/> object.
</para>
            </summary>
            <param name="index">
		A <see cref="T:DevExpress.Xpo.DB.DBIndex"/> object to locate in the collection.

            </param>
            <returns><b>true</b> if the collection contains the specified object; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.DBTable.IsView">
            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <returns>@nbsp
</returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.DBTable.Name">
            <summary>
                <para>Specifies the table's name.
</para>
            </summary>
            <returns>A <see cref="T:System.String"/> value that specifies the table's name.
</returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.DBTable.PrimaryKey">
            <summary>
                <para>Specifies a <see cref="T:DevExpress.Xpo.DB.DBPrimaryKey"/> object that represents a primary keys for the table. The primary key must be unique to identify the record in the table. It's also possible to have a table with a primary key made up of two or more columns.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Data.IDataDictionary">

            <summary>
                <para>Represents the interface which provides methods that return custom names for the Field List items.


</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.IDataDictionary.GetDataSourceDisplayName">
            <summary>
                <para>If implemented by a class, returns the name displayed for a datasource in a Field List.
</para>
            </summary>
            <returns>A <see cref="T:System.String"/> value specifying the datasource's name.
</returns>


        </member>
        <member name="M:DevExpress.Data.IDataDictionary.GetObjectDisplayName(System.String)">
            <summary>
                <para>If implemented by a class, returns the name displayed in a Field List for the name of a table or column item.

</para>
            </summary>
            <param name="dataMember">
		A <see cref="T:System.String"/> value specifying the data member name of the item whose displayed name in the Field List is to be obtained. 

            </param>
            <returns>A <see cref="T:System.String"/> value specifying the name of the <b>dataMember</b>.
</returns>


        </member>
        <member name="T:DevExpress.Xpo.DB.DBPrimaryKey">

            <summary>
                <para>Represents the primary key for a table.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.DBPrimaryKey.#ctor(System.Collections.ICollection)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.DBPrimaryKey"/> class with the specified columns that function as primary keys for a table.
</para>
            </summary>
            <param name="columns">
		A collection of strings that identify columns that function as primary keys for a table. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.DBTableMultiColumnGadget.Columns"/> property.


            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.DBPrimaryKey.#ctor(System.String,System.Collections.ICollection)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.DBPrimaryKey"/> class with the specified name and columns that function as primary keys for a table.
</para>
            </summary>
            <param name="name">
		A <see cref="T:System.String"/> value that is assigned to the <see cref="F:DevExpress.Xpo.DB.DBTableMultiColumnGadget.Name"/> property.

            </param>
            <param name="columns">
		A collection of strings that identify columns that function as primary keys for a table. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.DBTableMultiColumnGadget.Columns"/> property.


            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.DBPrimaryKey.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.DBPrimaryKey"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpo.DB.DBIndex">

            <summary>
                <para>Represents an index for a table.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.DBIndex.#ctor(System.String,System.Collections.ICollection,System.Boolean)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.DBIndex"/> class with the specified settings.
</para>
            </summary>
            <param name="name">
		A <see cref="T:System.String"/> value that is assigned to the <see cref="F:DevExpress.Xpo.DB.DBTableMultiColumnGadget.Name"/> property.

            </param>
            <param name="columns">
		A collection of strings that identify columns that function as primary keys for a table. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.DBTableMultiColumnGadget.Columns"/> property.


            </param>
            <param name="isUnique">
		<b>true</b> if the index must be unique; otherwise, <b>false</b>. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.DBIndex.IsUnique"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.DBIndex.#ctor(System.Collections.ICollection,System.Boolean)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.DBIndex"/> class with the specified settings.
</para>
            </summary>
            <param name="columns">
		A collection of strings that identify the columns that function as primary keys for a table. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.DBTableMultiColumnGadget.Columns"/> property.


            </param>
            <param name="isUnique">
		<b>true</b> if the index must be unique; otherwise, <b>false</b>. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.DBIndex.IsUnique"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.DBIndex.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.DBIndex"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpo.DB.DBIndex.IsUnique">
            <summary>
                <para><b>true</b> if the index must be unique; otherwise, <b>false</b>. If the <b>IsUnique</b> field is set to <b>true</b>, an exception will be thrown when assigning an index that isn't unique.

</para>
            </summary>
            <returns></returns>


        </member>
        <member name="T:DevExpress.Xpo.DB.DBForeignKey">

            <summary>
                <para>Represents the foreign key for a table.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.DBForeignKey.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.DBForeignKey"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpo.DB.DBForeignKey.#ctor(System.Collections.ICollection,System.String,System.Collections.Specialized.StringCollection)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.DBForeignKey"/> class with the specified settings.
</para>
            </summary>
            <param name="columns">
		A collection of strings that specify the names of columns that function as foreign keys for a table. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.DBTableMultiColumnGadget.Columns"/> property.

            </param>
            <param name="primaryKeyTable">
		A <see cref="T:System.String"/> value that specifies the name of a primary key table. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.DBForeignKey.PrimaryKeyTable"/> field.

            </param>
            <param name="primaryKeyTableKeyColumns">
		A collection of strings that specify the names of columns that function as primary keys for a table. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.DBForeignKey.PrimaryKeyTableKeyColumns"/> field.

            </param>


        </member>
        <member name="F:DevExpress.Xpo.DB.DBForeignKey.PrimaryKeyTable">
            <summary>
                <para>Specifies the name of a primary key table.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.DBForeignKey.PrimaryKeyTableKeyColumns">
            <summary>
                <para>A collection of strings that specify the names of columns that function as primary keys for a table.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpo.DB.DBColumn">

            <summary>
                <para>Represents the schema of a column in a <see cref="T:DevExpress.Xpo.DB.DBTable"/>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.DBColumn.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.DBColumn"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpo.DB.DBColumn.#ctor(System.String,System.Boolean,System.String,System.Int32,DevExpress.Xpo.DB.DBColumnType)">
            <summary>
                <para>Initializes a new instance of the DBColumn class with the specified settings.
</para>
            </summary>
            <param name="name">
		@nbsp

            </param>
            <param name="isKey">
		@nbsp

            </param>
            <param name="dBTypeName">
		@nbsp

            </param>
            <param name="size">
		@nbsp

            </param>
            <param name="type">
		@nbsp

            </param>


        </member>
        <member name="F:DevExpress.Xpo.DB.DBColumn.ColumnType">
            <summary>
                <para>Specifies the column's type.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.DBColumn.DBTypeName">
            <summary>
                <para>Specifies the name of the column data type.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.DBColumn.GetColumnType(System.Type)">
            <summary>
                <para>Gets the column type of the specified runtime type.
</para>
            </summary>
            <param name="type">
		A <see cref="T:System.Type"/> object that represents the exact runtime type.

            </param>
            <returns>A <see cref="T:DevExpress.Xpo.DB.DBColumnType"/> enumeration value that corresponds to the specified runtime type.
</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.DBColumn.GetColumnType(System.Type,System.Boolean)">
            <summary>
                <para>Gets the column type of the specified runtime type.
</para>
            </summary>
            <param name="type">
		A <see cref="T:System.Type"/> object that represents the exact runtime type.

            </param>
            <param name="supressExceptionOnUnknown">
		<b>true</b> to return the <see cref="F:DevExpress.Xpo.DB.DBColumnType.Unknown"/> value if the specified type cannot be identified; <b>false</b> to raise the <see cref="T:DevExpress.Xpo.DB.Exceptions.PropertyTypeMappingMissingException"/> exception.

            </param>
            <returns>A <see cref="T:DevExpress.Xpo.DB.DBColumnType"/> enumeration value that corresponds to the specified runtime type.
</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.DBColumn.GetType(DevExpress.Xpo.DB.DBColumnType)">
            <summary>
                <para>Gets the runtime type of the specified column type.
</para>
            </summary>
            <param name="type">
		One of the <see cref="T:DevExpress.Xpo.DB.DBColumnType"/> enumeration values.

            </param>
            <returns>A <see cref="T:System.Type"/> object that represents the exact runtime type of the specified column type.
</returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.DBColumn.IsIdentity">
            <summary>
                <para><b>true</b> if the value of the column increments automatically; otherwise, <b>false</b>. The default value is <b>false</b>.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.DBColumn.IsKey">
            <summary>
                <para><b>true</b> if the column is the key column; otherwise, <b>false</b>.
</para>
            </summary>
            <returns></returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.DBColumn.IsStorableType(DevExpress.Xpo.DB.DBColumnType)">
            <summary>
                <para>Indicates whether values of the specified column type can be saved to a database.
</para>
            </summary>
            <param name="type">
		One of the <see cref="T:DevExpress.Xpo.DB.DBColumnType"/> enumeration values.

            </param>
            <returns><b>true</b> if values of the specified column type can be saved to a database; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.DBColumn.IsStorableType(System.Type)">
            <summary>
                <para>Indicates whether values of the specified runtime type can be saved to a database.
</para>
            </summary>
            <param name="type">
		A <see cref="T:System.Type"/> object that represents the exact runtime type.

            </param>
            <returns><b>true</b> if values of the specified type can be saved to a database; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.DBColumn.Name">
            <summary>
                <para>Specifies the column's name.
</para>
            </summary>
            <returns>@nbsp
</returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.DBColumn.Size">
            <summary>
                <para>The maximum length of a text column. This is ignored for non-text columns.

</para>
            </summary>
            <returns></returns>


        </member>
        <member name="T:DevExpress.Data.Filtering.CriteriaOperatorCollection">

            <summary>
                <para>Represents a collection of <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> objects.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperatorCollection.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.CriteriaOperatorCollection"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperatorCollection.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		A CriteriaOperatorCollection object to compare with the current object.

            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.

</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperatorCollection.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current CriteriaOperatorCollection object.

</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperatorCollection.ToString">
            <summary>
                <para>Returns a string that represents the current object.
</para>
            </summary>
            <returns>A <see cref="T:System.String"/> that represents the current CriteriaOperatorCollection object.
</returns>


        </member>
        <member name="T:DevExpress.Xpo.DB.UpdateSchemaResult">

            <summary>
                <para>Lists the values that specify the result of the data store schema update operation.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpo.DB.UpdateSchemaResult.FirstTableNotExists">
            <summary>
                <para>Indicates that the table that corresponds to the first item doesn't exist in the data store.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpo.DB.UpdateSchemaResult.SchemaExists">
            <summary>
                <para>Indicates that the database schema exists.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpo.DB.SortingDirection">

            <summary>
                <para>Lists the values that specify the sort order.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpo.DB.SortingDirection.Ascending">
            <summary>
                <para>A property (column) is sorted in ascending order.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpo.DB.SortingDirection.Descending">
            <summary>
                <para>A property (column) is sorted in descending order.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Data.SelectionChangedEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraGrid.Views.Base.ColumnView.SelectionChanged"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.SelectionChangedEventHandler.Invoke(System.Object,DevExpress.Data.SelectionChangedEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraGrid.Views.Base.ColumnView.SelectionChanged"/> event.
</para>
            </summary>
            <param name="sender">
		The event sender.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Data.SelectionChangedEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Xpo.DB.JoinType">

            <summary>
                <para>Lists the values that specify the join type.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpo.DB.JoinType.Inner">
            <summary>
                <para>Represents an inner join. Inner joins (also known as equijoins) are used to combine information from two or more tables.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpo.DB.JoinType.LeftOuter">
            <summary>
                <para>Includes all the records in which the linked field value in both tables is an exact match and every record in the primary for which there is no match.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpo.DB.AutoCreateOption">

            <summary>
                <para>Lists the values that specify the action which is performed when a session is connected to a data store.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpo.DB.AutoCreateOption.DatabaseAndSchema">
            <summary>
                <para>A session will try to automatically create the database if it doesn't exist. It will also create or update the database schema for any persistent objects currently being used.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpo.DB.AutoCreateOption.None">
            <summary>
                <para>A session will not automatically create the database or update database schema.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpo.DB.AutoCreateOption.SchemaAlreadyExists">
            <summary>
                <para>A session will never update the database schema.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpo.DB.AutoCreateOption.SchemaOnly">
            <summary>
                <para>A session will automatically create or update the database schema for the persistent objects currently being used. If the database doesn't exist, it won't be created by a session.

</para>
            </summary>


        </member>
        <member name="T:DevExpress.Data.Filtering.Exceptions.InvalidPropertyPathException">

            <summary>
                <para>An exception that is thrown when a specific property that is used in a specific expression cannot be resolved.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.Filtering.Exceptions.InvalidPropertyPathException.#ctor(System.String)">
            <summary>
                <para>Initializes a new instance of the InvalidPropertyPathException class.
</para>
            </summary>
            <param name="messageText">
		A string that specifies the error description.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.Exceptions.InvalidPropertyPathException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
                <para>Sets the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> with information about the exception.
</para>
            </summary>
            <param name="info">
		The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.

            </param>
            <param name="context">
		The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.

            </param>


        </member>
        <member name="T:DevExpress.Data.Filtering.Exceptions.CriteriaParserException">

            <summary>
                <para>An exception that is thrown when a string that represents criteria cannot be parsed.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.Filtering.Exceptions.CriteriaParserException.#ctor(System.String)">
            <summary>
                <para>Initializes a new instance of the CriteriaParserException class.
</para>
            </summary>
            <param name="explanation">
		A string that specifies the error description.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.Exceptions.CriteriaParserException.#ctor(System.String,System.Int32,System.Int32)">
            <summary>
                <para>Initializes a new instance of the CriteriaParserException class.
</para>
            </summary>
            <param name="explanation">
		A string that specifies the error description.

            </param>
            <param name="line">
		A value of the <b>CriteriaLexer.Line</b> property that specifies the line where an exception occured. This value is used to initialize the <see cref="P:DevExpress.Data.Filtering.Exceptions.CriteriaParserException.Line"/> property.

            </param>
            <param name="column">
		A value of the <b>CriteriaLexer.Col</b> property that specifies the column where an exception occured. This value is used to initialize the <see cref="P:DevExpress.Data.Filtering.Exceptions.CriteriaParserException.Column"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Data.Filtering.Exceptions.CriteriaParserException.Column">
            <summary>
                <para>The value of the <b>CriteriaLexer.Col</b> property that specifies the column where an exception occured.
</para>
            </summary>
            <value>The value of the <b>CriteriaLexer.Col</b> property that specifies the column where an exception occured.
</value>


        </member>
        <member name="M:DevExpress.Data.Filtering.Exceptions.CriteriaParserException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
                <para>Sets the <b>System.Runtime.Serialization.SerializationInfo</b> with information about the exception. 
</para>
            </summary>
            <param name="info">
		The <b>System.Runtime.Serialization.SerializationInfo</b> that holds the serialized object data about the exception being thrown. 

            </param>
            <param name="context">
		The <b>System.Runtime.Serialization.StreamingContext</b> that contains contextual information about the source or destination. 

            </param>


        </member>
        <member name="P:DevExpress.Data.Filtering.Exceptions.CriteriaParserException.Line">
            <summary>
                <para>A value of the <b>CriteriaLexer.Line</b> property that specifies the line where an exception occured.
</para>
            </summary>
            <value>A value of the <b>CriteriaLexer.Line</b> property that specifies the line where an exception occured.
</value>


        </member>
        <member name="T:DevExpress.Data.Filtering.ICriteriaVisitor">

            <summary>
                <para>For internal use only.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.Filtering.ICriteriaVisitor.Visit(DevExpress.Data.Filtering.UnaryOperator)">
            <summary>
                <para>For internal use only.

</para>
            </summary>
            <param name="theOperator">
		@nbsp

            </param>
            <returns>@nbsp
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.ICriteriaVisitor.Visit(DevExpress.Data.Filtering.BinaryOperator)">
            <summary>
                <para>For internal use only.

</para>
            </summary>
            <param name="theOperator">
		@nbsp

            </param>
            <returns>@nbsp
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.ICriteriaVisitor.Visit(DevExpress.Data.Filtering.BetweenOperator)">
            <summary>
                <para>For internal use only.

</para>
            </summary>
            <param name="theOperator">
		@nbsp

            </param>
            <returns>@nbsp
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.ICriteriaVisitor.Visit(DevExpress.Data.Filtering.InOperator)">
            <summary>
                <para>For internal use only.

</para>
            </summary>
            <param name="theOperator">
		@nbsp

            </param>
            <returns>@nbsp
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.ICriteriaVisitor.Visit(DevExpress.Data.Filtering.FunctionOperator)">
            <summary>
                <para>For internal use only.

</para>
            </summary>
            <param name="theOperator">
		@nbsp

            </param>
            <returns>@nbsp
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.ICriteriaVisitor.Visit(DevExpress.Data.Filtering.OperandValue)">
            <summary>
                <para>For internal use only.

</para>
            </summary>
            <param name="theOperand">
		@nbsp

            </param>
            <returns>@nbsp
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.ICriteriaVisitor.Visit(DevExpress.Data.Filtering.GroupOperator)">
            <summary>
                <para>For internal use only.

</para>
            </summary>
            <param name="theOperator">
		@nbsp

            </param>
            <returns>@nbsp
</returns>


        </member>
        <member name="T:DevExpress.Data.Filtering.IClientCriteriaVisitor">

            <summary>
                <para>For internal use only.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.Filtering.IClientCriteriaVisitor.Visit(DevExpress.Data.Filtering.AggregateOperand)">
            <summary>
                <para>For internal use only.

</para>
            </summary>
            <param name="theOperand">
		@nbsp

            </param>
            <returns>@nbsp
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.IClientCriteriaVisitor.Visit(DevExpress.Data.Filtering.OperandProperty)">
            <summary>
                <para>For internal use only.

</para>
            </summary>
            <param name="theOperand">
		@nbsp

            </param>
            <returns>@nbsp
</returns>


        </member>
        <member name="T:DevExpress.Data.Filtering.UnaryOperator">

            <summary>
                <para>Represents the unary operator which performs an operation on only one expression.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.Filtering.UnaryOperator.#ctor(DevExpress.Data.Filtering.UnaryOperatorType,DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.UnaryOperator"/> class with the specified operand and operator type.
</para>
            </summary>
            <param name="operatorType">
		A <see cref="T:DevExpress.Data.Filtering.UnaryOperator"/> enumeration value which specifies the type of the unary operator. This value is assigned to the <see cref="F:DevExpress.Data.Filtering.UnaryOperator.OperatorType"/> property.

            </param>
            <param name="operand">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents the operand. This value is assigned to the <see cref="F:DevExpress.Data.Filtering.UnaryOperator.Operand"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.UnaryOperator.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.UnaryOperator"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Data.Filtering.UnaryOperator.#ctor(DevExpress.Data.Filtering.UnaryOperatorType,System.String)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.UnaryOperator"/> class with the specified settings.
</para>
            </summary>
            <param name="operatorType">
		A <see cref="T:DevExpress.Data.Filtering.UnaryOperator"/> enumeration value which specifies the type of the unary operator. This value is assigned to the <see cref="F:DevExpress.Data.Filtering.UnaryOperator.OperatorType"/> property.

            </param>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the property's name.


            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.UnaryOperator.Accept(DevExpress.Data.Filtering.ICriteriaVisitor)">
            <summary>
                <para>Invokes an appropriate overload of the visitor's Visit method.
</para>
            </summary>
            <param name="visitor">
		A visitor that implements the <see cref="T:DevExpress.Data.Filtering.ICriteriaVisitor"/> interface.

            </param>
            <returns>An object returned by the specified visitor's Visit method.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.UnaryOperator.Clone">
            <summary>
                <para>Creates a copy of the current UnaryOperator instance.
</para>
            </summary>
            <returns>A UnaryOperator object which represents an exact copy of the current object.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.UnaryOperator.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		An UnaryOperator object to compare with the current object.


            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.

</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.UnaryOperator.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current UnaryOperator object.

</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="F:DevExpress.Data.Filtering.UnaryOperator.Operand">
            <summary>
                <para>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents the operand.
</para>
            </summary>
            <returns></returns>


        </member>
        <member name="F:DevExpress.Data.Filtering.UnaryOperator.OperatorType">
            <summary>
                <para>An <see cref="T:DevExpress.Data.Filtering.UnaryOperatorType"/> enumeration value which specifies the type of the unary operator.

</para>
            </summary>
            <returns>
</returns>


        </member>
        <member name="T:DevExpress.Data.Filtering.OperandValue">

            <summary>
                <para>Represents a value operand in the criteria expression.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.Filtering.OperandValue.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.OperandValue"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Data.Filtering.OperandValue.#ctor(System.Object)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.OperandValue"/> class with the specified value.
</para>
            </summary>
            <param name="value">
		The value for the operand. This value is assigned to the <see cref="P:DevExpress.Data.Filtering.OperandValue.Value"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.OperandValue.Accept(DevExpress.Data.Filtering.ICriteriaVisitor)">
            <summary>
                <para>Invokes an appropriate overload of the visitor's Visit method.
</para>
            </summary>
            <param name="visitor">
		A visitor that implements the <see cref="T:DevExpress.Data.Filtering.ICriteriaVisitor"/> interface.

            </param>
            <returns>An object returned by the specified visitor's Visit method.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.OperandValue.Clone">
            <summary>
                <para>Creates a copy of the current OperandValue instance.
</para>
            </summary>
            <returns>An OperandValue object which represents an exact copy of the current object.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.OperandValue.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		An OperandValue object to compare with the current object.

            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.

</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.OperandValue.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current OperandValue object.

</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="P:DevExpress.Data.Filtering.OperandValue.Value">
            <summary>
                <para>Gets or sets a value for the operand.
</para>
            </summary>
            <value>The value for the operand.
</value>


        </member>
        <member name="P:DevExpress.Data.Filtering.OperandValue.XmlValue">
            <summary>
                <para>Gets or sets the current operand's formatted value.
</para>
            </summary>
            <value>An object that represents the current operand's formatted value.
</value>


        </member>
        <member name="T:DevExpress.Data.Filtering.OperandProperty">

            <summary>
                <para>Represents an object property operand in the criteria expression.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.Filtering.OperandProperty.#ctor(System.String)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.OperandProperty"/> class with the specified property name.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value that specifies the name of the property. It can be a path through object relationships (e.g.  "Employer.DefaultAddress.City"). This value is case-sensitive. and will be assigned to the <see cref="P:DevExpress.Data.Filtering.OperandProperty.PropertyName"/> property.


            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.OperandProperty.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.OperandProperty"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Data.Filtering.OperandProperty.Accept(DevExpress.Data.Filtering.ICriteriaVisitor)">
            <summary>
                <para>Invokes an appropriate overload of the visitor's Visit method.
</para>
            </summary>
            <param name="visitor">
		A visitor that implements the <see cref="T:DevExpress.Data.Filtering.ICriteriaVisitor"/> interface.

            </param>
            <returns>An object returned by the specified visitor's Visit method.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.OperandProperty.Clone">
            <summary>
                <para>Creates a copy of the current OperandProperty instance.
</para>
            </summary>
            <returns>An OperandProperty object which represents an exact copy of the current object.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.OperandProperty.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		An OperandProperty object to compare with the current object.


            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.

</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.OperandProperty.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current OperandProperty object.

</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="P:DevExpress.Data.Filtering.OperandProperty.Item(DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>For internal use only.


</para>
            </summary>
            <param name="condition">
		@nbsp

            </param>
            <value>@nbsp
</value>


        </member>
        <member name="P:DevExpress.Data.Filtering.OperandProperty.PropertyName">
            <summary>
                <para>Gets or sets a property name.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the name of the property. It can be a path through object relationships (e.g.  "Employer.DefaultAddress.City"). This value is case-sensitive.
</value>


        </member>
        <member name="T:DevExpress.Data.Filtering.NullOperator">

            <summary>
                <para>Represents an operator which compares a persistent object's property value with a <b>null</b> reference (<b>Nothing</b> in Visual Basic).
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.Filtering.NullOperator.#ctor(System.String)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.NullOperator"/> class with the specified property name.
</para>
            </summary>
            <param name="operand">
		A <see cref="T:System.String"/> value which specifies the name of the property to check for <b>null</b> (<b>Nothing</b> in Visual Basic).

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.NullOperator.#ctor(DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.NullOperator"/> class with the specified criteria operand.
</para>
            </summary>
            <param name="operand">
		An <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents the criteria expression.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.NullOperator.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.NullOperator"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Data.Filtering.NotOperator">

            <summary>
                <para>Represents a logical NOT operation which is performed on the operand.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.Filtering.NotOperator.#ctor(DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.NotOperator"/> class with the specified operand.
</para>
            </summary>
            <param name="operand">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant which represents the operand that the logical inversion is applied to.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.NotOperator.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.NotOperator"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Data.Filtering.InOperator">

            <summary>
                <para>Determines whether a value matches any value in the specified list.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.Filtering.InOperator.#ctor(System.String,System.Collections.ICollection)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.InOperator"/> class with the specified settings.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the property name.

            </param>
            <param name="values">
		A collection of expressions to test for a match.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.InOperator.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.InOperator"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Data.Filtering.InOperator.#ctor(DevExpress.Data.Filtering.CriteriaOperator,DevExpress.Data.Filtering.CriteriaOperator[])">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.InOperator"/> class with the specified settings.
</para>
            </summary>
            <param name="leftOperand">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents the expression to test.

            </param>
            <param name="operands">
		An array of <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> objects that represent expressions to test for a match.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.InOperator.#ctor(DevExpress.Data.Filtering.CriteriaOperator,System.Collections.Generic.IEnumerable`1)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.InOperator"/> class.
</para>
            </summary>
            <param name="leftOperand">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents the expression to test.

            </param>
            <param name="operands">
		An array of objects that represent expressions to test for a match.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.InOperator.Accept(DevExpress.Data.Filtering.ICriteriaVisitor)">
            <summary>
                <para>Invokes an appropriate overload of the visitor's Visit method.
</para>
            </summary>
            <param name="visitor">
		A visitor that implements the <see cref="T:DevExpress.Data.Filtering.ICriteriaVisitor"/> interface.

            </param>
            <returns>An object returned by the specified visitor's Visit method.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.InOperator.Clone">
            <summary>
                <para>Creates a copy of the current InOperator instance.
</para>
            </summary>
            <returns>An InOperator object which represents an exact copy of the current object.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.InOperator.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		An InOperator object to compare with the current object.


            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.

</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.InOperator.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current InOperator object.

</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="P:DevExpress.Data.Filtering.InOperator.LeftOperand">
            <summary>
                <para>Gets or sets the expression which is tested to see if it matches any value in the <see cref="P:DevExpress.Data.Filtering.InOperator.Operands"/> list.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents the expression to test.
</value>


        </member>
        <member name="P:DevExpress.Data.Filtering.InOperator.Operands">
            <summary>
                <para>Provides access to the <see cref="T:DevExpress.Data.Filtering.CriteriaOperatorCollection"/> object that represents a collection of the operands used to construct the current InOperator.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperatorCollection"/> object that represents a collection of the operands used to construct the current InOperator.
</value>


        </member>
        <member name="T:DevExpress.Data.Filtering.GroupOperator">

            <summary>
                <para>Represents a logical expression which groups two or more operands with a logical AND or OR.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.Filtering.GroupOperator.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.GroupOperator"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Data.Filtering.GroupOperator.#ctor(DevExpress.Data.Filtering.GroupOperatorType,DevExpress.Data.Filtering.CriteriaOperator[])">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.GroupOperator"/> class with the specified settings.
</para>
            </summary>
            <param name="type">
		A <see cref="T:DevExpress.Data.Filtering.GroupOperatorType"/> enumeration value that specifies the type of the group operator. This value is assigned to the <see cref="F:DevExpress.Data.Filtering.GroupOperator.OperatorType"/> property.

            </param>
            <param name="operands">
		An array of <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> objects that represent operands. This value is assigned to the <see cref="P:DevExpress.Data.Filtering.GroupOperator.Operands"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.GroupOperator.#ctor(DevExpress.Data.Filtering.CriteriaOperator[])">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.GroupOperator"/> class with the specified operands.
</para>
            </summary>
            <param name="operands">
		An array of the <see cref="T:DevExpress.Data.Filtering.GroupOperatorType"/> objects that represent operands. This value is assigned to the <see cref="P:DevExpress.Data.Filtering.GroupOperator.Operands"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.GroupOperator.#ctor(DevExpress.Data.Filtering.GroupOperatorType,System.Collections.Generic.IEnumerable`1)">
            <summary>
                <para>Initializes a new instance of the GroupOperator with the specified parameters.
</para>
            </summary>
            <param name="type">
		A <see cref="T:DevExpress.Data.Filtering.GroupOperatorType"/> enumeration value that specifies the type of the group operator. This value is assigned to the <see cref="F:DevExpress.Data.Filtering.GroupOperator.OperatorType"/> property.

            </param>
            <param name="operands">
		An object that implements the <b>IEnumerable</b> interface and represents a collection of <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> objects which specify the operands required by the selected function. This value is used to initialize the <see cref="P:DevExpress.Data.Filtering.FunctionOperator.Operands"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.GroupOperator.Accept(DevExpress.Data.Filtering.ICriteriaVisitor)">
            <summary>
                <para>Invokes an appropriate overload of the visitor's Visit method.
</para>
            </summary>
            <param name="visitor">
		A visitor that implements the <see cref="T:DevExpress.Data.Filtering.ICriteriaVisitor"/> interface.

            </param>
            <returns>An object returned by the specified visitor's Visit method.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.GroupOperator.Clone">
            <summary>
                <para>Creates a copy of the current GroupOperator instance.
</para>
            </summary>
            <returns>A GroupOperator object which represents an exact copy of the current object.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.GroupOperator.Combine(DevExpress.Data.Filtering.GroupOperatorType,DevExpress.Data.Filtering.CriteriaOperator,DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>Returns the expression which groups two operands with logical AND or OR.
</para>
            </summary>
            <param name="opType">
		A <see cref="T:DevExpress.Data.Filtering.GroupOperatorType"/> enumeration value that specifies the type of the group operator.

            </param>
            <param name="left">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents the first operand.

            </param>
            <param name="right">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents the second operand.

            </param>
            <returns>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object representing the expression which groups two operands.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.GroupOperator.Combine(DevExpress.Data.Filtering.GroupOperatorType,DevExpress.Data.Filtering.CriteriaOperator[])">
            <summary>
                <para>Returns the expression which groups the specified operands with logical AND or OR.
</para>
            </summary>
            <param name="opType">
		A <see cref="T:DevExpress.Data.Filtering.GroupOperatorType"/> enumeration value that specifies the type of the group operator.

            </param>
            <param name="operands">
		An array of <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> objects that represent operands.

            </param>
            <returns>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object representing the expression which groups the specified operands.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.GroupOperator.Combine(DevExpress.Data.Filtering.GroupOperatorType,System.Collections.Generic.IEnumerable`1)">
            <summary>
                <para>Returns the expression which groups the specified operands with logical AND or OR.
</para>
            </summary>
            <param name="opType">
		A <see cref="T:DevExpress.Data.Filtering.GroupOperatorType"/> enumeration value that specifies the type of the group operator.

            </param>
            <param name="operands">
		A list of operands to be grouped.

            </param>
            <returns>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object representing the expression which groups the specified operands.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.GroupOperator.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		A GroupOperator object to compare with the current object.

            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.

</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.GroupOperator.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current GroupOperator object.

</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="P:DevExpress.Data.Filtering.GroupOperator.Operands">
            <summary>
                <para>Provides access to the <see cref="T:DevExpress.Data.Filtering.CriteriaOperatorCollection"/> object that represents a collection of the operands used to construct the current GroupOperator.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperatorCollection"/> object that represents a collection of the operands used to construct the current GroupOperator.
</value>


        </member>
        <member name="F:DevExpress.Data.Filtering.GroupOperator.OperatorType">
            <summary>
                <para>Specifies the type of the group operator. The available types are enumerated in the <see cref="T:DevExpress.Data.Filtering.GroupOperatorType"/> enumeration.
</para>
            </summary>
            <returns></returns>


        </member>
        <member name="T:DevExpress.Data.Filtering.FunctionOperator">

            <summary>
                <para>Represents the function operator that can be used when constructing filter criteria.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.Filtering.FunctionOperator.#ctor">
            <summary>
                <para>Initializes a new instance of the FunctionOperator class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Data.Filtering.FunctionOperator.#ctor(DevExpress.Data.Filtering.FunctionOperatorType,DevExpress.Data.Filtering.CriteriaOperator[])">
            <summary>
                <para>Initializes a new instance of the FunctionOperator class with default settings.
</para>
            </summary>
            <param name="type">
		A <see cref="T:DevExpress.Data.Filtering.FunctionOperatorType"/> value that specifies the type of function. This value is used to initialize the <see cref="F:DevExpress.Data.Filtering.FunctionOperator.OperatorType"/> property.

            </param>
            <param name="operands">
		An array of <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> objects that specify the operands required by the selected function. This value is used to initialize the <see cref="P:DevExpress.Data.Filtering.FunctionOperator.Operands"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.FunctionOperator.#ctor(System.String,DevExpress.Data.Filtering.CriteriaOperator[])">
            <summary>
                <para>Initializes a new instance of the FunctionOperator class.
</para>
            </summary>
            <param name="customFunctionName">
		@nbsp

            </param>
            <param name="operands">
		@nbsp

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.FunctionOperator.#ctor(DevExpress.Data.Filtering.FunctionOperatorType,System.Collections.Generic.IEnumerable`1)">
            <summary>
                <para>Initializes a new instance of the FunctionOperator class with the specified parameters.
</para>
            </summary>
            <param name="type">
		A <see cref="T:DevExpress.Data.Filtering.FunctionOperatorType"/> enumeration value that specifies the type of function. This value is used to initialize the <see cref="F:DevExpress.Data.Filtering.FunctionOperator.OperatorType"/> property.

            </param>
            <param name="operands">
		An object that implements the <b>IEnumerable</b> interface and represents a collection of <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> objects which specify the operands required by the selected function. This value is used to initialize the <see cref="P:DevExpress.Data.Filtering.FunctionOperator.Operands"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.FunctionOperator.Accept(DevExpress.Data.Filtering.ICriteriaVisitor)">
            <summary>
                <para>Invokes an appropriate overload of the visitor's Visit method.
</para>
            </summary>
            <param name="visitor">
		A visitor that implements the <see cref="T:DevExpress.Data.Filtering.ICriteriaVisitor"/> interface.

            </param>
            <returns>An object returned by the specified visitor's Visit method.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.FunctionOperator.Clone">
            <summary>
                <para>Creates a copy of the current FunctionOperator instance.
</para>
            </summary>
            <returns>A FunctionOperator object which represents an exact copy of the current object.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.FunctionOperator.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		A FunctionOperator object to compare with the current object.

            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.

</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.FunctionOperator.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current FunctionOperator object.

</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="P:DevExpress.Data.Filtering.FunctionOperator.Operands">
            <summary>
                <para>Provides access to the <see cref="T:DevExpress.Data.Filtering.CriteriaOperatorCollection"/> object that represents a collection of the operands used to construct the current FunctionOperator.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperatorCollection"/> object that represents a collection of the operands used to construct the current FunctionOperator.
</value>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperator.OperatorType">
            <summary>
                <para>Represents the function type. 
<para>
The operands are specified by the <see cref="P:DevExpress.Data.Filtering.FunctionOperator.Operands"/> property and these can be initialized in the constructor. Each operand represents an instance of the <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> class descendant, the number of operands depends on the function type.
</para>
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.Data.Filtering.FunctionOperatorType"/> value that specify the function type.
</returns>


        </member>
        <member name="T:DevExpress.Data.Filtering.CriteriaOperator">

            <summary>
                <para>Provides the abstract (<b>MustInherit</b> in Visual Basic) base class for criteria operators.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.Accept(DevExpress.Data.Filtering.ICriteriaVisitor)">
            <summary>
                <para>Invokes an appropriate overload of the visitor's Visit method.
</para>
            </summary>
            <param name="visitor">
		A visitor that implements the <see cref="T:DevExpress.Data.Filtering.ICriteriaVisitor"/> interface.

            </param>
            <returns>An object returned by the specified visitor's Visit method.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.And(DevExpress.Data.Filtering.CriteriaOperator,DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>Returns the expression which groups two operands with logical AND.
</para>
            </summary>
            <param name="left">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents the first operand.

            </param>
            <param name="right">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents the second operand.

            </param>
            <returns>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object representing the expression which groups two operands with logical AND.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.And(DevExpress.Data.Filtering.CriteriaOperator[])">
            <summary>
                <para>Returns the expression which groups the specified operands with logical AND.
</para>
            </summary>
            <param name="operands">
		An array of <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> objects that represent operands.

            </param>
            <returns>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object representing the expression which groups the specified operands with logical AND.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.And(System.Collections.Generic.IEnumerable`1)">
            <summary>
                <para>Returns the expression which groups the specified operands with logical AND.
</para>
            </summary>
            <param name="operands">
		A list of operands to be grouped.

            </param>
            <returns>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object representing the expression which groups the specified operands with logical AND.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.Clone(DevExpress.Data.Filtering.OperandProperty)">
            <summary>
                <para>Creates a copy of the specified <see cref="T:DevExpress.Data.Filtering.OperandProperty"/> object.
</para>
            </summary>
            <param name="origin">
		An <see cref="T:DevExpress.Data.Filtering.OperandProperty"/> object to clone.

            </param>
            <returns>An <see cref="T:DevExpress.Data.Filtering.OperandProperty"/> object which represents an exact copy of the specified object. <b>null</b> (<b>Nothing</b> in Visual Basic) if the specified object is a null reference.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.Clone(DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>Creates a copy of the specified <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object.
</para>
            </summary>
            <param name="origin">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object to clone.

            </param>
            <returns>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents an exact copy of the specified object. <b>null</b> (<b>Nothing</b> in Visual Basic) if the specified object is null reference.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		An object to compare with the current object.

            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current CriteriaOperator object.
</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.IsNotNull">
            <summary>
                <para>For internal use only.

</para>
            </summary>
            <returns>@nbsp
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.IsNull">
            <summary>
                <para>For internal use only.

</para>
            </summary>
            <returns>@nbsp
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.LegacyToString">
            <summary>
                <para>Returns the string representation of the criterion defined by the current CriteriaOperator.
</para>
            </summary>
            <returns>A string representation of the criterion defined by the current <b>CriteriaOperator</b>.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.LegacyToString(DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>Returns the string representation of the criterion defined by the specified CriteriaOperator.
</para>
            </summary>
            <param name="criteria">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object that represents the criterion to convert to its string representation.

            </param>
            <returns>A string representation of the criterion defined by the specified CriteriaOperator.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.Not">
            <summary>
                <para>Returns an instance of the <see cref="T:DevExpress.Data.Filtering.UnaryOperator"/> that represents the criterion resulting from the logical inversion of the criterion the current CriteriaOperator represents.
</para>
            </summary>
            <returns>An instance of the <see cref="T:DevExpress.Data.Filtering.UnaryOperator"/> that represents the criterion resulting from the logical inversion of the criterion the current CriteriaOperator represents.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.Or(DevExpress.Data.Filtering.CriteriaOperator[])">
            <summary>
                <para>Returns the expression which groups the specified operands with logical OR.
</para>
            </summary>
            <param name="operands">
		An array of <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> objects that represent operands.

            </param>
            <returns>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object representing the expression which groups the specified operands with logical OR.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.Or(DevExpress.Data.Filtering.CriteriaOperator,DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>Returns the expression which groups two operands with logical OR.
</para>
            </summary>
            <param name="left">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents the first operand.

            </param>
            <param name="right">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents the second operand.

            </param>
            <returns>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object representing the expression which groups two operands with logical OR.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.Or(System.Collections.Generic.IEnumerable`1)">
            <summary>
                <para>Returns the expression which groups the specified operands with logical OR.
</para>
            </summary>
            <param name="operands">
		A list of operands to be grouped.

            </param>
            <returns>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object representing the expression which groups the specified operands with logical OR.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.Parse(System.String,System.Object[])">
            <summary>
                <para>Converts the specified string representation of an expression to its <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> equivalent.
</para>
            </summary>
            <param name="criteria">
		A <see cref="T:System.String"/> value that represents the expression to convert.

            </param>
            <param name="parameters">
		The values that are substituted into the expression in place of question mark characters. These parameters can be omitted.


            </param>
            <returns>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> equivalent to the expression contained in <i>criteria</i>.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.Parse(System.String,DevExpress.Data.Filtering.OperandValue[])">
            <summary>
                <para>Converts the specified string representation of an expression to its <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> equivalent.
</para>
            </summary>
            <param name="stringCriteria">
		A <see cref="T:System.String"/> value that represents the expression to convert.

            </param>
            <param name="criteriaParametersList">
		[out] Receives the values that are substituted into the expression in place of question mark characters. These parameters can be omitted.


            </param>
            <returns>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> equivalent to the expression contained in <i>criteria</i>.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.ParseList(System.String,DevExpress.Data.Filtering.OperandValue[])">
            <summary>
                <para>Converts the specified string representation of expressions that are divided by a semicolon into their <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> equivalents.

</para>
            </summary>
            <param name="criteriaList">
		A <see cref="T:System.String"/> value that represents the expressions to convert. The expressions are divided by a semicolon.

            </param>
            <param name="criteriaParametersList">
		[out] Receives the values that are substituted into the expression in place of question mark characters. These parameters can be omitted.


            </param>
            <returns>An array of <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> equivalents to the expressions contained in <i>criteriaList</i>.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.ParseList(System.String,System.Object[])">
            <summary>
                <para>Converts the specified string representation of expressions that are divided by a semicolon into their <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> equivalents.

</para>
            </summary>
            <param name="criteriaList">
		A <see cref="T:System.String"/> value that represents the expressions to convert. The expressions are divided by a semicolon.

            </param>
            <param name="parameters">
		The values that are substituted into the expression in place of question mark characters. These parameters can be omitted.


            </param>
            <returns>An array of <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> equivalents to the expressions contained in <i>criteriaList</i>.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.ToBasicStyleString(DevExpress.Data.Filtering.CriteriaOperator,DevExpress.Data.Filtering.OperandValue[])">
            <summary>
                <para>Converts the specified expression into its equivalent string in VB style.

</para>
            </summary>
            <param name="criteria">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object that represents the expression to convert.

            </param>
            <param name="criteriaParametersList">
		[out] Receives the values that are marked by a question mark character in the result string.


            </param>
            <returns>A string representation of the specified expression.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.ToBasicStyleString(DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>Converts the specified expression into its equivalent string in VB style.

</para>
            </summary>
            <param name="criteria">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object that represents the expression to convert.

            </param>
            <returns>A string representation of the specified expression.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.ToCStyleString(DevExpress.Data.Filtering.CriteriaOperator,DevExpress.Data.Filtering.OperandValue[])">
            <summary>
                <para>Converts the specified expression into its equivalent string in C# style.

</para>
            </summary>
            <param name="criteria">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object that represents the expression to convert.

            </param>
            <param name="criteriaParametersList">
		[out] Receives the values that are marked by a question mark character in the result string.


            </param>
            <returns>A string representation of the specified expression.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.ToCStyleString(DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>Converts the specified expression into its equivalent string in C# style.

</para>
            </summary>
            <param name="criteria">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object that represents the expression to convert.

            </param>
            <returns>A string representation of the specified expression.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.ToString">
            <summary>
                <para>Converts the current expression into its VB equivalent string.

</para>
            </summary>
            <returns>A string representation of this expression.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.ToString(DevExpress.Data.Filtering.CriteriaOperator,DevExpress.Data.Filtering.OperandValue[])">
            <summary>
                <para>Converts the specified expression into its equivalent string in VB style.

</para>
            </summary>
            <param name="criteria">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object that represents the expression to convert.

            </param>
            <param name="criteriaParametersList">
		[out] Receives the values that are marked by question mark character in the result string.

            </param>
            <returns>A string representation of the specified expression.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.ToString(DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>Converts the specified expression into its equivalent string in VB style.

</para>
            </summary>
            <param name="criteria">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object that represents the expression to convert.

            </param>
            <returns>A string representation of the specified expression.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.CriteriaOperator.TryParse(System.String,System.Object[])">
            <summary>
                <para>Tries to convert the specified string representation of an expression to its <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> equivalent.

</para>
            </summary>
            <param name="criteria">
		A <see cref="T:System.String"/> value that represents the expression to convert.

            </param>
            <param name="parameters">
		The values that are substituted into the expression in place of question mark characters. These parameters can be omitted.

            </param>
            <returns>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> equivalent to the expression contained in <i>criteria</i>.
</returns>


        </member>
        <member name="T:DevExpress.Data.Filtering.ContainsOperator">

            <summary>
                <para>Represents the contains operator that can be used when constructing filter criteria.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.Filtering.ContainsOperator.#ctor(System.String,DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.ContainsOperator"/> class with the specified property and criteria expression.
</para>
            </summary>
            <param name="collectionProperty">
		A <see cref="T:System.String"/> value which identifies the property whose values will be searched.


            </param>
            <param name="condition">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object that represents a search criteria.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.ContainsOperator.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.ContainsOperator"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Data.Filtering.ContainsOperator.#ctor(DevExpress.Data.Filtering.OperandProperty,DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.ContainsOperator"/> class with the specified property operand and criteria expression.
</para>
            </summary>
            <param name="collectionProperty">
		An <see cref="T:DevExpress.Data.Filtering.OperandProperty"/> object which represents the property operand in the criteria expression.

            </param>
            <param name="condition">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents the search criteria.


            </param>


        </member>
        <member name="T:DevExpress.Data.Filtering.BinaryOperator">

            <summary>
                <para>Represents a logical expression that consists of a comparison operation between two operands.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(DevExpress.Data.Filtering.CriteriaOperator,DevExpress.Data.Filtering.CriteriaOperator,DevExpress.Data.Filtering.BinaryOperatorType)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class with the specified left and right operands and binary operator type.
</para>
            </summary>
            <param name="opLeft">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant which represents the expression for the first operand. This value is assigned to the <see cref="P:DevExpress.Data.Filtering.BinaryOperator.LeftOperand"/> property.

            </param>
            <param name="opRight">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant which represents the expression for the second operand. This value is assigned to the <see cref="P:DevExpress.Data.Filtering.BinaryOperator.RightOperand"/> property.

            </param>
            <param name="type">
		A <see cref="T:DevExpress.Data.Filtering.BinaryOperatorType"/> enumeration value which specifies the type of the binary operator. This value is assigned to the <see cref="F:DevExpress.Data.Filtering.BinaryOperator.OperatorType"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor">
            <summary>
                <para>Initializes a new instance of the BinaryOperator class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Object)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		An <see cref="T:System.Object"/> value to compare with the values of the specified property.


            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Object,DevExpress.Data.Filtering.BinaryOperatorType)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class with the specified type of the binary operator.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.


            </param>
            <param name="value">
		An <see cref="T:System.Object"/> value to compare with the values of the specified property.

            </param>
            <param name="type">
		A <see cref="T:DevExpress.Data.Filtering.BinaryOperatorType"/> enumeration value which specifies the type of the binary operator. This value is assigned to the <see cref="F:DevExpress.Data.Filtering.BinaryOperator.OperatorType"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Double)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class which is used to compare the values of the specified property to <see cref="T:System.Double"/> values.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Single)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class which is used to compare the values of the specified property to <see cref="T:System.String"/> values.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Int32)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class which is used to compare the values of the specified property to integer values.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Decimal)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class which is used to compare the values of the specified property to the <see cref="T:System.Decimal"/> value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Boolean)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class which is used to compare the values of the specified property to the <see cref="T:System.Boolean"/> value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Byte)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class which is used to compare the values of the specified property to the <see cref="T:System.Byte"/> value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Char)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class which is used to compare the values of the specified property to the <see cref="T:System.Char"/> value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.DateTime)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class which is used to compare the values of the specified property to the <see cref="T:System.DateTime"/> value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.TimeSpan)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class which is used to compare the values of the specified property to the <see cref="T:System.TimeSpan"/> value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Byte[])">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class which is used to compare the values of the specified property to an array of <see cref="T:System.Byte"/> values.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		An array of <see cref="T:System.Byte"/> values to compare with the values of the specified property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.String)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class which is used to compare the values of the specified property to the <see cref="T:System.String"/> value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Int64)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class which is used to compare the values of the specified property to the <see cref="T:System.Int64"/> value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Int16)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class which is used to compare the values of the specified property to the <see cref="T:System.Int16"/> value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Guid)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class which is used to compare the values of the specified property to the <see cref="T:System.Guid"/> value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Double,DevExpress.Data.Filtering.BinaryOperatorType)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class with the specified type of binary operator. This binary operator is used to compare the values of the specified property to the specified <see cref="T:System.Double"/> value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>
            <param name="type">
		A <see cref="T:DevExpress.Data.Filtering.BinaryOperatorType"/> enumeration value which specifies the type of the binary operator. This value is assigned to the <see cref="F:DevExpress.Data.Filtering.BinaryOperator.OperatorType"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Single,DevExpress.Data.Filtering.BinaryOperatorType)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class with the specified type of the binary operator. This binary operator is used to compare the values of the specified property to the specified <see cref="T:System.Single"/> value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>
            <param name="type">
		A <see cref="T:DevExpress.Data.Filtering.BinaryOperatorType"/> enumeration value which specifies the type of the binary operator. This value is assigned to the <see cref="F:DevExpress.Data.Filtering.BinaryOperator.OperatorType"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Int32,DevExpress.Data.Filtering.BinaryOperatorType)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class with the specified type of binary operator. This binary operator is used to compare the values of the specified property to the specified integer value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>
            <param name="type">
		A <see cref="T:DevExpress.Data.Filtering.BinaryOperatorType"/> enumeration value which specifies the type of the binary operator. This value is assigned to the <see cref="F:DevExpress.Data.Filtering.BinaryOperator.OperatorType"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Decimal,DevExpress.Data.Filtering.BinaryOperatorType)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class with the specified type of binary operator. This binary operator is used to compare the values of the specified property to the specified <see cref="T:System.Decimal"/> value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>
            <param name="type">
		A <see cref="T:DevExpress.Data.Filtering.BinaryOperatorType"/> enumeration value which specifies the type of the binary operator. This value is assigned to the <see cref="F:DevExpress.Data.Filtering.BinaryOperator.OperatorType"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Boolean,DevExpress.Data.Filtering.BinaryOperatorType)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class with the specified type of binary operator. This binary operator is used to compare the values of the specified property to the specified <see cref="T:System.Boolean"/> value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>
            <param name="type">
		A <see cref="T:DevExpress.Data.Filtering.BinaryOperatorType"/> enumeration value which specifies the type of the binary operator. This value is assigned to the <see cref="F:DevExpress.Data.Filtering.BinaryOperator.OperatorType"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Byte,DevExpress.Data.Filtering.BinaryOperatorType)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class with the specified type of binary operator. This binary operator is used to compare the values of the specified property to the specified <see cref="T:System.Byte"/> value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>
            <param name="type">
		A <see cref="T:DevExpress.Data.Filtering.BinaryOperatorType"/> enumeration value which specifies the type of the binary operator. This value is assigned to the <see cref="F:DevExpress.Data.Filtering.BinaryOperator.OperatorType"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Char,DevExpress.Data.Filtering.BinaryOperatorType)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class with the specified type of binary operator.  This binary operator is used to compare the values of the specified property to the specified <see cref="T:System.Char"/> value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>
            <param name="type">
		A <see cref="T:DevExpress.Data.Filtering.BinaryOperatorType"/> enumeration value which specifies the type of the binary operator. This value is assigned to the <see cref="F:DevExpress.Data.Filtering.BinaryOperator.OperatorType"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.DateTime,DevExpress.Data.Filtering.BinaryOperatorType)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class with the specified type of binary operator.  This binary operator is used to compare the values of the specified property to the specified <see cref="T:System.DateTime"/> value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>
            <param name="type">
		A <see cref="T:DevExpress.Data.Filtering.BinaryOperatorType"/> enumeration value which specifies the type of the binary operator. This value is assigned to the <see cref="F:DevExpress.Data.Filtering.BinaryOperator.OperatorType"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.TimeSpan,DevExpress.Data.Filtering.BinaryOperatorType)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class with the specified type of binary operator.  This binary operator is used to compare the values of the specified property to the specified <see cref="T:System.TimeSpan"/> value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>
            <param name="type">
		A <see cref="T:DevExpress.Data.Filtering.BinaryOperatorType"/> enumeration value which specifies the type of the binary operator. This value is assigned to the <see cref="F:DevExpress.Data.Filtering.BinaryOperator.OperatorType"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Byte[],DevExpress.Data.Filtering.BinaryOperatorType)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class with the specified type of binary operator.  This binary operator is used to compare the values of the specified property to the specified <see cref="T:System.Byte"/> values.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		An array of <see cref="T:System.Byte"/> values to compare with the values of the specified property.

            </param>
            <param name="type">
		A <see cref="T:DevExpress.Data.Filtering.BinaryOperatorType"/> enumeration value which specifies the type of the binary operator. This value is assigned to the <see cref="F:DevExpress.Data.Filtering.BinaryOperator.OperatorType"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.String,DevExpress.Data.Filtering.BinaryOperatorType)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class with the specified type of binary operator.  This binary operator is used to compare the values of the specified property to the specified <see cref="T:System.String"/> value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>
            <param name="type">
		A <see cref="T:DevExpress.Data.Filtering.BinaryOperatorType"/> enumeration value which specifies the type of the binary operator. This value is assigned to the <see cref="F:DevExpress.Data.Filtering.BinaryOperator.OperatorType"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Int64,DevExpress.Data.Filtering.BinaryOperatorType)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class with the specified type of binary operator.  This binary operator is used to compare the values of the specified property to the specified <see cref="T:System.Int64"/> value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>
            <param name="type">
		A <see cref="T:DevExpress.Data.Filtering.BinaryOperatorType"/> enumeration value which specifies the type of the binary operator. This value is assigned to the <see cref="F:DevExpress.Data.Filtering.BinaryOperator.OperatorType"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Int16,DevExpress.Data.Filtering.BinaryOperatorType)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class with the specified type of binary operator.  This binary operator is used to compare the values of the specified property to the specified <see cref="T:System.Int16"/> value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>
            <param name="type">
		A <see cref="T:DevExpress.Data.Filtering.BinaryOperatorType"/> enumeration value which specifies the type of the binary operator. This value is assigned to the <see cref="F:DevExpress.Data.Filtering.BinaryOperator.OperatorType"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.#ctor(System.String,System.Guid,DevExpress.Data.Filtering.BinaryOperatorType)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BinaryOperator"/> class with the specified type of binary operator.  This binary operator is used to compare the values of the specified property to the specified <see cref="T:System.Guid"/> value.
</para>
            </summary>
            <param name="propertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are compared to the <i>value</i>.

            </param>
            <param name="value">
		The value to compare with the values of the specified property.

            </param>
            <param name="type">
		A <see cref="T:DevExpress.Data.Filtering.BinaryOperatorType"/> enumeration value which specifies the type of the binary operator. This value is assigned to the <see cref="F:DevExpress.Data.Filtering.BinaryOperator.OperatorType"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.Accept(DevExpress.Data.Filtering.ICriteriaVisitor)">
            <summary>
                <para>Invokes an appropriate overload of the visitor's Visit method.
</para>
            </summary>
            <param name="visitor">
		A visitor that implements the <see cref="T:DevExpress.Data.Filtering.ICriteriaVisitor"/> interface.

            </param>
            <returns>An object returned by the specified visitor's Visit method.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.Clone">
            <summary>
                <para>Creates a copy of the current BinaryOperator instance.
</para>
            </summary>
            <returns>A BinaryOperator object which represents an exact copy of the current object.
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		A BinaryOperator object to compare with the current object.

            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.

</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.BinaryOperator.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current BinaryOperator object.

</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="F:DevExpress.Data.Filtering.BinaryOperator.LeftOperand">
            <summary>
                <para>The left operand.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Data.Filtering.BinaryOperator.OperatorType">
            <summary>
                <para>Gets the type of the binary operator.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.Data.Filtering.BinaryOperatorType"/> enumeration value which specifies the type of the binary operator.
</returns>


        </member>
        <member name="F:DevExpress.Data.Filtering.BinaryOperator.RightOperand">
            <summary>
                <para>The right operand.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Data.Filtering.BetweenOperator">

            <summary>
                <para>Determines whether the expression lies between the specified range of values.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.Filtering.BetweenOperator.#ctor(System.String,System.Object,System.Object)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BetweenOperator"/> class with the specified property name and the specified range of values.
</para>
            </summary>
            <param name="testPropertyName">
		A <see cref="T:System.String"/> value which specifies the name of the property whose values are tested.

            </param>
            <param name="beginValue">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant which represents the expression for the lower value in the range. This value is assigned to the <see cref="P:DevExpress.Data.Filtering.BetweenOperator.BeginExpression"/> property.

            </param>
            <param name="endValue">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant which represents the expression for the upper value in the range. This value is assigned to the <see cref="P:DevExpress.Data.Filtering.BetweenOperator.EndExpression"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BetweenOperator.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BetweenOperator"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Data.Filtering.BetweenOperator.#ctor(System.String,DevExpress.Data.Filtering.CriteriaOperator,DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BetweenOperator"/> class with the specified property and the specified range of values.
</para>
            </summary>
            <param name="testPropertyName">
		A <see cref="T:System.String"/> value which identifies the property whose values are tested.

            </param>
            <param name="beginExpression">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant which represents the expression for the lower value in the range. This value is assigned to the <see cref="P:DevExpress.Data.Filtering.BetweenOperator.BeginExpression"/> property.

            </param>
            <param name="endExpression">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant which represents the expression for the upper value in the range. This value is assigned to the <see cref="P:DevExpress.Data.Filtering.BetweenOperator.EndExpression"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BetweenOperator.#ctor(DevExpress.Data.Filtering.CriteriaOperator,DevExpress.Data.Filtering.CriteriaOperator,DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.BetweenOperator"/> class with the specified property and the specified range of values.
</para>
            </summary>
            <param name="testExpression">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant which represents the expression that identifies the property whose values are tested.

            </param>
            <param name="beginExpression">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant which represents the expression for the lower value in the range. This value is assigned to the <see cref="P:DevExpress.Data.Filtering.BetweenOperator.LeftOperand"/> property.

            </param>
            <param name="endExpression">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant which represents the expression for the upper value in the range. This value is assigned to the <see cref="P:DevExpress.Data.Filtering.BetweenOperator.RightOperand"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.BetweenOperator.Accept(DevExpress.Data.Filtering.ICriteriaVisitor)">
            <summary>
                <para>Invokes an appropriate overload of the visitor's Visit method.
</para>
            </summary>
            <param name="visitor">
		A visitor that implements the <see cref="T:DevExpress.Data.Filtering.ICriteriaVisitor"/> interface.

            </param>
            <returns>An object returned by the specified visitor's Visit method.
</returns>


        </member>
        <member name="P:DevExpress.Data.Filtering.BetweenOperator.BeginExpression">
            <summary>
                <para>Gets the expression for the first operand.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant which represents the expression for the lower value in the range.
</value>


        </member>
        <member name="M:DevExpress.Data.Filtering.BetweenOperator.Clone">
            <summary>
                <para>Creates a copy of the current BetweenOperator instance.
</para>
            </summary>
            <returns>A BetweenOperator object which represents an exact copy of the current object.
</returns>


        </member>
        <member name="P:DevExpress.Data.Filtering.BetweenOperator.EndExpression">
            <summary>
                <para>Gets the expression for the second operand.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant which represents the expression for the upper value in the range.
</value>


        </member>
        <member name="M:DevExpress.Data.Filtering.BetweenOperator.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.

</para>
            </summary>
            <param name="obj">
		A BetweenOperator object to compare with the current object.

            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.

</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.BetweenOperator.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current BetweenOperator object.

</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="P:DevExpress.Data.Filtering.BetweenOperator.LeftOperand">
            <summary>
                <para>Gets the expression for the first (left) operand.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant which represents the expression for the lower value in the range.
</value>


        </member>
        <member name="P:DevExpress.Data.Filtering.BetweenOperator.Property">
            <summary>
                <para>Gets or sets the test expression.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant which represents the test expression.
</value>


        </member>
        <member name="P:DevExpress.Data.Filtering.BetweenOperator.RightOperand">
            <summary>
                <para>Gets the expression for the second (right) operand.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant which represents the expression for the upper value in the range.
</value>


        </member>
        <member name="P:DevExpress.Data.Filtering.BetweenOperator.TestExpression">
            <summary>
                <para>Gets or sets the test expression.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant which represents the test expression.
</value>


        </member>
        <member name="T:DevExpress.Data.Filtering.AggregateOperand">

            <summary>
                <para>Represents an aggregate operator which calculates aggregate expressions (MIN, MAX, SUM, etc.).
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.Filtering.AggregateOperand.#ctor(System.String,DevExpress.Data.Filtering.Aggregate,DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.AggregateOperand"/> class.
</para>
            </summary>
            <param name="collectionProperty">
		A <see cref="T:System.String"/> value which specifies the name of the collection property whose values are used to calculate the aggregate expression. It can be represented by a path through object relationships, for example "Employer.Addresses".


            </param>
            <param name="type">
		An <see cref="T:DevExpress.Data.Filtering.Aggregate"/> enumeration value which specifies the type of the aggregate operator. This value is assigned to the <see cref="P:DevExpress.Data.Filtering.AggregateOperand.AggregateType"/> property.

            </param>
            <param name="condition">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents the filter criteria. <b>null</b> (<b>Nothing</b> in Visual Basic) if the filter criteria isn't needed. This value is assigned to the <see cref="P:DevExpress.Data.Filtering.AggregateOperand.Condition"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.AggregateOperand.#ctor(System.String,System.String,DevExpress.Data.Filtering.Aggregate,DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.AggregateOperand"/> class with the specified settings.
</para>
            </summary>
            <param name="collectionProperty">
		A <see cref="T:System.String"/> value which specifies the name of the collection property. It can be represented by a path through object relationships, for example "Employer.Addresses".

            </param>
            <param name="aggregatedExpression">
		A <see cref="T:System.String"/> value which specifies the name of the persistent property of the object from the <i>property</i> collection.

            </param>
            <param name="type">
		An <see cref="T:DevExpress.Data.Filtering.Aggregate"/> enumeration value which specifies the type of the aggregate operator. This value is assigned to the <see cref="P:DevExpress.Data.Filtering.AggregateOperand.AggregateType"/> property.

            </param>
            <param name="condition">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents the filter criteria. <b>null</b> (<b>Nothing</b> in Visual Basic) if the filter criteria isn't needed. This value is assigned to the <see cref="P:DevExpress.Data.Filtering.AggregateOperand.Condition"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.AggregateOperand.#ctor(DevExpress.Data.Filtering.OperandProperty,DevExpress.Data.Filtering.CriteriaOperator,DevExpress.Data.Filtering.Aggregate,DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.AggregateOperand"/> class with the specified settings.
</para>
            </summary>
            <param name="collectionProperty">
		An <see cref="T:DevExpress.Data.Filtering.OperandProperty"/> object which represents the collection property. This value is assigned to the <see cref="P:DevExpress.Data.Filtering.AggregateOperand.CollectionProperty"/> property.

            </param>
            <param name="aggregatedExpression">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents the aggregated expression. This value is assigned to the <see cref="P:DevExpress.Data.Filtering.AggregateOperand.AggregatedExpression"/> property.

            </param>
            <param name="type">
		An <see cref="T:DevExpress.Data.Filtering.Aggregate"/> enumeration value which specifies the type of the aggregate operator. This value is assigned to the <see cref="P:DevExpress.Data.Filtering.AggregateOperand.AggregateType"/> property.

            </param>
            <param name="condition">
		A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents the filter criteria. <b>null</b> (<b>Nothing</b> in Visual Basic) if the filter criteria isn't needed. This value is assigned to the <see cref="P:DevExpress.Data.Filtering.AggregateOperand.Condition"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.AggregateOperand.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.AggregateOperand"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Data.Filtering.AggregateOperand.#ctor(System.String,DevExpress.Data.Filtering.Aggregate)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.AggregateOperand"/> class with the specified collection property and operator type.
</para>
            </summary>
            <param name="collectionProperty">
		A <see cref="T:System.String"/> value which specifies the name of the collection property whose values are used to calculate the aggregate expression. It can be represented by a path through object relationships, for example "Employer.Addresses".


            </param>
            <param name="type">
		An <see cref="T:DevExpress.Data.Filtering.Aggregate"/> enumeration value which specifies the type of the aggregate operator. This value is assigned to the <see cref="P:DevExpress.Data.Filtering.AggregateOperand.AggregateType"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.AggregateOperand.#ctor(System.String,System.String,DevExpress.Data.Filtering.Aggregate)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.Filtering.AggregateOperand"/> class with the specified settings.
</para>
            </summary>
            <param name="collectionProperty">
		A <see cref="T:System.String"/> value which specifies the name of the collection property whose values are used to calculate the aggregate expression. It can be represented by a path through object relationships, for example "Employer.Addresses".



            </param>
            <param name="aggregatedExpression">
		A <see cref="T:System.String"/> value which specifies the name of the persistent property of the object from the <i>property</i> collection.

            </param>
            <param name="type">
		An <see cref="T:DevExpress.Data.Filtering.Aggregate"/> enumeration value which specifies the type of the aggregate operator. This value is assigned to the <see cref="P:DevExpress.Data.Filtering.AggregateOperand.AggregateType"/> property.


            </param>


        </member>
        <member name="M:DevExpress.Data.Filtering.AggregateOperand.Accept(DevExpress.Data.Filtering.ICriteriaVisitor)">
            <summary>
                <para>Invokes an appropriate overload of the visitor's Visit method.
</para>
            </summary>
            <param name="visitor">
		A visitor that implements the <see cref="T:DevExpress.Data.Filtering.ICriteriaVisitor"/> interface.

            </param>
            <returns>An object returned by the specified visitor's Visit method.
</returns>


        </member>
        <member name="P:DevExpress.Data.Filtering.AggregateOperand.AggregatedExpression">
            <summary>
                <para>Gets or sets the aggregated expression.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents the aggregated expression.
</value>


        </member>
        <member name="P:DevExpress.Data.Filtering.AggregateOperand.AggregateType">
            <summary>
                <para>Gets or sets the type of the aggregate operator.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Data.Filtering.Aggregate"/> enumeration value which specifies the type of the aggregate operator.
</value>


        </member>
        <member name="M:DevExpress.Data.Filtering.AggregateOperand.Avg(DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>For internal use only.


</para>
            </summary>
            <param name="aggregatedExpression">
		@nbsp

            </param>
            <returns>@nbsp
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.AggregateOperand.Clone">
            <summary>
                <para>Creates a copy of the current AggregateOperand instance.
</para>
            </summary>
            <returns>An AggregateOperand object which represents an exact copy of the current object.
</returns>


        </member>
        <member name="P:DevExpress.Data.Filtering.AggregateOperand.CollectionProperty">
            <summary>
                <para>Gets or sets a collection property whose elements are used to calculate an aggregate expression.

</para>
            </summary>
            <value>An <see cref="T:DevExpress.Data.Filtering.OperandProperty"/> object which represents the collection property.
</value>


        </member>
        <member name="P:DevExpress.Data.Filtering.AggregateOperand.Condition">
            <summary>
                <para>Gets or sets the filter criteria which is applied to <see cref="T:DevExpress.Data.Filtering.AggregateOperand"/> values.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> object which represents the filter criteria. <b>null</b> (<b>Nothing</b> in Visual Basic) if the filter criteria isn't needed.
</value>


        </member>
        <member name="M:DevExpress.Data.Filtering.AggregateOperand.Count">
            <summary>
                <para>For internal use only.

</para>
            </summary>
            <returns>@nbsp

</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.AggregateOperand.Count(DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>For internal use only.

</para>
            </summary>
            <param name="aggregatedExpression">
		@nbsp

            </param>
            <returns>@nbsp

</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.AggregateOperand.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		An AggregateOperand object to compare with the current object.


            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.

</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.AggregateOperand.Exists(DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>For internal use only.

</para>
            </summary>
            <param name="aggregatedExpression">
		@nbsp

            </param>
            <returns>@nbsp
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.AggregateOperand.Exists">
            <summary>
                <para>For internal use only.

</para>
            </summary>
            <returns>@nbsp
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.AggregateOperand.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current AggregateOperand object.

</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="P:DevExpress.Data.Filtering.AggregateOperand.IsTopLevel">
            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value><b>true</b> if the <see cref="P:DevExpress.Data.Filtering.AggregateOperand.CollectionProperty"/> is <b>null</b> reference; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.Data.Filtering.AggregateOperand.Max(DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>For internal use only.


</para>
            </summary>
            <param name="aggregatedExpression">
		@nbsp

            </param>
            <returns>@nbsp
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.AggregateOperand.Min(DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>For internal use only.

</para>
            </summary>
            <param name="aggregatedExpression">
		@nbsp

            </param>
            <returns>@nbsp
</returns>


        </member>
        <member name="M:DevExpress.Data.Filtering.AggregateOperand.Sum(DevExpress.Data.Filtering.CriteriaOperator)">
            <summary>
                <para>For internal use only.


</para>
            </summary>
            <param name="aggregatedExpression">
		@nbsp

            </param>
            <returns>@nbsp
</returns>


        </member>
        <member name="T:DevExpress.Data.Filtering.UnaryOperatorType">

            <summary>
                <para>Enumerates unary operator types.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Data.Filtering.UnaryOperatorType.BitwiseNot">
            <summary>
                <para>Represents the bitwise NOT operator.

<para>
To create the <b>bitwise NOT</b> operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax:
</para>

<b>CriteriaOperator.Parse("~Field1")</b>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.UnaryOperatorType.IsNull">
            <summary>
                <para>Represents the operator which determines whether or not a given expression is NULL.

<para>
To create the is-null operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax:
</para>

<b>CriteriaOperator.Parse("Field1 IS NULL")</b>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.UnaryOperatorType.Minus">
            <summary>
                <para>Represents the unary negation (-) operator.

<para>
To create the unary negation operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax:
</para>

<b>CriteriaOperator.Parse("-Field1 = -10")</b>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.UnaryOperatorType.Not">
            <summary>
                <para>Represents the logical NOT.

<para>
To create the <b>logical NOT</b> operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax:
</para>

<b>CriteriaOperator.Parse("NOT ((Field1 = A) OR (Field1 = B))")</b>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.UnaryOperatorType.Plus">
            <summary>
                <para>Represents the unary plus (+) operator.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Data.Filtering.GroupOperatorType">

            <summary>
                <para>Enumerates group operator types.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Data.Filtering.GroupOperatorType.And">
            <summary>
                <para>Groups operands with logical AND.
<para>
To create the <b>logical AND</b> operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax:
</para>

<b>CriteriaOperator.Parse("Field1 @gt; 100 AND Field2 @lt; 150")</b>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.GroupOperatorType.Or">
            <summary>
                <para>Groups operands with logical OR.
<para>
To create the <b>logical OR</b> operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax:
</para>
<b>CriteriaOperator.Parse("Field1 @gt; 100 OR Field2 @lt; 150")</b>
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Data.Filtering.FunctionOperatorType">

            <summary>
                <para>Enumerates function types that can be used by the <see cref="T:DevExpress.Data.Filtering.FunctionOperator"/> operators.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.Concat">
            <summary>
                <para>Concatenates one or more strings.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.Custom">
            <summary>
                <para>Identifies a custom operation.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.Iif">
            <summary>
                <para>Returns one of two values depending upon the value of a logical expression.

<para>
The function requires three operands of the  <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> class:
</para>

<para>
1 - determines the logical expression;
</para>
<para>
2 - specifies the value that will be returned if the logical expression evaluates to TRUE;
</para>
<para>
3 - specifies the value that  will be returned if the logical expression evaluates to FALSE
</para>

<para>
To create the <b>Iif</b> operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax: 
<b>CriteriaOperator.Parse("Iif(Field1 == 100, Field1, Field2)")</b>
</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.IsNull">
            <summary>
                <para>Compares the first operand with the NULL value.

This function requires one or two operands of the  <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> class. The value returned depends upon the number of arguments.

<para>
If a single argument is passed, the function returns <b>true</b> if the operand is <b>null</b>, otherwise, <b>false</b> is returned.
</para>

<para>
If two operands are passed, the function returns the second operand if the first operand is <b>null</b>, otherwise, the first operand is returned.
</para>

<para>
To create the <b>IsNull</b> operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax: 
<b>CriteriaOperator.Parse("IsNull(Field1)");</b>
</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.IsOutlookIntervalBeyondThisYear">
            <summary>
                <para>The Boolean Is Beyond This Year operator for date/time values. Requires one argument.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.IsOutlookIntervalEarlierThisMonth">
            <summary>
                <para>The Boolean Is Earlier This Month operator for date/time values. Requires one argument.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.IsOutlookIntervalEarlierThisWeek">
            <summary>
                <para>The Boolean Is Earlier This Week operator for date/time values. Requires one argument.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.IsOutlookIntervalEarlierThisYear">
            <summary>
                <para>The Boolean Is Earlier This Year operator for date/time values. Requires one argument.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.IsOutlookIntervalLastWeek">
            <summary>
                <para>The Boolean Is Last Week operator for date/time values. Requires one argument.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.IsOutlookIntervalLaterThisMonth">
            <summary>
                <para>The Boolean Is Later This Month operator for date/time values. Requires one argument.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.IsOutlookIntervalLaterThisWeek">
            <summary>
                <para>The Boolean Is Later This Week operator for date/time values. Requires one argument.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.IsOutlookIntervalLaterThisYear">
            <summary>
                <para>The Boolean Is Later This Year operator for date/time values. Requires one argument.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.IsOutlookIntervalNextWeek">
            <summary>
                <para>The Boolean Is Next Week operator for date/time values. Requires one argument.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.IsOutlookIntervalPriorThisYear">
            <summary>
                <para>The Boolean Is Prior This Year operator for date/time values. Requires one argument.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.IsOutlookIntervalToday">
            <summary>
                <para>The Boolean Is Today operator for date/time values. Requires one argument.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.IsOutlookIntervalTomorrow">
            <summary>
                <para>The Boolean Is Tomorrow operator for date/time values. Requires one argument.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.IsOutlookIntervalYesterday">
            <summary>
                <para>The Boolean Is Yesterday operator for date/time values. Requires one argument.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.Len">
            <summary>
                <para>Returns the length of the string specified by an operand.

<para>
The operand should be an object of the <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> type.
</para>


<para>
To create the <b>Len</b> operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax: 
<b>CriteriaOperator.Parse("Len(Field2)");</b>
</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.LocalDateTimeDayAfterTomorrow">
            <summary>
                <para>Returns the DateTime value corresponding to the day after Tomorrow.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.LocalDateTimeLastWeek">
            <summary>
                <para>Returns the DateTime value corresponding to the first day of the previous week.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.LocalDateTimeNextMonth">
            <summary>
                <para>Returns the DateTime value corresponding to the first day of next month.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.LocalDateTimeNextWeek">
            <summary>
                <para>Returns the DateTime value corresponding to the first day of the following week.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.LocalDateTimeNextYear">
            <summary>
                <para>Returns the DateTime value corresponding to the first day of the following year.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.LocalDateTimeNow">
            <summary>
                <para>Returns the DateTime value corresponding to the current moment in time.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.LocalDateTimeThisMonth">
            <summary>
                <para>Returns the DateTime value corresponding to the first day of the current month.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.LocalDateTimeThisWeek">
            <summary>
                <para>Returns the DateTime value corresponding to the first day of the current week.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.LocalDateTimeThisYear">
            <summary>
                <para>Returns the DateTime value corresponding to the first day of the current year.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.LocalDateTimeToday">
            <summary>
                <para>Returns the DateTime value corresponding to Today.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.LocalDateTimeTomorrow">
            <summary>
                <para>Returns the DateTime value corresponding to Tomorrow.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.LocalDateTimeTwoWeeksAway">
            <summary>
                <para>Returns the DateTime value corresponding to the day 14 days from the current date.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.LocalDateTimeYesterday">
            <summary>
                <para>Returns the DateTime value corresponding to Yesterday.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.Lower">
            <summary>
                <para>Converts all characters in a string operand to lowercase.
<para>
The operand should be an object of the <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> type.
</para>
To create the <b>Lower</b> operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method, use the following syntax: <b>CriteriaOperator.Parse("Lower(Field1)");</b> 

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.None">
            <summary>
                <para>The function type is not specified.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.Substring">
            <summary>
                <para>Returns a substring extracted from the specified string. This function requires two or three operands of the <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> class.
<para>
If two operands are passed the substring will be extracted from the beginning of the original string. The operands should be defined as follows:
</para>
<para>
1 - an original string;
</para>
<para>
2 - an integer that specifies the length of the substring.
</para>

<para>
If three operands are passed a substring can be subtracted starting from any position in the original string. The operands should be defined as follows:

</para>
<para>
1 - an original string;
</para>
<para>
2 - an integer that specifies the zero-based index at which the substring to return begins;
</para>
<para>
3 - an integer that specifies the length of the substring.
</para>

<para>
To create a <b>Substring</b> operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax: 
<b>CriteriaOperator.Parse("Substring(Field1, 1, 3)");</b>
</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.Trim">
            <summary>
                <para>Returns a string containing a copy of a specified string with no leading nor trailing spaces. This function requires a single operand of the <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> class that refers to the original string.

<para>
To create the <b>Trim</b> operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax: 
<b>CriteriaOperator.Parse("Trim(Field2)");</b>
</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.FunctionOperatorType.Upper">
            <summary>
                <para>Converts all characters in a string operand to uppercase.
<para>
The operand should be an object of the <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> type.
</para>
To create the <b>Upper</b> operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method, use the following syntax: <b>CriteriaOperator.Parse("Upper(Field1)");</b> 

</para>
            </summary>


        </member>
        <member name="T:DevExpress.Data.Filtering.BinaryOperatorType">

            <summary>
                <para>Enumerates binary operator types.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Data.Filtering.BinaryOperatorType.BitwiseAnd">
            <summary>
                <para>Represents the bitwise AND operator.

<para>
To create the <b>bitwise AND</b> operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax:
</para>

<b>CriteriaOperator.Parse("Field1 @ 128 = 128")</b>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.BinaryOperatorType.BitwiseOr">
            <summary>
                <para>Represents the bitwise OR operator.

<para>
To create the <b>bitwise OR</b> operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax:
</para>

<b>CriteriaOperator.Parse("Field1 | 3")</b>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.BinaryOperatorType.BitwiseXor">
            <summary>
                <para>Represents the bitwise XOR operator.

<para>
To create the <b>bitwise XOR</b> operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax:
</para>

<b>CriteriaOperator.Parse("(Field1 ^ Field2) = 1")</b>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.BinaryOperatorType.Divide">
            <summary>
                <para>Represents the division operator.

<para>
To create the division operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax:
</para>
<b>CriteriaOperator.Parse("Field1 / Field2 = 2")</b>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.BinaryOperatorType.Equal">
            <summary>
                <para>Represents the Boolean equality operator.

<para>
To create the Boolean equality operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax:
</para>

<b>CriteriaOperator.Parse("Field1 = Field2")</b>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.BinaryOperatorType.Greater">
            <summary>
                <para>Represents the Boolean greater-than operator.

<para>
To create the Boolean greater-than operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax:
</para>

<b>CriteriaOperator.Parse("Field1 @gt; Field2")</b>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.BinaryOperatorType.GreaterOrEqual">
            <summary>
                <para>Represents the Boolean greater-than-or-equal-to operator.

<para>
To create the Boolean greater-than-or-equal-to operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax:
</para>

<b>CriteriaOperator.Parse("Field1 @gt;= Field2")</b>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.BinaryOperatorType.Less">
            <summary>
                <para>Represents the Boolean less-than operator.

<para>
To create the Boolean less-than operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax:
</para>

<b>CriteriaOperator.Parse("Field1 @lt; Field2")</b>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.BinaryOperatorType.LessOrEqual">
            <summary>
                <para>Represents the Boolean less-than-or-equal-to operator.

<para>
To create the Boolean less-than-or-equal-to operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax:
</para>

<b>CriteriaOperator.Parse("Field1 @lt;= Field2")</b>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.BinaryOperatorType.Like">
            <summary>
                <para>Represents the LIKE operator.

<para>
To create the <b>LIKE</b> operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax:
</para>

<b>CriteriaOperator.Parse("Field1 like '%val1%' OR Field1 like '%val2%'")</b>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.BinaryOperatorType.Minus">
            <summary>
                <para>Represents the subtraction operator.

<para>
To create the subtraction operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax:
</para>

<b>CriteriaOperator.Parse("Field1 - Field2 = 10")</b>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.BinaryOperatorType.Modulo">
            <summary>
                <para>Represents the modulus operator (computes the remainder after dividing its first operand by its second).

<para>
To create the modulus operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax:
</para>

<b>CriteriaOperator.Parse("Field1 % Field2 = 1")</b>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.BinaryOperatorType.Multiply">
            <summary>
                <para>Represents the multiplication operator.

<para>
To create the multiplication operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax:
</para>

<b>CriteriaOperator.Parse("Field1 * Field2 = 100")</b>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.BinaryOperatorType.NotEqual">
            <summary>
                <para>Represents the Boolean inequality operator.

<para>
To create the Boolean inequality operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax:
</para>

<b>CriteriaOperator.Parse("Field1 != Field2")</b> or <b>CriteriaOperator.Parse("Field1 @lt;@gt; Field2")</b>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.BinaryOperatorType.Plus">
            <summary>
                <para>Represents the addition operator.

<para>
To create the addition operator using the <see cref="M:DevExpress.Data.Filtering.CriteriaOperator.Parse"/> method use the following syntax:
</para>

<b>CriteriaOperator.Parse("Field1 + Field2 = 20")</b>
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Data.Filtering.Aggregate">

            <summary>
                <para>Enumerates aggregate functions.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Data.Filtering.Aggregate.Avg">
            <summary>
                <para>Evaluates the average of the values in the collection.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.Aggregate.Count">
            <summary>
                <para>Returns the number of objects in the collection.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.Aggregate.Exists">
            <summary>
                <para>Determines whether the object exists in the collection.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.Aggregate.Max">
            <summary>
                <para>Returns the maximum expression value in the collection.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.Aggregate.Min">
            <summary>
                <para>Returns the minimum expression value in the collection.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.Filtering.Aggregate.Sum">
            <summary>
                <para>Returns the sum of all the expression values in the collection.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraSpellChecker.SpellCheckMode">

            <summary>
                <para>Lists available operation modes of the spell checker.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraSpellChecker.SpellCheckMode.AsYouType">
            <summary>
                <para>Starts spelling check when you finish typing the word or move the cursor after changing it.This operation mode underlines the misspelled word and allows you to select a suggestion from the context menu, invoked by right-clicking the word.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraSpellChecker.SpellCheckMode.OnDemand">
            <summary>
                <para>Starts spelling check and invokes the spelling form for making corrections.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraEditors.DXErrorProvider.IDXDataErrorInfo">

            <summary>
                <para>Provides methods to return error information for a business object's properties.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraEditors.DXErrorProvider.IDXDataErrorInfo.GetError(DevExpress.XtraEditors.DXErrorProvider.ErrorInfo)">
            <summary>
                <para>When implemented by a class, this method returns information on an error associated with a business object.
</para>
            </summary>
            <param name="info">
		An <see cref="T:DevExpress.XtraEditors.DXErrorProvider.ErrorInfo"/> object that contains information on an error.

            </param>


        </member>
        <member name="M:DevExpress.XtraEditors.DXErrorProvider.IDXDataErrorInfo.GetPropertyError(System.String,DevExpress.XtraEditors.DXErrorProvider.ErrorInfo)">
            <summary>
                <para>When implemented by a class, this method returns information on an error associated with a specific business object's property.
</para>
            </summary>
            <param name="propertyName">
		A string that identifies the name of the property for which information on an error is to be returned.

            </param>
            <param name="info">
		An <see cref="T:DevExpress.XtraEditors.DXErrorProvider.ErrorInfo"/> object that contains information on an error.

            </param>


        </member>
        <member name="T:DevExpress.XtraEditors.DXErrorProvider.ErrorInfo">

            <summary>
                <para>Encapsulates error information on a specific property.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraEditors.DXErrorProvider.ErrorInfo.#ctor">
            <summary>
                <para>Initializes a new instance of the ErrorInfo class with the default values.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraEditors.DXErrorProvider.ErrorInfo.#ctor(System.String,DevExpress.XtraEditors.DXErrorProvider.ErrorType)">
            <summary>
                <para>Initializes a new instance of the ErrorInfo class with the specified error information.
</para>
            </summary>
            <param name="errorText">
		A string that specifies the error text. This value is used to initialize the <see cref="P:DevExpress.XtraEditors.DXErrorProvider.ErrorInfo.ErrorText"/> property.

            </param>
            <param name="errorType">
		An error type. This value is used to initialize the <see cref="P:DevExpress.XtraEditors.DXErrorProvider.ErrorInfo.ErrorType"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraEditors.DXErrorProvider.ErrorInfo.ErrorText">
            <summary>
                <para>Gets or sets the error text associated with the current property name.
</para>
            </summary>
            <value>A string that represents the error text.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.DXErrorProvider.ErrorInfo.ErrorType">
            <summary>
                <para>Gets or sets the type of error associated with the current property name.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraEditors.DXErrorProvider.ErrorType"/> value that represents the error type.
</value>


        </member>
        <member name="T:DevExpress.Utils.Controls.BaseOptionChangedEventHandler">

            <summary>
                <para>Represents a method that will handle the event raised after an option value change.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.Controls.BaseOptionChangedEventHandler.Invoke(System.Object,DevExpress.Utils.Controls.BaseOptionChangedEventArgs)">
            <summary>
                <para>Represents a method that will handle the event raised after the option has been changed.
</para>
            </summary>
            <param name="sender">
		The event sender.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Utils.Controls.BaseOptionChangedEventArgs"/> object that contains data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.Utils.FormatType">

            <summary>
                <para>Enumerates values for the <see cref="P:DevExpress.Utils.FormatInfo.FormatType"/> property.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Utils.FormatType.Custom">
            <summary>
                <para>Specifies that you assign a custom <see cref="T:System.IFormatProvider"/> to the <see cref="P:DevExpress.Utils.FormatInfo.Format"/> property.  Setting <see cref="P:DevExpress.Utils.FormatInfo.FormatType"/> to <b>Custom</b> does not change the <see cref="P:DevExpress.Utils.FormatInfo.Format"/> and <see cref="P:DevExpress.Utils.FormatInfo.FormatString"/> property values.

<para>
If you wish to use your own <see cref="T:System.IFormatProvider"/> to format values, set the <see cref="P:DevExpress.Utils.FormatInfo.FormatType"/> property to <b>Custom</b> and  the <see cref="P:DevExpress.Utils.FormatInfo.Format"/> property to the required format provider.
</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.FormatType.DateTime">
            <summary>
                <para>Specifies that values should be formatted as the <see cref="T:System.DateTime"/> type.
<para>
Setting <see cref="P:DevExpress.Utils.FormatInfo.FormatType"/> to <b>DateTime</b> assigns the static <see cref="P:System.Globalization.DateTimeFormatInfo.CurrentInfo"/> format provider to the <see cref="P:DevExpress.Utils.FormatInfo.Format"/> property. This defines the format provider for the current culture.
Also <see cref="P:DevExpress.Utils.FormatInfo.FormatString"/> is set to "d" (short date pattern).
</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.FormatType.None">
            <summary>
                <para>No formatting is performed.
<para>
Setting the <see cref="P:DevExpress.Utils.FormatInfo.FormatType"/> property to <b>None</b> clears the <see cref="P:DevExpress.Utils.FormatInfo.FormatString"/> property and sets the <see cref="P:DevExpress.Utils.FormatInfo.Format"/> object to <b>null</b>.
</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.FormatType.Numeric">
            <summary>
                <para>Specifies that values should be formatted as numbers.
<para>
Setting <see cref="P:DevExpress.Utils.FormatInfo.FormatType"/> to <b>Numeric</b> assigns the static <b>System.Globalization.NumberFormatInfo.CurrentInfo</b> format provider to the <see cref="P:DevExpress.Utils.FormatInfo.Format"/> property. This defines the format provider for the current culture.

Also <see cref="P:DevExpress.Utils.FormatInfo.FormatString"/> is set to an empty string.
</para>
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.VertAlignment">

            <summary>
                <para>Specifies the vertical alignment of an object or text in a control.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Utils.VertAlignment.Bottom">
            <summary>
                <para>Places the text to the bottom of an object.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.VertAlignment.Center">
            <summary>
                <para>Centers the text within an object.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.VertAlignment.Default">
            <summary>
                <para>Places the text to its default position. The default position is in the center of an object.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.VertAlignment.Top">
            <summary>
                <para>Places the text to the top of an object.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.HorzAlignment">

            <summary>
                <para>Specifies the horizontal alignment of an object or text in a control.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Utils.HorzAlignment.Center">
            <summary>
                <para>Centers an object or text within an object.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.HorzAlignment.Default">
            <summary>
                <para>Places an object or text at the position specified via the <B>RightToLeft</B> property.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.HorzAlignment.Far">
            <summary>
                <para>Sets the object/text position relative to its default position within an object.
The default position of the text is specified via the <B>RightToLeft</B> property. If the default alignment is left, setting an alignment option to Far places the text to the right. Conversely, if the default alignment is right, setting the alignment option to Far places the text to the left.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.HorzAlignment.Near">
            <summary>
                <para>Sets the object/text position relative to its default position within an object.
The default position of the text is specified via the <B>RightToLeft</B> property. If the default alignment is left, setting the alignment option to Near places the text to the right. Convercely, if the default alignment is right, setting the alignment option to Near places the text to the right.

</para>
            </summary>


        </member>
        <member name="T:DevExpress.Data.IRelationListEx">

            <summary>
                <para>An extended version of the <see cref="T:DevExpress.Data.IRelationList"/> interface, that can be implemented by a data source to support master-detail relationships.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.IRelationListEx.GetRelationCount(System.Int32)">
            <summary>
                <para>When implemented by a class, returns the number of relationships for a master row.
</para>
            </summary>
            <param name="index">
		An integer value that specifies the master row.

            </param>
            <returns>An integer value that specifies the number of relationships for a master row.
</returns>


        </member>
        <member name="M:DevExpress.Data.IRelationListEx.GetRelationDisplayName(System.Int32,System.Int32)">
            <summary>
                <para>When implemented by a class, returns the display caption of a particular relationship in a specific master row. 
</para>
            </summary>
            <param name="index">
		An integer value that specifies the master row.

            </param>
            <param name="relationIndex">
		An integer value that specifies the relationship.

            </param>
            <returns>A string value that specifies the display caption of a particular relationship in a specific master row. 
</returns>


        </member>
        <member name="T:DevExpress.Data.IRelationList">

            <summary>
                <para>An interface that can be implemented by a data source to support master-detail relationships.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.IRelationList.GetDetailList(System.Int32,System.Int32)">
            <summary>
                <para>When implemented by a class, returns detail data for a relationship.
</para>
            </summary>
            <param name="index">
		An integer value that specifies the index of a master row in the data source.

            </param>
            <param name="relationIndex">
		An integer value that specifies the relationship.

            </param>
            <returns>An object that implements the <b>IList</b> interface and represents detail data for a relationship.
</returns>


        </member>
        <member name="M:DevExpress.Data.IRelationList.GetRelationName(System.Int32,System.Int32)">
            <summary>
                <para>When implemented by a class, returns the name of a master-detail relationship.
</para>
            </summary>
            <param name="index">
		An integer value that specifies the index of a master row in the data source.

            </param>
            <param name="relationIndex">
		An integer value that specifies the relationship.

            </param>
            <returns>The name of a master-detail relationship.
</returns>


        </member>
        <member name="M:DevExpress.Data.IRelationList.IsMasterRowEmpty(System.Int32,System.Int32)">
            <summary>
                <para>When implemented by a class, returns a Boolean value that specifies whether a specific detail of a particular master row contains data.
</para>
            </summary>
            <param name="index">
		An integer value that specifies the index of a master row in the data source.

            </param>
            <param name="relationIndex">
		An integer value that specifies the relationship.

            </param>
            <returns>A Boolean value that specifies whether a specific detail of a particular master row contains data.
</returns>


        </member>
        <member name="P:DevExpress.Data.IRelationList.RelationCount">
            <summary>
                <para>When implemented by a class, returns the number of master-detail relationships for the current master data source.
</para>
            </summary>
            <value>An integer value that specifies the number of master-detail relationships for the current master data source.
</value>


        </member>
        <member name="T:DevExpress.Data.ExceptionAction">

            <summary>
                <para>Specifies how to resolve exceptions while performing an action.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Data.ExceptionAction.CancelAction">
            <summary>
                <para>Cancel the action that caused an exception.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.ExceptionAction.RetryAction">
            <summary>
                <para>Retry the action that caused an exception.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.FormatInfo">

            <summary>
                <para>Provides formatting settings.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.FormatInfo.#ctor">
            <summary>
                <para>Creates an instance of the <see cref="T:DevExpress.Utils.FormatInfo"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.FormatInfo.#ctor(DevExpress.Utils.IComponentLoading)">
            <summary>
                <para>Initializes a new instance of the FormatInfo class with the specified settings.
</para>
            </summary>
            <param name="componentLoading">
		A DevExpress.Utils.IComponentLoading object.

            </param>


        </member>
        <member name="M:DevExpress.Utils.FormatInfo.#ctor(DevExpress.WebUtils.IViewBagOwner,System.String)">
            <summary>
                <para>Initializes a new instance of the FormatInfo class.
</para>
            </summary>
            <param name="bagOwner">
		An object that implements the IViewBagOwner interface.

            </param>
            <param name="objectPath">
		The string value.

            </param>


        </member>
        <member name="M:DevExpress.Utils.FormatInfo.#ctor(DevExpress.Utils.IComponentLoading,DevExpress.WebUtils.IViewBagOwner,System.String)">
            <summary>
                <para>Initializes a new instance of the FormatInfo class with the specified settings.
</para>
            </summary>
            <param name="componentLoading">
		A DevExpress.Utils.IComponentLoading object.

            </param>
            <param name="bagOwner">
		An object that implements the IViewBagOwner interface.

            </param>
            <param name="objectPath">
		The string value.

            </param>


        </member>
        <member name="P:DevExpress.Utils.FormatInfo.AlwaysUseThreadFormat">
            <summary>
                <para>Gets or sets whether a value for the <see cref="P:DevExpress.Utils.FormatInfo.Format"/> property should be determined each time the property is accessed.
</para>
            </summary>
            <value><b>true</b> if the value for the <see cref="P:DevExpress.Utils.FormatInfo.Format"/> property should be determined each time the property is accessed.
</value>


        </member>
        <member name="M:DevExpress.Utils.FormatInfo.Assign(DevExpress.Utils.FormatInfo)">
            <summary>
                <para>Copies properties of the specified <see cref="T:DevExpress.Utils.FormatInfo"/> object to the current object.
</para>
            </summary>
            <param name="info">
		The <see cref="T:DevExpress.Utils.FormatInfo"/> object whose properties must be copied.

            </param>


        </member>
        <member name="E:DevExpress.Utils.FormatInfo.Changed">
            <summary>
                <para>Occurs on changing properties of the current <see cref="T:DevExpress.Utils.FormatInfo"/> class object.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.FormatInfo.Empty">
            <summary>
                <para>Gets a <see cref="T:DevExpress.Utils.FormatInfo"/> object with default settings.
</para>
            </summary>
            <value>The <see cref="T:DevExpress.Utils.FormatInfo"/> object with default property values.
</value>


        </member>
        <member name="P:DevExpress.Utils.FormatInfo.Format">
            <summary>
                <para>Gets or sets the <see cref="T:System.IFormatProvider"/> object which specifies how values should be formatted.
</para>
            </summary>
            <value>The <b>IFormatProvider</b> object which specifies how values should be formatted.
</value>


        </member>
        <member name="P:DevExpress.Utils.FormatInfo.FormatString">
            <summary>
                <para>Gets the pattern for formatting values.
</para>
            </summary>
            <value>The string representing the format pattern.
</value>


        </member>
        <member name="P:DevExpress.Utils.FormatInfo.FormatType">
            <summary>
                <para>Gets or sets the type of formatting specified by the current <see cref="T:DevExpress.Utils.FormatInfo"/> object.
</para>
            </summary>
            <value>One of <see cref="T:DevExpress.Utils.FormatType"/> values specifying the formatting type.
</value>


        </member>
        <member name="M:DevExpress.Utils.FormatInfo.GetDisplayText(System.Object)">
            <summary>
                <para>Gets the value formatted according to the format pattern and using the format provider settings.
</para>
            </summary>
            <param name="val">
		The value to format.

            </param>
            <returns>The text representation of the specified value according to settings of the current <see cref="T:DevExpress.Utils.FormatInfo"/> object.
</returns>


        </member>
        <member name="M:DevExpress.Utils.FormatInfo.GetFormatString">
            <summary>
                <para>Returns the actual format string.
</para>
            </summary>
            <returns>The actual format string.
</returns>


        </member>
        <member name="P:DevExpress.Utils.FormatInfo.IsEmpty">
            <summary>
                <para>Tests whether properties of the current object are set to default values.
</para>
            </summary>
            <value><b>true</b> if the object properties are set to default values; otherwise,<b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.Utils.FormatInfo.IsEquals(DevExpress.Utils.FormatInfo)">
            <summary>
                <para>Compares properties of the current object with settings of the specified <see cref="T:DevExpress.Utils.FormatInfo"/> object.
</para>
            </summary>
            <param name="info">
		The <see cref="T:DevExpress.Utils.FormatInfo"/> object whose properties are compared.

            </param>
            <returns><b>true</b> if properties of the current object equal to corresponding properties of the <i>info</i> parameter.
</returns>


        </member>
        <member name="M:DevExpress.Utils.FormatInfo.LockParse">
            <summary>
                <para>The method supports the internal .NET infrastructure and you must not call it from your code.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.FormatInfo.OnEndDeserializing(System.String)">
            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="restoredVersion">
		@nbsp

            </param>


        </member>
        <member name="M:DevExpress.Utils.FormatInfo.OnEndSerializing">
            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.FormatInfo.OnStartDeserializing(DevExpress.Utils.LayoutAllowEventArgs)">
            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="e">
		@nbsp

            </param>


        </member>
        <member name="M:DevExpress.Utils.FormatInfo.OnStartSerializing">
            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.FormatInfo.Parse">
            <summary>
                <para>Performs actions when the <see cref="P:DevExpress.Utils.FormatInfo.FormatType"/> property changes.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.FormatInfo.Reset">
            <summary>
                <para>Reverts properties of the current object to default values.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.FormatInfo.ShouldSerialize">
            <summary>
                <para>Tests whether the <see cref="T:DevExpress.Utils.FormatInfo"/> object should be persisted.
</para>
            </summary>
            <returns><b>true</b> if the object should be persisted; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Utils.FormatInfo.ToString">
            <summary>
                <para>Returns a string that represents the current object.
</para>
            </summary>
            <returns>A <see cref="T:System.String"/> value that represents the current object.
</returns>


        </member>
        <member name="M:DevExpress.Utils.FormatInfo.UnlockParse">
            <summary>
                <para>The method supports the internal .NET infrastructure and you must not call it from your code.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraGrid.ColumnSortMode">

            <summary>
                <para>Lists the values that specify how a column's data should be sorted.

</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraGrid.ColumnSortMode.Custom">
            <summary>
                <para>Enables custom sorting of the column's data via the <see cref="E:DevExpress.XtraGrid.Views.Base.ColumnView.CustomColumnSort"/> event.

<para>
Enables custom grouping of the rows when grouping is applied against the current column via the <see cref="E:DevExpress.XtraGrid.Views.Grid.GridView.CustomColumnGroup"/> event.
</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraGrid.ColumnSortMode.Default">
            <summary>
                <para>Sorts the column's data according to the type of the editor assigned to the column. 

<para>
For columns which use <see cref="T:DevExpress.XtraEditors.LookUpEdit"/> and <see cref="T:DevExpress.XtraEditors.ImageComboBoxEdit"/> in-place editors the data is sorted by the displayed values (the strings displayed within the column's cells). 
</para>

For other columns their data is sorted by the edit values (these are synchronized with the bound data source's values). For some editors (<see cref="T:DevExpress.XtraEditors.TextEdit"/>, <see cref="T:DevExpress.XtraEditors.ComboBoxEdit"/>, etc), however, the edit values match the display values.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraGrid.ColumnSortMode.DisplayText">
            <summary>
                <para>Sorts the column's data by the column's display text (the strings displayed within the column's cells). 

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraGrid.ColumnSortMode.Value">
            <summary>
                <para>Sorts the column's data by the column's edit values (these are synchronized with the bound data source's values). 
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraGrid.ColumnGroupInterval">

            <summary>
                <para>Lists the values that specify how the data rows are combined into groups when in grouping mode.

</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraGrid.ColumnGroupInterval.Alphabetical">
            <summary>
                <para>Rows are grouped by the character that their values start with.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraGrid.ColumnGroupInterval.Date">
            <summary>
                <para>This option is in effect only for columns that store date/time values. 
<para>
Rows are grouped by the date part of their values, the time portion is ignored in this grouping mode. </para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraGrid.ColumnGroupInterval.DateMonth">
            <summary>
                <para>This option is in effect only for columns that store date/time values. 
<para>
Rows are grouped by the month part of their values. </para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraGrid.ColumnGroupInterval.DateRange">
            <summary>
                <para>This option is in effect only for columns that store date/time values. 
<para>
Rows are grouped into the following groups according to their date value as compared with the current system date: "Today", "Tomorrow", "Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Next Week", "Two Weeks Away", "Three Weeks Away", "Next Month", "Beyond Next Month", "Yesterday", "Last Week", "Two Weeks Ago", "Three Weeks Ago", "Last Month", "Older".
</para>


</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraGrid.ColumnGroupInterval.DateYear">
            <summary>
                <para>This option is in effect only for columns that store date/time values. 
<para>
Rows are grouped by the year part of their values.
</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraGrid.ColumnGroupInterval.Default">
            <summary>
                <para>In regular binding mode, for columns which store date/time values, this option is the same as the <see cref="F:DevExpress.XtraGrid.ColumnGroupInterval.Date"/> option. For non date/time columns this option is the same as the <see cref="F:DevExpress.XtraGrid.ColumnGroupInterval.Value"/> option.

<para>
In server mode, rows are always grouped by values. See the description of the <see cref="F:DevExpress.XtraGrid.ColumnGroupInterval.Value"/> option.
</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraGrid.ColumnGroupInterval.DisplayText">
            <summary>
                <para>Rows are grouped by cells' display values.

<para>
In specific instances, cells can have different edit values, but the same display value. To combine these rows into the same group, use the <b>DisplayText</b> group mode.
</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraGrid.ColumnGroupInterval.Value">
            <summary>
                <para>Rows are grouped by their values (the entire values of each row in a group have to match). The number of groups matches the number of unique values within the grouping column. 

<para>
Note that <see cref="T:System.DateTime"/> objects which have the same date portion but different time portions are treated as unique values (each has it's own group).
</para>


</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.Controls.BaseOptionChangedEventArgs">

            <summary>
                <para>Provides data for the events that fire after option changes .
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.Controls.BaseOptionChangedEventArgs.#ctor(System.String,System.Object,System.Object)">
            <summary>
                <para>Creates a new <see cref="T:DevExpress.Utils.Controls.BaseOptionChangedEventArgs"/> object.
</para>
            </summary>
            <param name="name">
		A <see cref="T:System.String"/> value representing the name of the option whose value has been changed. This value is assigned to the <see cref="P:DevExpress.Utils.Controls.BaseOptionChangedEventArgs.Name"/> property.

            </param>
            <param name="oldValue">
		A <see cref="T:System.Object"/> value representing the option's previous value. This value is assigned to the <see cref="P:DevExpress.Utils.Controls.BaseOptionChangedEventArgs.OldValue"/> property.

            </param>
            <param name="newValue">
		A <see cref="T:System.Object"/> value representing the option's current value. This value is assigned to the <see cref="P:DevExpress.Utils.Controls.BaseOptionChangedEventArgs.NewValue"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Controls.BaseOptionChangedEventArgs.#ctor">
            <summary>
                <para>Creates a new <see cref="T:DevExpress.Utils.Controls.BaseOptionChangedEventArgs"/> object with default values.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.Controls.BaseOptionChangedEventArgs.Name">
            <summary>
                <para>Gets the name of the option whose value has been changed.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value representing the option's name.
</value>


        </member>
        <member name="P:DevExpress.Utils.Controls.BaseOptionChangedEventArgs.NewValue">
            <summary>
                <para>Gets or sets the option's current value. 
</para>
            </summary>
            <value>A <see cref="T:System.Object"/> value representing the option's current value.
</value>


        </member>
        <member name="P:DevExpress.Utils.Controls.BaseOptionChangedEventArgs.OldValue">
            <summary>
                <para>Gets the option's previous value.
</para>
            </summary>
            <value>A <see cref="T:System.Object"/> value representing the option's previous value.
</value>


        </member>
        <member name="T:DevExpress.Utils.Controls.BaseOptions">

            <summary>
                <para>Serves as a base for classes representing options.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.Controls.BaseOptions.#ctor">
            <summary>
                <para>Creates a new <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> object.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.Controls.BaseOptions.#ctor(DevExpress.WebUtils.IViewBagOwner,System.String)">
            <summary>
                <para>Initializes a new instance of the BaseOptions class.
</para>
            </summary>
            <param name="viewBagOwner">
		An object that implements the IViewBagOwner interface.

            </param>
            <param name="objectPath">
		The string value.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Controls.BaseOptions.Assign(DevExpress.Utils.Controls.BaseOptions)">
            <summary>
                <para>Copies all settings from the options object passed as a parameter.
</para>
            </summary>
            <param name="options">
		A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object. 

            </param>


        </member>
        <member name="M:DevExpress.Utils.Controls.BaseOptions.BeginUpdate">
            <summary>
                <para>Prevents the control from being updated until the <see cref="M:DevExpress.Utils.Controls.BaseOptions.CancelUpdate"/> or <see cref="M:DevExpress.Utils.Controls.BaseOptions.EndUpdate"/> method is called.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.Controls.BaseOptions.CancelUpdate">
            <summary>
                <para>Re-enables control updates after the <see cref="M:DevExpress.Utils.Controls.BaseOptions.BeginUpdate"/> method call without causing immediate repainting of the control.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.Controls.BaseOptions.EndUpdate">
            <summary>
                <para>Re-enables control updates after the <see cref="M:DevExpress.Utils.Controls.BaseOptions.BeginUpdate"/> method call and forces an immediate update.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.Controls.BaseOptions.Reset">
            <summary>
                <para>Resets all options to their default values.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.Controls.BaseOptions.ToString">
            <summary>
                <para>Returns a string representing the currently enabled options.
</para>
            </summary>
            <returns>A <see cref="T:System.String"/> value representing the currently enabled options. "" (String.Empty) if no option is enabled.
</returns>


        </member>
        <member name="T:System.ComponentModel.DXDisplayNameAttribute">

            <summary>
                <para>Specifies the display name for a property or event.
</para>
            </summary>

        </member>
        <member name="M:System.ComponentModel.DXDisplayNameAttribute.#ctor(System.Type,System.String,System.String,System.String)">
            <summary>
                <para>Initializes a new instance of the DXDisplayNameAttribute class with the specified resource finder, resource file, resource name and default display name.

</para>
            </summary>
            <param name="resourceFinder">
		A <see cref="T:System.Type"/> of the class, which implements resource finding, if necessary. In general, this parameter is required for internal use, and you won't need to use it in your applications.

            </param>
            <param name="resourceFile">
		A <see cref="T:System.String"/> specifying the resource file, containing resources that represent display names.

            </param>
            <param name="resourceName">
		A <see cref="T:System.String"/> specifying the name of the resource that stores the display name for the current property.

            </param>
            <param name="defaultDisplayName">
		A <see cref="T:System.String"/> specifying the default display name, which is used when the specified resource file or resource in it isn't found.

            </param>


        </member>
        <member name="M:System.ComponentModel.DXDisplayNameAttribute.#ctor(System.Type,System.String,System.String)">
            <summary>
                <para>Initializes a new instance of the DXDisplayNameAttribute class with the specified resource finder, resource file and resource name.
</para>
            </summary>
            <param name="resourceFinder">
		A <see cref="T:System.Type"/> of the class, which implements resource finding, if necessary. In general, this parameter is required for internal use, and you won't need to use it in your applications.

            </param>
            <param name="resourceFile">
		A <see cref="T:System.String"/> specifying the resource file, containing resources that represent display names.

            </param>
            <param name="resourceName">
		A <see cref="T:System.String"/> specifying the name of the resource that stores the display name for the current property.

            </param>


        </member>
        <member name="F:System.ComponentModel.DXDisplayNameAttribute.DefaultResourceFile">
            <summary>
                <para>Returns the name of the default resource file containing display names (always "PropertyNamesRes").

</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="P:System.ComponentModel.DXDisplayNameAttribute.DisplayName">
            <summary>
                <para>Gets the display name for a property or event.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> representing the display name.
</value>


        </member>
        <member name="F:System.ComponentModel.DXDisplayNameAttribute.UseResourceManager">
            <summary>
                <para>Gets or sets a value indicating whether to use the resource manager to obtain the property display name, or use the real property name instead.
</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="T:DevExpress.Data.CustomSummaryEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraGrid.Views.Grid.GridView.CustomSummaryCalculate"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.CustomSummaryEventHandler.Invoke(System.Object,DevExpress.Data.CustomSummaryEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraGrid.Views.Grid.GridView.CustomSummaryCalculate"/> event.
</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Data.CustomSummaryEventArgs"/> object containing data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.Data.CustomSummaryEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraGrid.Views.Grid.GridView.CustomSummaryCalculate"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.CustomSummaryEventArgs.#ctor(System.Int32,System.Object,System.Object,System.Int32,DevExpress.Data.CustomSummaryProcess,System.Object,System.Int32)">
            <summary>
                <para>Creates a new <see cref="T:DevExpress.Data.CustomSummaryEventArgs"/> object.
</para>
            </summary>
            <param name="controllerRow">
		An integer value identifying the processed row by its handle. This value is assigned to the <see cref="P:DevExpress.Data.CustomSummaryEventArgs.RowHandle"/> property.

            </param>
            <param name="totalValue">
		An object representing the custom calculated summary value. This value is assigned to the <see cref="P:DevExpress.Data.CustomSummaryEventArgs.TotalValue"/> property.

            </param>
            <param name="fieldValue">
		An object representing the currently processed field value. This value is assigned to the <see cref="P:DevExpress.Data.CustomSummaryEventArgs.FieldValue"/> property.

            </param>
            <param name="groupRowHandle">
		An integer value identifying the group row whose child data rows are involved in summary calculation. This value is assigned to the <see cref="P:DevExpress.Data.CustomSummaryEventArgs.GroupRowHandle"/> property.

            </param>
            <param name="summaryProcess">
		A <see cref="T:DevExpress.Data.CustomSummaryProcess"/> enumeration value identifying the current calculation stage. This value is assigned to the <see cref="P:DevExpress.Data.CustomSummaryEventArgs.SummaryProcess"/> property.

            </param>
            <param name="item">
		An object representing the summary item whose value is being calculated. This value is assigned to the <see cref="P:DevExpress.Data.CustomSummaryEventArgs.Item"/> property.

            </param>
            <param name="groupLevel">
		An integer value indicating the nested group level of the processed row. This value is assigned to the <see cref="P:DevExpress.Data.CustomSummaryEventArgs.GroupLevel"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Data.CustomSummaryEventArgs.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.CustomSummaryEventArgs"/> class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Data.CustomSummaryEventArgs.FieldValue">
            <summary>
                <para>Gets the processed field value.
</para>
            </summary>
            <value>An object representing the processed field value.
</value>


        </member>
        <member name="M:DevExpress.Data.CustomSummaryEventArgs.GetGroupSummary(System.Int32,System.Object)">
            <summary>
                <para>Returns the value of the specified group summary for the specified group row.
</para>
            </summary>
            <param name="groupRowHandle">
		An integer that specifies a group row's handle.

            </param>
            <param name="summaryItem">
		An object representing the group summary item. In the GridControl, it's represented by the <see cref="T:DevExpress.XtraGrid.GridGroupSummaryItem"/> class.

            </param>
            <returns>The specified group summary's value.
</returns>


        </member>
        <member name="M:DevExpress.Data.CustomSummaryEventArgs.GetValue(System.String)">
            <summary>
                <para>Returns the value in the specified field
</para>
            </summary>
            <param name="fieldName">
		A string that identifies the field whose value must be returned.

            </param>
            <returns>An object that represents the value of the specified field.
</returns>


        </member>
        <member name="P:DevExpress.Data.CustomSummaryEventArgs.GroupLevel">
            <summary>
                <para>Gets the nested level of the group whose summary value is being calculated.
</para>
            </summary>
            <value>An integer value representing the zero-based nested level of the processed group.
</value>


        </member>
        <member name="P:DevExpress.Data.CustomSummaryEventArgs.GroupRowHandle">
            <summary>
                <para>Gets a value identifying the group row whose child data rows are involved in summary calculation.
</para>
            </summary>
            <value>An integer value representing the handle of the group row containing the processed row as a child. <B>0</B> when calculating a total summary value.
</value>


        </member>
        <member name="P:DevExpress.Data.CustomSummaryEventArgs.IsGroupSummary">
            <summary>
                <para>Gets whether a group summary value is being calculated.
</para>
            </summary>
            <value><B>true</B> if a group summary value is being calculated; otherwise, <B>false</B>.
</value>


        </member>
        <member name="P:DevExpress.Data.CustomSummaryEventArgs.IsTotalSummary">
            <summary>
                <para>Gets whether a total summary value is being calculated.
</para>
            </summary>
            <value><B>true</B> if a total summary value is being calculated; otherwise, <B>false</B>.
</value>


        </member>
        <member name="P:DevExpress.Data.CustomSummaryEventArgs.Item">
            <summary>
                <para>Gets a summary item whose value is being calculated.
</para>
            </summary>
            <value>An object representing a summary item whose value is being calculated.
</value>


        </member>
        <member name="P:DevExpress.Data.CustomSummaryEventArgs.RowHandle">
            <summary>
                <para>Gets the handle of the processed row.
</para>
            </summary>
            <value>An integer value identifying the processed data row by its handle.
</value>


        </member>
        <member name="P:DevExpress.Data.CustomSummaryEventArgs.SummaryProcess">
            <summary>
                <para>Gets a value indicating calculation stage.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Data.CustomSummaryProcess"/> enumeration value indicating calculation stage.
</value>


        </member>
        <member name="P:DevExpress.Data.CustomSummaryEventArgs.TotalValue">
            <summary>
                <para>Gets or sets the total summary value.
</para>
            </summary>
            <value>An object representing the total summary value.
</value>


        </member>
        <member name="P:DevExpress.Data.CustomSummaryEventArgs.TotalValueReady">
            <summary>
                <para>Gets or sets whether the Calculation stage of the custom summary calculation process should be skipped.
</para>
            </summary>
            <value><b>true</b> if the Calculation stage of the custom summary calculation process should be skipped; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.Data.CustomSummaryExistEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraGrid.Views.Grid.GridView.CustomSummaryExists"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.CustomSummaryExistEventHandler.Invoke(System.Object,DevExpress.Data.CustomSummaryExistEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraGrid.Views.Grid.GridView.CustomSummaryExists"/> event.
</para>
            </summary>
            <param name="sender">
		The event source.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Data.CustomSummaryExistEventArgs"/> object containing data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.Data.CustomSummaryExistEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraGrid.Views.Grid.GridView.CustomSummaryExists"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.CustomSummaryExistEventArgs.#ctor(System.Int32,System.Int32,System.Object)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.CustomSummaryExistEventArgs"/> class.
</para>
            </summary>
            <param name="groupRowHandle">
		An integer value specifying the handle of the group row whose summary value is about to be calculated. This value is assigned to the <see cref="P:DevExpress.Data.CustomSummaryExistEventArgs.GroupRowHandle"/> property.

            </param>
            <param name="groupLevel">
		An integer value specifying the zero-based nesting level of the processed group. This value is assigned to the <see cref="P:DevExpress.Data.CustomSummaryExistEventArgs.GroupLevel"/> property.

            </param>
            <param name="item">
		An object which represents the summary item whose value is about to be calculated. This value is assigned to the <see cref="P:DevExpress.Data.CustomSummaryExistEventArgs.Item"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Data.CustomSummaryExistEventArgs.Exists">
            <summary>
                <para>Gets or sets whether the summary value should be calculated and displayed.
</para>
            </summary>
            <value><B>true</B> to calculate and display the summary value; otherwise, <B>false</B>. 
</value>


        </member>
        <member name="P:DevExpress.Data.CustomSummaryExistEventArgs.GroupLevel">
            <summary>
                <para>Gets the nesting level of the group whose summary value is being calculated.
</para>
            </summary>
            <value>An integer value representing the zero-based nesting level of the processed group.
</value>


        </member>
        <member name="P:DevExpress.Data.CustomSummaryExistEventArgs.GroupRowHandle">
            <summary>
                <para>Gets a value identifying the group row whose summary value is about to be calculated.
</para>
            </summary>
            <value>An integer value representing the handle of the group row whose summary value is about to be calculated. <B>0</B> when calculating a total summary value.
</value>


        </member>
        <member name="P:DevExpress.Data.CustomSummaryExistEventArgs.IsGroupSummary">
            <summary>
                <para>Gets whether a group summary value is about to be calculated.
</para>
            </summary>
            <value><B>true</B> if a group summary value is about to be calculated; otherwise, <B>false</B>.
</value>


        </member>
        <member name="P:DevExpress.Data.CustomSummaryExistEventArgs.IsTotalSummary">
            <summary>
                <para>Gets whether a total summary value is about to be calculated.
</para>
            </summary>
            <value><B>true</B> if a total summary value is about to be calculated; otherwise, <B>false</B>.
</value>


        </member>
        <member name="P:DevExpress.Data.CustomSummaryExistEventArgs.Item">
            <summary>
                <para>Gets a summary item whose value is about to be calculated.
</para>
            </summary>
            <value>An object representing a summary item whose value is about to be calculated.
</value>


        </member>
        <member name="T:DevExpress.Data.CustomSummaryProcess">

            <summary>
                <para>Specifies the current status of custom summary calculation.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Data.CustomSummaryProcess.Calculate">
            <summary>
                <para>Indicates that custom summary calculation is in progress. This means the event is raised for a specific data row containing the field value used to calculate the processed custom summary.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.CustomSummaryProcess.Finalize">
            <summary>
                <para>Indicates that the process of custom summary calculation is finished. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.CustomSummaryProcess.Start">
            <summary>
                <para>Indicates that the process of custom summary calculation is about to be started. 

</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraSpellChecker.AfterCheckWordEventArgs">

            <summary>
                <para>Provides data for the <b>AfterCheckWord</b> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraSpellChecker.AfterCheckWordEventArgs.#ctor(System.String,System.String,DevExpress.XtraSpellChecker.SpellCheckOperation,DevExpress.XtraSpellChecker.Parser.Position)">
            <summary>
                <para>Initializes a new instance of the AfterCheckWordEventArgs class with specified settings.
</para>
            </summary>
            <param name="originalWord">
		A string, representing a word before the check.

            </param>
            <param name="changedWord">
		A string, representing the word modified after the spell check.


            </param>
            <param name="result">
		A <see cref="T:DevExpress.XtraSpellChecker.SpellCheckOperation"/> enumeration value, representing the operation type.

            </param>
            <param name="startPosition">
		A <see cref="T:DevExpress.XtraSpellChecker.Parser.Position"/> object, representing the position in a text where the checked word begins. 

            </param>


        </member>
        <member name="P:DevExpress.XtraSpellChecker.AfterCheckWordEventArgs.ChangedWord">
            <summary>
                <para>Gets the word which replaced the former one after the spelling check is performed.
</para>
            </summary>
            <value>A string, representing the word modified after spelling check.
</value>


        </member>
        <member name="P:DevExpress.XtraSpellChecker.AfterCheckWordEventArgs.Operation">
            <summary>
                <para>Gets the type of operation performed with the word in a spell check.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraSpellChecker.SpellCheckOperation"/> enumeration value, representing the operation type.

</value>


        </member>
        <member name="P:DevExpress.XtraSpellChecker.AfterCheckWordEventArgs.OriginalWord">
            <summary>
                <para>Gets the word before the spell check is performed.

</para>
            </summary>
            <value>A string, representing a word before the check.

</value>


        </member>
        <member name="P:DevExpress.XtraSpellChecker.AfterCheckWordEventArgs.StartPosition">
            <summary>
                <para>Gets the position of a checked word in a text.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraSpellChecker.Parser.Position"/> object, representing the position in a text where the checked word begins. 

</value>


        </member>
        <member name="T:DevExpress.Data.SummaryItemType">

            <summary>
                <para>Lists values specifying the aggregate function types.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Data.SummaryItemType.Average">
            <summary>
                <para>The average value of a column.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.SummaryItemType.Count">
            <summary>
                <para>The record count.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.SummaryItemType.Custom">
            <summary>
                <para>Specifies whether calculations should be performed manually using a specially designed event.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.SummaryItemType.Max">
            <summary>
                <para>The maximum value in a column.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.SummaryItemType.Min">
            <summary>
                <para>The minimum value in a column.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.SummaryItemType.None">
            <summary>
                <para>Disables summary value calculation.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.SummaryItemType.Sum">
            <summary>
                <para>The sum of all values in a column.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpo.DB.Exceptions.UnableToOpenDatabaseException">

            <summary>
                <para>An exception that is thrown when a database either cannot be created or opened.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.Exceptions.UnableToOpenDatabaseException.#ctor(System.String,System.Exception)">
            <summary>
                <para>Initializes a new instance of the UnableToOpenDatabaseException class with the specified settings.
</para>
            </summary>
            <param name="connectionString">
		A connection string that is used to connect to the database.

            </param>
            <param name="innerException">
		An <see cref="T:System.Exception"/> object that represents the inner error.

            </param>


        </member>
        <member name="T:DevExpress.Xpo.DB.Exceptions.UnableToCreateDBObjectException">

            <summary>
                <para>An exception that is thrown when a specific object cannot be created in a data store.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.Exceptions.UnableToCreateDBObjectException.#ctor(System.String,System.String,System.String,System.Exception)">
            <summary>
                <para>Initializes a new instance of the UnableToCreateDBObjectException class with the specified settings.
</para>
            </summary>
            <param name="objectTypeName">
		A string that specifies the type of the object that cannot be created.

            </param>
            <param name="objectName">
		A string that specifies the name of the object.

            </param>
            <param name="parentObjectName">
		A string that specifies the name of the object's parent.

            </param>
            <param name="innerException">
		An <see cref="T:System.Exception"/> object that represents the inner error.

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.Exceptions.UnableToCreateDBObjectException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
                <para>Sets the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> with information about the exception.
</para>
            </summary>
            <param name="info">
		The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.

            </param>
            <param name="context">
		The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.

            </param>


        </member>
        <member name="P:DevExpress.Xpo.DB.Exceptions.UnableToCreateDBObjectException.ObjectName">
            <summary>
                <para>Gets the name of the object that cannot be created.
</para>
            </summary>
            <value>The name of the object that cannot be created.
</value>


        </member>
        <member name="P:DevExpress.Xpo.DB.Exceptions.UnableToCreateDBObjectException.ObjectTypeName">
            <summary>
                <para>Gets the type of the object that cannot be created.
</para>
            </summary>
            <value>A string that specifies the type of the object that cannot be created.
</value>


        </member>
        <member name="P:DevExpress.Xpo.DB.Exceptions.UnableToCreateDBObjectException.ParentObjectName">
            <summary>
                <para>Gets the name of the parent which contains the object that cannot be created.
</para>
            </summary>
            <value>The name of the parent.
</value>


        </member>
        <member name="T:DevExpress.Xpo.DB.Exceptions.SqlExecutionErrorException">

            <summary>
                <para>An exception that is thrown when an unexpected error occurs during the execution of a SQL statement.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.Exceptions.SqlExecutionErrorException.#ctor(System.String,System.String,System.Exception)">
            <summary>
                <para>Initializes a new instance of the SqlExecutionErrorException class with the specified settings.
</para>
            </summary>
            <param name="sql">
		A string that specifies the SQL statement that raised the exception.

            </param>
            <param name="parameters">
		A string that specifies the parameters for the SQL statement.

            </param>
            <param name="innerException">
		An <see cref="T:System.Exception"/> object that represents the inner error.

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.Exceptions.SqlExecutionErrorException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
                <para>Sets the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> with information about the exception.
</para>
            </summary>
            <param name="info">
		The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.

            </param>
            <param name="context">
		The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.

            </param>


        </member>
        <member name="P:DevExpress.Xpo.DB.Exceptions.SqlExecutionErrorException.Parameters">
            <summary>
                <para>Gets a string that represents the parameters for the current SQL statement.

</para>
            </summary>
            <value>A string that represents the parameters for the SQL statement.
</value>


        </member>
        <member name="P:DevExpress.Xpo.DB.Exceptions.SqlExecutionErrorException.Sql">
            <summary>
                <para>Gets the SQL statement that raised the exception.
</para>
            </summary>
            <value>A string that specifies the SQL statement which raised the exception.
</value>


        </member>
        <member name="T:DevExpress.Xpo.DB.Exceptions.SchemaCorrectionNeededException">

            <summary>
                <para>An exception that is thrown when the storage schema doesn't match the structure of persistent objects and the schema cannot be modified.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.Exceptions.SchemaCorrectionNeededException.#ctor(System.String)">
            <summary>
                <para>Initializes a new instance of the SchemaCorrectionNeededException class .
</para>
            </summary>
            <param name="sql">
		A SQL statement or the name of the object that raised this exception.


            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.Exceptions.SchemaCorrectionNeededException.#ctor(System.Exception)">
            <summary>
                <para>Initializes a new instance of the SchemaCorrectionNeededException class.
</para>
            </summary>
            <param name="innerException">
		@nbsp

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.Exceptions.SchemaCorrectionNeededException.#ctor(System.String,System.Exception)">
            <summary>
                <para>Initializes a new instance of the SchemaCorrectionNeededException class.
</para>
            </summary>
            <param name="sql">
		@nbsp

            </param>
            <param name="innerException">
		@nbsp

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.Exceptions.SchemaCorrectionNeededException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
                <para>Sets the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> with information about the exception.
</para>
            </summary>
            <param name="info">
		The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.

            </param>
            <param name="context">
		The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.

            </param>


        </member>
        <member name="P:DevExpress.Xpo.DB.Exceptions.SchemaCorrectionNeededException.Sql">
            <summary>
                <para>A SQL statement or the name of an object that raised this exception.
</para>
            </summary>
            <value>A SQL statement or the name of an object that raised this exception.
</value>


        </member>
        <member name="T:DevExpress.Xpo.DB.Exceptions.PropertyTypeMappingMissingException">

            <summary>
                <para>An exception that is thrown when a connection provider cannot identify the type of column to create a column in a data store.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.Exceptions.PropertyTypeMappingMissingException.#ctor(System.Type)">
            <summary>
                <para>Initializes a new instance of the PropertyTypeMappingMissingException class with the specified settings.
</para>
            </summary>
            <param name="objtype">
		The type of column that cannot be identified.

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.Exceptions.PropertyTypeMappingMissingException.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
                <para>Sets the <see cref="T:System.Runtime.Serialization.SerializationInfo"/> with information about the exception.
</para>
            </summary>
            <param name="info">
		The <see cref="T:System.Runtime.Serialization.SerializationInfo"/> that holds the serialized object data about the exception being thrown.

            </param>
            <param name="context">
		The <see cref="T:System.Runtime.Serialization.StreamingContext"/> that contains contextual information about the source or destination.

            </param>


        </member>
        <member name="P:DevExpress.Xpo.DB.Exceptions.PropertyTypeMappingMissingException.PropertyType">
            <summary>
                <para>Gets the type of column that cannot be identified.
</para>
            </summary>
            <value>The type of column that cannot be identified.
</value>


        </member>
        <member name="T:DevExpress.Xpo.DB.Exceptions.LockingException">

            <summary>
                <para>An exception that is thrown when an attempt is made to modify a row in a database, but its version doesn't match the version of the row being posted.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.Exceptions.LockingException.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.Exceptions.LockingException"/> class.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpo.DB.ISqlDataStore">

            <summary>
                <para>Represents the interface for objects that need to interact with SQL Server databases,
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpo.DB.ISqlDataStore.Connection">
            <summary>
                <para>A <see cref="T:System.Data.IDbConnection"/> object that specifies the connection to a data store, if the specified data store allows commands to be created.

</para>
            </summary>
            <value>A <see cref="T:System.Data.IDbConnection"/> object that specifies the connection to the data store, if the specified data store allows commands to be created.

</value>


        </member>
        <member name="M:DevExpress.Xpo.DB.ISqlDataStore.CreateCommand">
            <summary>
                <para>Creates a command if the data store allows commands to be created.

</para>
            </summary>
            <returns>A <see cref="T:System.Data.IDbCommand"/> command that is executed when connected to a data store.
</returns>


        </member>
        <member name="T:DevExpress.Xpo.DB.UpdateStatement">

            <summary>
                <para>Represents the update statement.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.UpdateStatement.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.UpdateStatement"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpo.DB.UpdateStatement.#ctor(DevExpress.Xpo.DB.DBTable,System.String)">
            <summary>
                <para>Initializes a new instance of the UpdateStatement class with the specified settings.
</para>
            </summary>
            <param name="table">
		A <see cref="T:DevExpress.Xpo.DB.DBTable"/> object that represents the table.

            </param>
            <param name="alias">
		A <see cref="T:System.String"/> value that identifies the table. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.JoinNode.Alias"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.UpdateStatement.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		A UpdateStatement object to compare with the current object.

            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.
</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.UpdateStatement.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current UpdateStatement object.
</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.UpdateStatement.Parameters">
            <summary>
                <para>Gets the collection of <see cref="T:DevExpress.Data.Filtering.OperandValue"/> objects.
</para>
            </summary>
            <returns>@nbsp
</returns>


        </member>
        <member name="T:DevExpress.Xpo.DB.SelectStatementResult">

            <summary>
                <para>Represents the result of a single select query to a data store.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.SelectStatementResult.#ctor(System.Object[])">
            <summary>
                <para>Initializes a new instance of the SelectStatementResult class with the specified rows.
</para>
            </summary>
            <param name="testData">
		An array of objects that will be represented as the collection's elements.

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.SelectStatementResult.#ctor(System.Collections.ICollection)">
            <summary>
                <para>Initializes a new instance of the SelectStatementResult class with the specified collection of rows.
</para>
            </summary>
            <param name="rows">
		A collection of rows that will be copied to the collection being created.

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.SelectStatementResult.#ctor">
            <summary>
                <para>Initializes a new instance of the SelectStatementResult class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpo.DB.SelectStatementResult.#ctor(DevExpress.Xpo.DB.SelectStatementResultRow[])">
            <summary>
                <para>Initializes a new instance of the SelectStatementResult class with the specified collection of rows.
</para>
            </summary>
            <param name="rows">
		An array of rows that represent the result of a single select query to a data store. Each row contains the values of the queried fields. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.SelectStatementResult.Rows"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.SelectStatementResult.Clone">
            <summary>
                <para>Creates a copy of the current SelectStatementResult instance.
</para>
            </summary>
            <returns>A SelectStatementResult object which represents an exact copy of the current object.
</returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.SelectStatementResult.Rows">
            <summary>
                <para>An array of rows that represent the result of a single select query to a data store. Each row contains the values of the queried fields.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpo.DB.SelectStatement">

            <summary>
                <para>Represents the select statement.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.SelectStatement.#ctor(DevExpress.Xpo.DB.DBTable,System.String)">
            <summary>
                <para>Initializes a new instance of the SelectStatement class with the specified settings.
</para>
            </summary>
            <param name="table">
		A <see cref="T:DevExpress.Xpo.DB.DBTable"/> object that represents the table.

            </param>
            <param name="alias">
		A <see cref="T:System.String"/> value that identifies the table. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.JoinNode.Alias"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.SelectStatement.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.SelectStatement"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpo.DB.SelectStatement.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		A SelectStatement object to compare with the current object.

            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.
</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.SelectStatement.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current SelectStatement object.
</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.SelectStatement.GroupCondition">
            <summary>
                <para>Specifies the grouping expression that is represented by a <see cref="T:DevExpress.Data.Filtering.CriteriaOperator"/> descendant.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpo.DB.SelectStatement.GroupProperties">
            <summary>
                <para>Provides access to the <see cref="T:DevExpress.Data.Filtering.CriteriaOperatorCollection"/> that specifies the columns by which the result of the current SelectStatement should be grouped.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Data.Filtering.CriteriaOperatorCollection"/> that specifies the columns by which the result of the current <b>SelectStatement</b> should be grouped.


</value>


        </member>
        <member name="P:DevExpress.Xpo.DB.SelectStatement.SortProperties">
            <summary>
                <para>Provides access to the <see cref="T:DevExpress.Xpo.DB.QuerySortingCollection"/> collection.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpo.DB.QuerySortingCollection"/> object that represents the collection of <see cref="T:DevExpress.Xpo.DB.SortingColumn"/> objects.
</value>


        </member>
        <member name="F:DevExpress.Xpo.DB.SelectStatement.TopSelectedRecords">
            <summary>
                <para>Specifies the maximum number of selected records.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.SelectStatement.ToString">
            <summary>
                <para>Returns a string that represents the current object.
</para>
            </summary>
            <returns>A <see cref="T:System.String"/> that represents the current SelectStatement object.
</returns>


        </member>
        <member name="T:DevExpress.Xpo.DB.SelectedData">

            <summary>
                <para>Represents the result of batch select queries made to a data store. 

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.SelectedData.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.SelectedData"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpo.DB.SelectedData.#ctor(DevExpress.Xpo.DB.SelectStatementResult[])">
            <summary>
                <para>Initializes a new instance of the SelectedData class with the specified settings.
</para>
            </summary>
            <param name="resultSet">
		An array of <see cref="T:DevExpress.Xpo.DB.SelectStatementResult"/> objects that represent the result of select operations.

            </param>


        </member>
        <member name="F:DevExpress.Xpo.DB.SelectedData.ResultSet">
            <summary>
                <para>The results of select query statements.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpo.DB.ModificationStatement">

            <summary>
                <para>Serves as the base class for classes that represent modification statements.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.ModificationStatement.#ctor(DevExpress.Xpo.DB.DBTable,System.String)">
            <summary>
                <para>Initializes a new instance of the ModificationStatement class with the specified settings.
</para>
            </summary>
            <param name="table">
		A <see cref="T:DevExpress.Xpo.DB.DBTable"/> object that represents the table.

            </param>
            <param name="alias">
		A <see cref="T:System.String"/> value that identifies the table. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.JoinNode.Alias"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.ModificationStatement.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.ModificationStatement"/> class.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpo.DB.ModificationStatement.RecordsAffected">
            <summary>
                <para>Specifies the number of records to be modified.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.ModificationStatement.ToString">
            <summary>
                <para>Returns a string that represents the current object.
</para>
            </summary>
            <returns>A <see cref="T:System.String"/> that represents the current ModificationStatement object.
</returns>


        </member>
        <member name="T:DevExpress.Xpo.DB.ModificationResult">

            <summary>
                <para>Represents the result of an update operation on a data store.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.ModificationResult.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.ModificationResult"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpo.DB.ModificationResult.#ctor(DevExpress.Xpo.DB.ParameterValue[])">
            <summary>
                <para>Initializes a new instance of the ModificationResult class.
</para>
            </summary>
            <param name="identities">
		@nbsp

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.ModificationResult.#ctor(System.Collections.Generic.List`1)">
            <summary>
                <para>Initializes a new instance of the ModificationResult class.
</para>
            </summary>
            <param name="identities">
		@nbsp;

            </param>


        </member>
        <member name="F:DevExpress.Xpo.DB.ModificationResult.Identities">
            <summary>
                <para>An array of identifiers.
</para>
            </summary>
            <returns>@nbsp
</returns>


        </member>
        <member name="T:DevExpress.Xpo.DB.InsertStatement">

            <summary>
                <para>Represents the insert statement.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.InsertStatement.#ctor(DevExpress.Xpo.DB.DBTable,System.String)">
            <summary>
                <para>Initializes a new instance of the InsertStatement class with the specified settings.
</para>
            </summary>
            <param name="table">
		A <see cref="T:DevExpress.Xpo.DB.DBTable"/> object that represents the table.

            </param>
            <param name="alias">
		A <see cref="T:System.String"/> value that identifies the table. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.JoinNode.Alias"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.InsertStatement.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.InsertStatement"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpo.DB.InsertStatement.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		A InsertStatement object to compare with the current object.

            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.
</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.InsertStatement.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current InsertStatement object.
</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.InsertStatement.IdentityColumn">
            <summary>
                <para>Specifies the name of an identity column in a table.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.InsertStatement.IdentityParameter">
            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <returns>@nbsp
</returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.InsertStatement.Parameters">
            <summary>
                <para>Gets the collection of <see cref="T:DevExpress.Data.Filtering.OperandValue"/> objects.
</para>
            </summary>
            <returns>@nbsp
</returns>


        </member>
        <member name="T:DevExpress.Xpo.DB.DeleteStatement">

            <summary>
                <para>Represents the delete statement.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.DeleteStatement.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.DeleteStatement"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpo.DB.DeleteStatement.#ctor(DevExpress.Xpo.DB.DBTable,System.String)">
            <summary>
                <para>Initializes a new instance of the DeleteStatement class with the specified settings.
</para>
            </summary>
            <param name="table">
		A <see cref="T:DevExpress.Xpo.DB.DBTable"/> object that represents the table.

            </param>
            <param name="alias">
		A <see cref="T:System.String"/> value that identifies the table. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.JoinNode.Alias"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.DeleteStatement.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		A DeleteStatement object to compare with the current object.

            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.
</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.DeleteStatement.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current DeleteStatement object.
</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="T:DevExpress.Xpo.DB.DataStoreLogger">

            <summary>
                <para>Enables operations performed by a specific <see cref="T:DevExpress.Xpo.DB.IDataStore"/> object to be logged.


</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.DataStoreLogger.#ctor(DevExpress.Xpo.DB.IDataStore,System.IO.TextWriter)">
            <summary>
                <para>Initializes a new instance of the DataStoreLogger class with the specified settings.
</para>
            </summary>
            <param name="nestedProvider">
		A <see cref="T:DevExpress.Xpo.DB.IDataStore"/> object that represents a tracked data store.

            </param>
            <param name="logWriter">
		A <see cref="T:System.IO.TextWriter"/> object that will log data-aware operations performed on the tracked data store

            </param>


        </member>
        <member name="P:DevExpress.Xpo.DB.DataStoreLogger.LogWriter">
            <summary>
                <para>Gets a writer that logs the data-aware operations performed on the tracked data store.

</para>
            </summary>
            <value>A <see cref="T:System.IO.TextWriter"/> object that logs the data-aware operations performed on the tracked data store

</value>


        </member>
        <member name="T:DevExpress.XtraSpellChecker.AfterCheckWordEventHandler">

            <summary>
                <para>Represents a method that will handle the <b>AfterCheckWord</b> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraSpellChecker.AfterCheckWordEventHandler.Invoke(System.Object,DevExpress.XtraSpellChecker.AfterCheckWordEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraSpellChecker.SpellCheckerBase.AfterCheckWord"/> event.
</para>
            </summary>
            <param name="sender">
		An object that triggers the <see cref="E:DevExpress.XtraSpellChecker.SpellCheckerBase.AfterCheckWord"/> event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraSpellChecker.AfterCheckWordEventArgs"/> object that provides data for the <see cref="E:DevExpress.XtraSpellChecker.SpellCheckerBase.AfterCheckWord"/> event.

            </param>


        </member>
        <member name="T:DevExpress.Xpo.DB.BaseStatement">

            <summary>
                <para>Serves as the base class for the classes that represent statements.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpo.DB.BaseStatement.#ctor(DevExpress.Xpo.DB.DBTable,System.String)">
            <summary>
                <para>Initializes a new instance of the BaseStatement class with the specified settings.
</para>
            </summary>
            <param name="table">
		A <see cref="T:DevExpress.Xpo.DB.DBTable"/> object that represents the table.

            </param>
            <param name="alias">
		A <see cref="T:System.String"/> value that identifies the table. This value is assigned to the <see cref="F:DevExpress.Xpo.DB.JoinNode.Alias"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpo.DB.BaseStatement.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpo.DB.BaseStatement"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpo.DB.BaseStatement.Equals(System.Object)">
            <summary>
                <para>Determines whether the current object has the same settings as the specified object.
</para>
            </summary>
            <param name="obj">
		A BaseStatement object to compare with the current object.

            </param>
            <returns><B>true</B> if the object specified by the parameter has the same settings as the current object; otherwise, <B>false</B>.
</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.BaseStatement.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current BaseStatement object.
</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.BaseStatement.GetTablesColumns(DevExpress.Xpo.DB.BaseStatement[])">
            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="statements">
		@nbsp

            </param>
            <returns>@nbsp
</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.BaseStatement.GetTablesNames(DevExpress.Xpo.DB.BaseStatement[])">
            <summary>
                <para>Returns the names of the tables that are referenced in the specified statements.

</para>
            </summary>
            <param name="statements">
		An array of <see cref="T:DevExpress.Xpo.DB.BaseStatement"/> descendants that represents statements.

            </param>
            <returns>An array of strings that specify the names of the tables that are referenced in the specified statements.

</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.BaseStatement.GetTablesNames">
            <summary>
                <para>Returns the names of the tables that are referenced in the statement.

</para>
            </summary>
            <returns>An array of strings that specify the names of the tables that are referenced in the statement.

</returns>


        </member>
        <member name="F:DevExpress.Xpo.DB.BaseStatement.Operands">
            <summary>
                <para>The collection of operands.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Data.ColumnSortOrder">

            <summary>
                <para>Lists values specifying the sort orders.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Data.ColumnSortOrder.Ascending">
            <summary>
                <para>Sorts the column in ascending order.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.ColumnSortOrder.Descending">
            <summary>
                <para>Sorts the columns in descending order.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.ColumnSortOrder.None">
            <summary>
                <para>No sorting is applied to a column.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpo.DB.IDataStore">

            <summary>
                <para>Represents the interface for objects that need to interact with a data store.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpo.DB.IDataStore.AutoCreateOption">
            <summary>
                <para>When implemented by a class, returns which operations are performed when a data store is accessed for the first time.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpo.DB.AutoCreateOption"/> value that specifies which operations are performed when a data store is accessed for the first time.
</value>


        </member>
        <member name="M:DevExpress.Xpo.DB.IDataStore.ModifyData(DevExpress.Xpo.DB.ModificationStatement[])">
            <summary>
                <para>Updates data in a data store using the specified modification statements.
</para>
            </summary>
            <param name="dmlStatements">
		An array of data modification statements.

            </param>
            <returns>The result of the data modifications.
</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.IDataStore.SelectData(DevExpress.Xpo.DB.SelectStatement[])">
            <summary>
                <para>When implemented by a class, fetches data from a data store using the specified query statements.
</para>
            </summary>
            <param name="selects">
		An array of statements to obtain data from the data store.

            </param>
            <returns>Data retrieved from the data store.
</returns>


        </member>
        <member name="M:DevExpress.Xpo.DB.IDataStore.UpdateSchema(System.Boolean,DevExpress.Xpo.DB.DBTable[])">
            <summary>
                <para>When implemented by a class, updates the storage schema according to the specified class descriptions.
</para>
            </summary>
            <param name="dontCreateIfFirstTableNotExist">
		<b>true</b> if the schema should not be created if the table that corresponds to the first item in the <i>tables</i> array doesn't exist in the data store.

            </param>
            <param name="tables">
		An array of tables whose structure should be saved in the data store.

            </param>
            <returns>An <see cref="T:DevExpress.Xpo.DB.UpdateSchemaResult"/> value that specifies the result of the update operation.

</returns>


        </member>
        <member name="T:DevExpress.XtraGrid.ColumnFilterMode">

            <summary>
                <para>Contains values that specify how a column's data is filtered via the auto filter row and filter dropdown.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraGrid.ColumnFilterMode.DisplayText">
            <summary>
                <para>A column's data is filtered by the display text.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraGrid.ColumnFilterMode.Value">
            <summary>
                <para>A column's data is filtered by the edit values.

</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.OptionsLayoutGrid">

            <summary>
                <para>Contains options that specify how a control's layout is stored to and restored from storage (a stream, xml file or system registry).
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.OptionsLayoutGrid.#ctor">
            <summary>
                <para>Initializes a new instance of the OptionsLayoutGrid class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.OptionsLayoutGrid.Assign(DevExpress.Utils.Controls.BaseOptions)">
            <summary>
                <para>Copies all the settings from the options object passed as the parameter.

</para>
            </summary>
            <param name="options">
		A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.

            </param>


        </member>
        <member name="P:DevExpress.Utils.OptionsLayoutGrid.Columns">
            <summary>
                <para>Contains options that specify how the columns' and bands' settings are stored to and restored from storage (a stream, xml file or sysytem registry).
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.OptionsColumnLayout"/> object that specifies how the columns' and bands' settings are stored to and restored from storage.

</value>


        </member>
        <member name="M:DevExpress.Utils.OptionsLayoutGrid.Reset">
            <summary>
                <para>Resets all options to their default values.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.OptionsLayoutGrid.StoreAllOptions">
            <summary>
                <para>Gets or sets whether all the control's settings (except for the appearance settings and style conditions) are stored when the layout is saved to storage and restored when the layout is restored from storage.
</para>
            </summary>
            <value><b>true</b> if all the control's settings are included in the layout when it's saved to storage and these settings are restored when the layout is restored from the storage; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Utils.OptionsLayoutGrid.StoreAppearance">
            <summary>
                <para>Gets or sets whether the control's appearance settings are also stored when the layout is saved to storage and restored when the layout is restored from storage.
</para>
            </summary>
            <value><b>true</b> if the control's appearance settings are included in the layout when it's saved to storage and these settings are restored when the layout is restored from storage; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Utils.OptionsLayoutGrid.StoreDataSettings">
            <summary>
                <para>Gets or sets whether the control's grouping, sorting, filtering settings and summaries are stored when the layout is saved to storage and restored when the layout is restored from storage.
</para>
            </summary>
            <value><b>true</b> if the control's grouping, sorting, filtering settings and summaries are included in the layout when it's saved to storage and these settings are restored when the layout is restored from storage; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Utils.OptionsLayoutGrid.StoreVisualOptions">
            <summary>
                <para>Gets or sets whether the control's visual options are stored when the layout is saved to storage and restored when the layout is restored from storage.
</para>
            </summary>
            <value><b>true</b> if the control's visual options are included in the layout when it's saved to storage and these settings are restored when the layout is restored from storage; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.Utils.OptionsLayoutBase">

            <summary>
                <para>Represents the base class for classes which contain the options that are responsible for how the control's layout is stored to and restored from storage (a stream, xml file or system registry).
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.OptionsLayoutBase.#ctor">
            <summary>
                <para>Initializes a new instance of the OptionsLayoutBase class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.OptionsLayoutBase.FullLayout">
            <summary>
                <para>Returns an OptionsLayoutBase object whose settings indicate that the full layout of the control should be stored to and restored from storage (a stream, xml file or system registry).

</para>
            </summary>
            <value>A OptionsLayoutBase object whose settings indicate that the full layout of the control should be stored to/restored from storage.
</value>


        </member>
        <member name="P:DevExpress.Utils.OptionsLayoutBase.LayoutVersion">
            <summary>
                <para>Gets or sets the version of the layout.

</para>
            </summary>
            <value>A string representing the version of the layout.

</value>


        </member>
        <member name="M:DevExpress.Utils.OptionsLayoutBase.ShouldSerializeCore(System.ComponentModel.IComponent)">
            <summary>
                <para>Gets whether the settings should be persisted.
</para>
            </summary>
            <param name="owner">
		The component that owns the current settings.

            </param>
            <returns><b>true</b> if the settings should be persisted; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="T:DevExpress.Utils.OptionsColumnLayout">

            <summary>
                <para>Contains options that specify how column specific settings are stored to and restored from storage (a stream, xml file or the system registry).
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.OptionsColumnLayout.#ctor">
            <summary>
                <para>Initializes a new instance of the OptionsColumnLayout class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.OptionsColumnLayout.AddNewColumns">
            <summary>
                <para>Gets or sets whether the columns that exist in the current control but do not exist in a layout when it's restored should be retained.

</para>
            </summary>
            <value><b>true</b> to retain the columns that exist in the current control's layout but don't exist in the layout being restored; <b>false</b> to destroy such columns.

</value>


        </member>
        <member name="M:DevExpress.Utils.OptionsColumnLayout.Assign(DevExpress.Utils.Controls.BaseOptions)">
            <summary>
                <para>Copies all the settings from the options object passed as the parameter.

</para>
            </summary>
            <param name="options">
		A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.

            </param>


        </member>
        <member name="P:DevExpress.Utils.OptionsColumnLayout.RemoveOldColumns">
            <summary>
                <para>Gets or sets whether the columns that exist in a layout when it's restored but that don't exist in the current control should be discarded or added to the control.

</para>
            </summary>
            <value><b>true</b> to discard the columns that exist in the layout being restored but don't exist in the current control; <b>false</b> to add these columns to the control.

</value>


        </member>
        <member name="P:DevExpress.Utils.OptionsColumnLayout.StoreAllOptions">
            <summary>
                <para>Gets or sets whether all the settings of a control's columns/bands (except for the appearance settings) are stored when the layout is saved to storage and restored when the layout is restored from storage.
</para>
            </summary>
            <value><b>true</b> if all the settings of a control's columns/bands are included in the layout when it's saved to storage and these settings are restored when the layout is restored from storage; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.Utils.OptionsColumnLayout.StoreAppearance">
            <summary>
                <para>Gets or sets whether the appearance settings of the columns and bands are also stored when the layout is saved to storage and restored when the layout is restored from storage.
</para>
            </summary>
            <value><b>true</b> if the appearance settings of the columns and bands are included in the layout when it's saved to storage and these settings are restored when the layout is restored from storage; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Utils.OptionsColumnLayout.StoreLayout">
            <summary>
                <para>Gets or sets whether the position, width and visibility of the columns and bands are stored when the layout is saved to storage and restored when the layout is restored from storage.
</para>
            </summary>
            <value><b>true</b> if the position, width and visibility of columns and bands are included in the layout when it's saved to storage and these settings are restored when the layout is restored from storage; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.Utils.DefaultBoolean">

            <summary>
                <para>Lists values which specify the validity of a condition (indicates whether the condition is true or false).
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Utils.DefaultBoolean.Default">
            <summary>
                <para>The value is determined by a control's current setting. The default value is automatically set for a control if an end-user doesn't specify a value.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.DefaultBoolean.False">
            <summary>
                <para>Corresponds to a Boolean value of <b>false</b>.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.DefaultBoolean.True">
            <summary>
                <para>Corresponds to a Boolean value of <b>true</b>.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Data.SelectionChangedEventArgs">

            <summary>
                <para>Provides data for the selection changed events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Data.SelectionChangedEventArgs.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.SelectionChangedEventArgs"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Data.SelectionChangedEventArgs.#ctor(System.ComponentModel.CollectionChangeAction,System.Int32)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Data.SelectionChangedEventArgs"/> class with the specified settings.
</para>
            </summary>
            <param name="action">
		A <see cref="T:System.ComponentModel.CollectionChangeAction"/> enumeration value which specifies how the collection has been changed. This value is assigned to the <see cref="P:DevExpress.Data.SelectionChangedEventArgs.Action"/> property.

            </param>
            <param name="controllerRow">
		A zero-based integer specifying the handle of the row whose selected state has been changed. This value is assigned to the <see cref="P:DevExpress.Data.SelectionChangedEventArgs.ControllerRow"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Data.SelectionChangedEventArgs.Action">
            <summary>
                <para>Gets an action which describes how the collection has been changed.
</para>
            </summary>
            <value>A <see cref="T:System.ComponentModel.CollectionChangeAction"/> enumeration value which specifies how the collection has been changed.
</value>


        </member>
        <member name="P:DevExpress.Data.SelectionChangedEventArgs.ControllerRow">
            <summary>
                <para>Identifies the row whose selected state has been changed.
</para>
            </summary>
            <value>A zero-based integer identifying the row whose selected state has been changed.
</value>


        </member>
        <member name="T:DevExpress.Data.UnboundColumnType">

            <summary>
                <para>Contains values that specify the data type and binding mode of columns.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Data.UnboundColumnType.Boolean">
            <summary>
                <para>Indicates that the column is unbound and it contains Boolean values (the <see cref="T:System.Boolean"/> type).
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.UnboundColumnType.Bound">
            <summary>
                <para>Indicates that the column is bound to a field in the control's underlying data source. The type of data this column contains is determined by the bound field.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.UnboundColumnType.DateTime">
            <summary>
                <para>Indicates that the column is unbound and it contains date/time values (the <see cref="T:System.DateTime"/> type).
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.UnboundColumnType.Decimal">
            <summary>
                <para>Indicates that the column is unbound and it contains decimal values (the <see cref="T:System.Decimal"/> type).
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.UnboundColumnType.Integer">
            <summary>
                <para>Indicates that the column is unbound and it contains integer values (the <see cref="T:System.Int32"/> type).
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.UnboundColumnType.Object">
            <summary>
                <para>Indicates that the column is unbound and it contains values of any type. A <see cref="T:DevExpress.XtraEditors.TextEdit"/> editor is assigned for the in-place editing of such a column.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Data.UnboundColumnType.String">
            <summary>
                <para>Indicates that the column is unbound and it contains string values (the <see cref="T:System.String"/> type). 
</para>
            </summary>


        </member>
    </members>
</doc>
