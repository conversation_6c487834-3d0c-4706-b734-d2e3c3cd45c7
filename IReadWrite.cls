VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
END
Attribute VB_Name = "IReadWrite"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = True
Option Explicit

Public Enum SeekPos
    OriginF
    CurrentF
    EndF
End Enum

' 读取操作
Public Function Read(b() As Byte) As Long
End Function

Public Function ReadAt(b() As Byte, ByVal offset As Long) As Long
End Function

Public Function ReadByte() As Byte
End Function

Public Function ReadInteger() As Integer
End Function

Public Function ReadLong() As Long
End Function

Public Function ReadDate() As Date
End Function

' 定位操作
Public Function SeekFile(ByVal offsetZeroBase As Long, ByVal whence As SeekPos) As Long
End Function

' 写入操作
Public Function WriteFile(b() As Byte) As Long
End Function

Public Function WriteLong(ByVal l As Long) As Long
End Function
