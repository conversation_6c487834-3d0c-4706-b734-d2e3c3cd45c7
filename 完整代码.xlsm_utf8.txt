Attribute VB_Name = "CBytes"
Attribute VB_Base = "0{FCFB3D2A-A0FA-1068-A738-08002B3371B5}"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
Attribute VB_TemplateDerived = False
Attribute VB_Customizable = False
Option Explicit

Implements IReadWrite

'下标为0的Byte数组
Private bSrc() As Byte
'当前读取的下标
Private pos As Long
'数组的长度
Private lLen As Long

'设置数据源
Property Let SetData(v() As Byte)
    bSrc = v
    lLen = UBound(bSrc) + 1
End Property
'获取数据源
Property Get GetData() As Byte()
    GetData = bSrc
End Property

'找到第一个sep出现的位置
'Brute Force（暴力算法）
Function IndexBF(b() As Byte, sep() As Byte) As Long
    Dim iseplen As Long
    iseplen = UBound(sep) + 1
    
    Dim ilen As Long
    ilen = UBound(b) + 1
    
    Dim i As Long, j As Long
    For i = 0 To ilen - iseplen
        For j = 0 To iseplen - 1
            If b(i + j) <> sep(j) Then
                Exit For
            End If
        Next
        
        If j = iseplen Then
            IndexBF = i
            Exit Function
        End If
    Next
    
    IndexBF = -1
End Function

'找到第一个sep出现的位置
Function Index(b() As Byte, sep() As Byte) As Long
    Dim iseplen As Long
    iseplen = UBound(sep) + 1
    
    If iseplen = 1 Then
        Index = IndexByte(b, sep(0))
        Exit Function
    End If
    
    Index = KMP(b, sep)
End Function

'找到第一个sep出现的位置
Function IndexByte(b() As Byte, sep As Byte) As Long
    Dim i As Long
    
    For i = LBound(b) To UBound(b)
        If b(i) = sep Then
            IndexByte = i
            Exit Function
        End If
    Next
    
    IndexByte = -1
End Function

Private Function KMP(b() As Byte, sep() As Byte) As Long
    Dim ilen As Long
    Dim iseplen As Long
    
    ilen = UBound(b) + 1
    iseplen = UBound(sep) + 1
    
    Dim nextarr() As Long
    
    nextarr = KMPNext(sep)
    
    Dim i As Long
    Dim j As Long
    For i = 0 To ilen - 1
        Do While j > 0 And b(i) <> sep(j)
            j = nextarr(j)
        Loop
        
        If b(i) = sep(j) Then j = j + 1
        
        If j = iseplen Then
            KMP = i - iseplen + 1
            Exit Function
        End If
    Next
    
    KMP = -1
End Function

'根据要查找的数组，生成next数组
Private Function KMPNext(b() As Byte) As Long()
    Dim ilen As Long
    
    ilen = UBound(b) + 1
    
    Dim nextarr() As Long
    ReDim nextarr(ilen) As Long
    
    Dim j As Long
    Dim i As Long
    
    For i = 2 To ilen - 1
        Do While j > 0 And b(j) <> b(i - 1)
            j = nextarr(j)
        Loop
        
        If b(j) = b(i - 1) Then
            j = j + 1
        End If
        
        nextarr(i) = j
    Next
    
    KMPNext = nextarr
End Function

Function Read(b() As Byte) As Long
    Dim i As Long
    Dim ilen As Long
    
    ilen = UBound(b) + 1
    If ilen + pos > lLen Then ilen = lLen + (lLen - pos)
    
    For i = 0 To ilen - 1
        b(i) = ReadByte()
    Next
    
    Read = ilen
End Function
Function ReadAt(b() As Byte, offset As Long) As Long
    pos = offset
    ReadAt = Read(b)
End Function
Function ReadByte() As Byte
    If pos >= lLen Then
        Exit Function
    End If
    
    ReadByte = bSrc(pos)
    pos = pos + 1
End Function
Function ReadInteger() As Integer
    Dim b1 As Byte, b2 As Byte
    
    b1 = ReadByte()
    b2 = ReadByte()
    
    ReadInteger = BitMoveLeftInt(VBA.CInt(b2), 8) Or VBA.CInt(b1)
End Function
Function ReadLong() As Long
    Dim l(4 - 1) As Long
    Dim i As Long
    
    For i = 0 To 4 - 1
        l(i) = VBA.CLng(ReadByte())
    Next
    
    ReadLong = BitMoveLeft(l(3), 3 * 8) Or BitMoveLeft(l(2), 2 * 8) Or BitMoveLeft(l(1), 1 * 8) Or l(0)
End Function

Function ReadDate() As Date
    Dim l(8 - 1) As Long
    Dim i As Long
    
    For i = 0 To 8 - 1
        l(i) = VBA.CLng(ReadByte())
    Next
    
    'TODO 待完成
    ReadDate = 0
End Function

Function SeekFile(offsetZeroBase As Long, whence As SeekPos) As Long
    If whence = SeekPos.OriginF Then
        pos = offsetZeroBase
    ElseIf whence = SeekPos.CurrentF Then
        pos = pos + offsetZeroBase
    Else
        pos = lLen - offsetZeroBase - 1
    End If
    
    SeekFile = pos
End Function
Function WriteFile(b() As Byte) As Long
    'TODO 待完成
End Function
Function WriteLong(l As Long) As Long
    'TODO 待完成
End Function

'实现接口
Private Function IReadWrite_Read(b() As Byte) As Long
    IReadWrite_Read = Read(b)
End Function
Private Function IReadWrite_ReadAt(b() As Byte, offset As Long) As Long
    IReadWrite_ReadAt = ReadAt(b, offset)
End Function
Private Function IReadWrite_ReadByte() As Byte
    IReadWrite_ReadByte = ReadByte()
End Function
Private Function IReadWrite_ReadDate() As Date
    IReadWrite_ReadDate = ReadDate()
End Function
Private Function IReadWrite_ReadInteger() As Integer
    IReadWrite_ReadInteger = ReadInteger()
End Function
Private Function IReadWrite_ReadLong() As Long
    IReadWrite_ReadLong = ReadLong()
End Function
Private Function IReadWrite_SeekFile(offsetZeroBase As Long, whence As SeekPos) As Long
    IReadWrite_SeekFile = SeekFile(offsetZeroBase, whence)
End Function
Private Function IReadWrite_WriteFile(b() As Byte) As Long
    IReadWrite_WriteFile = WriteFile(b)
End Function
Private Function IReadWrite_WriteLong(l As Long) As Long
    IReadWrite_WriteLong = WriteLong(l)
End Function


Attribute VB_Name = "CCompoundFile"
Attribute VB_Base = "0{FCFB3D2A-A0FA-1068-A738-08002B3371B5}"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = True
Attribute VB_TemplateDerived = False
Attribute VB_Customizable = False
Option Explicit

'参考文章
'http://club.excelhome.net/thread-227502-1-1.html
'https://docs.microsoft.com/en-us/openspecs/windows_protocols/ms-cfb/53989ce4-7b05-4f8d-829b-d08d6148375b


Private Const CFHEADER_SIZE As Long = 2 ^ 9
Private Const DIR_SIZE As Long = 128

'Specifies an unallocated sector in the FAT, Mini FAT, or DIFAT
Private Const Free_SID As Long = -1

'End of a linked chain of sectors
Private Const End_Of_Chain_SID As Long = -2

'Specifies a FAT sector in the FAT.
Private Const FAT_SID As Long = -3

'Specifies a DIFAT sector in the FAT
Private Const DIFAT_SID As Long = -4

Private Type CFHeader
    Signature(7) As Byte               '文档标识id
    CLSID(15) As Byte                  '文件唯一标识
    MinorVersion As Integer            '文件格式修订号
    MajorVersion As Integer            '文件格式版本号
    ByteOrder As Integer               'FFFE表示 Little-Endian
    SectorShift As Integer             '扇区的大小 2的幂 通常为2^9=512
    MiniSectorShift As Integer         '短扇区大小，2的幂,通常为2^6
    Reserved(5) As Byte
    DirSectorsCount As Long             '目录的数量
    FATSectorsCount As Long             '分区表扇区的总数
    FirstDirSID As Long                 '目录流第一个扇区的ID
    TransactionSignatureNumber  As Long '
    MiniStreamSize As Long              '最小标准流
    FirstMiniFATSID As Long             '短分区表的第一个扇区ID
    MiniFATSectorsCount As Long         '短分区表扇区总数
    FirstDIFATSID As Long               '主分区表的第一个扇区ID
    DIFATSectorsCount As Long           '分区表的扇区总数
    DIFATS(108) As Long                 '主分区表前109个记录
End Type

Private Type CFDir
    EntryName(63) As Byte
    EntryNameLen As Integer
    ObjectType As Byte                    '1仓storage 2流 5根
    ColorFlag As Byte                     '0红色 1黑色
    LeftSiblingID As Long                 '-1表示叶子
    RightSiblingID As Long
    ChildID As Long
    CLSID(16 - 1) As Byte
    StateBits As Long
    CreationTime As Date
    ModifiedTime As Date
    StartingSectorID As Long               '目录入口所表示的第1个扇区编码
    StreamSize As Long                     '目录入口流尺寸，可判断是否是短扇区
    not_used As Long    '我是32位office，读不了64位整数！
    
    '这个不是结构体的字段
    StrDirName As String
    '在文件中的偏移位置
    lOffset As Long
End Type

Private Type cf
    r As IReadWrite
    '扇区的大小
    lSectorSize As Long
    '短扇区的大小
    lShortSectorSize As Long
    '每个扇区能够存放的4字节long类型个数
    longNumPerSector As Long
    '每个扇区存放的短流个数
    ssNumPerSector As Long
    
    '文件头
    Header As CFHeader
    '主分区表数组，指向的是存储分区表的SID
    FAT() As Long
    '分区表数组，指向的是下一个SID
    DIFAT() As Long
     '短分区表数据，直接记录偏移位置
    MiniFAT() As Long
     '目录
    ArrDir() As CFDir
    
    '记录dir中文件名称的下标
    h As CHash
End Type

'所有目录的全名称
Private ArrDirsName() As String

Private cf As cf
'

Property Get DirsName() As String()
    DirsName = ArrDirsName
End Property

Private Function GetDirsName() As String
    Dim i As Long
    ReDim ArrDirsName(UBound(cf.ArrDir)) As String
    
    For i = 0 To UBound(cf.ArrDir)
        ArrDirsName(i) = cf.ArrDir(i).StrDirName
    Next
End Function


'解析复合文档，获取复合文档文件信息
'ir         一个实现了IReadWrite接口的类
'Return     返回出错信息
Function Parse(ir As IReadWrite) As String
    Set cf.r = ir
    
    Dim ret As String
    ret = parseCfHeader()
    If VBA.Len(ret) Then
        Parse = ret
        Exit Function
    End If
    
    ret = parseDIFAT()
    If VBA.Len(ret) Then
        Parse = ret
        Exit Function
    End If
    
    ret = parseFAT()
    If VBA.Len(ret) Then
        Parse = ret
        Exit Function
    End If
    
    ret = parseDir()
    If VBA.Len(ret) Then
        Parse = ret
        Exit Function
    End If
    
    ret = parseMiniFAT()
    If VBA.Len(ret) Then
        Parse = ret
        Exit Function
    End If
End Function

'读取文件头
Private Function parseCfHeader() As String
    Dim iret As Long
    
    iret = cf.r.Read(cf.Header.Signature)
    If iret <> 8 Then
        parseCfHeader = "复合文档：文件头id读取出错"
        Exit Function
    End If
    
    Dim arr()
    arr = Array(208, 207, 17, 224, 161, 177, 26, 225)
    Dim i As Long
    For i = 0 To 8 - 1
        If cf.Header.Signature(i) <> arr(i) Then
            parseCfHeader = "复合文档：文件头id出错"
            Exit Function
        End If
    Next
    
    cf.r.Read cf.Header.CLSID '文件唯一标识
    cf.Header.MinorVersion = cf.r.ReadInteger '文件格式修订号
    cf.Header.MajorVersion = cf.r.ReadInteger  '文件格式版本号
    cf.Header.ByteOrder = cf.r.ReadInteger        'FFFE表示 Little-Endian
    cf.Header.SectorShift = cf.r.ReadInteger          '扇区的大小 2的幂 通常为2^9=512
    cf.Header.MiniSectorShift = cf.r.ReadInteger    '短扇区大小，2的幂,通常为2^6
    cf.r.Read cf.Header.Reserved
    cf.Header.DirSectorsCount = cf.r.ReadLong()
    cf.Header.FATSectorsCount = cf.r.ReadLong               '分区表扇区的总数
    cf.Header.FirstDirSID = cf.r.ReadLong           '目录流第一个扇区的ID
    cf.Header.TransactionSignatureNumber = cf.r.ReadLong()
    cf.Header.MiniStreamSize = cf.r.ReadLong         '最小标准流
    cf.Header.FirstMiniFATSID = cf.r.ReadLong          '短分区表的第一个扇区ID
    cf.Header.MiniFATSectorsCount = cf.r.ReadLong              '短分区表扇区总数
    cf.Header.FirstDIFATSID = cf.r.ReadLong          '主分区表的第一个扇区ID
    cf.Header.DIFATSectorsCount = cf.r.ReadLong              '分区表的扇区总数
    '主分区表前109个记录
    For i = 0 To 109 - 1
        cf.Header.DIFATS(i) = cf.r.ReadLong
    Next
    
    If cf.Header.ByteOrder <> -2 Then
        parseCfHeader = "复合文档：memory endian 不是小端"
        Exit Function
    End If
    If cf.Header.MiniSectorShift <> 6 Then
        parseCfHeader = "复合文档：The sector size of the Mini Stream MUST be 64 bytes"
        Exit Function
    End If
    
    If cf.Header.MajorVersion = 3 Then
        If cf.Header.SectorShift <> &H9 Then
            parseCfHeader = "复合文档：If Major Version is 3, the Sector Shift MUST be 0x0009, specifying a sector size of 512 bytes."
            Exit Function
        End If
    
    ElseIf cf.Header.MajorVersion = 4 Then
        If cf.Header.SectorShift <> &HC Then
            parseCfHeader = "复合文档：If Major Version is 4, the Sector Shift MUST be 0x000C, specifying a sector size of 4,096 bytes."
            Exit Function
        End If
        
    Else
        parseCfHeader = "复合文档：Major Version must by 3 or 4."
        Exit Function
    End If
    
    cf.lSectorSize = 2 ^ cf.Header.SectorShift
    cf.longNumPerSector = cf.lSectorSize \ 4
End Function

'读取主扇区配置表（Main allocator of space within the compound file）
'是一个SID数组
'数组的值n代表FAT占用的第n个Sector扇区
Private Function parseDIFAT() As String
    Dim i As Long
    Dim next_SID As Long
    Dim flag As Boolean
    Dim Count As Long

    ReDim cf.DIFAT(cf.Header.FATSectorsCount - 1) As Long

    '获取头文件中的109个
    For i = 0 To 109 - 1
        If cf.Header.DIFATS(i) = -1 Then
            '头中并没有109个，小于6.875M的文件肯定是没有的
            Exit Function
        End If

        cf.DIFAT(i) = cf.Header.DIFATS(i)
    Next i

    '获取另外的
    Count = 109
    next_SID = cf.Header.FirstDIFATSID
    flag = True
    Dim tmp As Long
    
    Do
        '设置读取的位置
        cf.r.SeekFile getOffsetBySID(next_SID), OriginF
        '每一个sector扇区，512字节（128×4），能够存储127个DIFAT，第128个指向下一个SID(sector ID)
        For i = 0 To cf.longNumPerSector - 1 - 1
            tmp = cf.r.ReadLong()
            If tmp = Free_SID Then
                flag = False
                Exit For
            End If

            cf.DIFAT(Count) = tmp
            Count = Count + 1
        Next i
        
        next_SID = cf.r.ReadLong()  'SID的最后4个字节存储再下一个的SID
    Loop While flag

End Function

'读取扇区配置表（Used to locate FAT sectors in the compound file）
'FAT是扇区编号的数组
'数组的值代表下一个SID
Private Function parseFAT() As String
    Dim i As Long, j As Long
    Dim Count As Long
    Dim arr(127) As Long

    '每一个sector扇区，512字节（128×4），能够存储128个FAT
    ReDim cf.FAT(cf.Header.FATSectorsCount * cf.longNumPerSector - 1) As Long

    Count = 0
    For i = 0 To cf.Header.FATSectorsCount - 1
        '设置读取的位置
        cf.r.SeekFile getOffsetBySID(cf.DIFAT(i)), OriginF
        
        For j = 0 To cf.longNumPerSector - 1
            cf.FAT(Count) = cf.r.ReadLong()
            Count = Count + 1
        Next j
    Next i
End Function

'解析目录
Private Function parseDir() As String
    Dim l_sub_dir As Long
    Dim l_SID As Long
    Dim k As Long
    Dim i As Long
    Dim lOffset As Long
    
    l_SID = cf.Header.FirstDirSID

    k = 0
    Do
        lOffset = getOffsetBySID(l_SID)
        '设置读取的位置
        cf.r.SeekFile lOffset, OriginF
        
        ReDim Preserve cf.ArrDir(k + 4 - 1) As CFDir
        For i = 0 To 4 - 1
            cf.r.Read cf.ArrDir(k + i).EntryName
            cf.ArrDir(k + i).EntryNameLen = cf.r.ReadInteger()
            '名称长度为0就可以退出了
            If cf.ArrDir(k + i).EntryNameLen = 0 Then Exit Do
            
            cf.ArrDir(k + i).ObjectType = cf.r.ReadByte()                  '1仓storage 2流 5根
            cf.ArrDir(k + i).ColorFlag = cf.r.ReadByte()                  '0红色 1黑色
            cf.ArrDir(k + i).LeftSiblingID = cf.r.ReadLong()             '-1表示叶子
            cf.ArrDir(k + i).RightSiblingID = cf.r.ReadLong()
            cf.ArrDir(k + i).ChildID = cf.r.ReadLong()
            cf.r.Read cf.ArrDir(k + i).CLSID
            cf.ArrDir(k + i).StateBits = cf.r.ReadLong()
            cf.ArrDir(k + i).CreationTime = cf.r.ReadDate()
            cf.ArrDir(k + i).ModifiedTime = cf.r.ReadDate()
            cf.ArrDir(k + i).StartingSectorID = cf.r.ReadLong()             '目录入口所表示的第1个扇区编码
            cf.ArrDir(k + i).StreamSize = cf.r.ReadLong()           '目录入口流尺寸，可判断是否是短扇区
            cf.ArrDir(k + i).not_used = cf.r.ReadLong()
            
            
            If cf.ArrDir(k + i).EntryName(0) <= 5 Then
                cf.ArrDir(k + i).StrDirName = VBA.CStr(cf.ArrDir(k + i).EntryName(0))
                cf.ArrDir(k + i).EntryName(0) = VBA.Asc("]")
                cf.ArrDir(k + i).StrDirName = "[" & cf.ArrDir(k + i).StrDirName & VBA.Left$(cf.ArrDir(k + i).EntryName, cf.ArrDir(k + i).EntryNameLen \ 2 - 1)
                
            Else
                cf.ArrDir(k + i).StrDirName = VBA.Left$(cf.ArrDir(k + i).EntryName, cf.ArrDir(k + i).EntryNameLen \ 2 - 1) '-1包含结尾的0
            End If
            
            cf.ArrDir(k + i).lOffset = lOffset
            lOffset = lOffset + DIR_SIZE
            
        Next
    
        k = k + 4
        l_SID = cf.FAT(l_SID)
    Loop Until l_SID = End_Of_Chain_SID
    
    '去掉最后的一个空白
    ReDim Preserve cf.ArrDir(k + i - 1) As CFDir
    
    recordDir
    
    GetDirsName
End Function

'记录dir的完整名称到hash
Private Function recordDir() As String
    '记录目录名称的Hash
    Set cf.h = NewCHash(UBound(cf.ArrDir) + 1)
    
    RrecordDir cf.ArrDir(0), "", 0
End Function
Private Function RrecordDir(d As CFDir, preDir As String, dirIndex As Long)
    cf.h.Add preDir & d.StrDirName, dirIndex
    d.StrDirName = preDir & d.StrDirName
    
    If d.LeftSiblingID = -1 And d.RightSiblingID = -1 And d.ChildID = -1 Then
        Exit Function
    End If
    
    If d.LeftSiblingID <> -1 Then RrecordDir cf.ArrDir(d.LeftSiblingID), preDir, d.LeftSiblingID
    If d.RightSiblingID <> -1 Then RrecordDir cf.ArrDir(d.RightSiblingID), preDir, d.RightSiblingID
    If d.ChildID <> -1 Then RrecordDir cf.ArrDir(d.ChildID), d.StrDirName & Application.PathSeparator, d.ChildID
End Function

'读取短扇区配置表（Allocator for mini stream  user-defined data）
'是一个SID数组
Private Function parseMiniFAT() As String
    Dim l_SID As Long
    Dim i As Long, j As Long

    If cf.Header.MiniFATSectorsCount = 0 Then Exit Function
    
    cf.lShortSectorSize = 2 ^ cf.Header.MiniSectorShift
    cf.ssNumPerSector = cf.lSectorSize / cf.lShortSectorSize
    
'    根目录的 stream_size 表示短流存放流的大小，每64个为一个short sector
    ReDim cf.MiniFAT(cf.ArrDir(0).StreamSize / cf.lShortSectorSize - 1) As Long

    l_SID = cf.Header.FirstMiniFATSID    '短流起始SID
    
    For i = 0 To UBound(cf.MiniFAT) Step cf.longNumPerSector
        '设置读取的位置
        cf.r.SeekFile getOffsetBySID(l_SID), OriginF
        
        For j = 0 To cf.longNumPerSector - 1
            cf.MiniFAT(i + j) = cf.r.ReadLong()
            
            If i + j = UBound(cf.MiniFAT) Then Exit For
        Next
        
        l_SID = cf.FAT(l_SID)
    Next

End Function

'改写数据流
'dir_name   需要被改写的文件名称，是复合文档中的文件名称
'WriteBytes 需要改写为的数据Byte数组
'Return     返回出错信息
Function ReWriteStream(dir_name As String, WriteBytes() As Byte) As String
    If cf.h.Exists(dir_name) Then
        ReWriteStream = ReWriteStreamByDirIndex(VBA.CLng(cf.h.GetItem(dir_name)), WriteBytes)
    Else
        ReWriteStream = "复合文档：不存在的目录"
        Exit Function
    End If
End Function

Private Function ReWriteStreamByDirIndex(dirIndex As Long, WriteBytes() As Byte) As String
    '1仓storage 2流 5根
    If cf.ArrDir(dirIndex).ObjectType <> 2 Then
        ReWriteStreamByDirIndex = "复合文档：不是数据流"
        Exit Function
    End If
    If cf.ArrDir(dirIndex).StartingSectorID = Free_SID Then
        ReWriteStreamByDirIndex = "复合文档：流的大小为0"
        Exit Function
    End If
    
    Dim ilen As Long
    ilen = UBound(WriteBytes) + 1
    
    If cf.ArrDir(dirIndex).StreamSize < cf.Header.MiniStreamSize Then
        ReWriteStreamByDirIndex = ReWriteStreamMiniFAT(dirIndex, WriteBytes)
    Else
        ReWriteStreamByDirIndex = ReWriteStreamFAT(dirIndex, WriteBytes)
    End If
    
    '改写目录stream_size
    cf.ArrDir(dirIndex).StreamSize = ilen
    cf.r.SeekFile cf.ArrDir(dirIndex).lOffset + DIR_SIZE - 8, OriginF
    cf.r.WriteLong ilen
End Function

Private Function ReWriteStreamMiniFAT(dirIndex As Long, WriteBytes() As Byte) As String
    Dim ilen As Long
    ilen = UBound(WriteBytes) + 1
    
    '如果改写的数据超过了原来的范围，并且超越了扇区，需要报错
    If ilen > cf.ArrDir(dirIndex).StreamSize Then
        If ilen \ cf.lShortSectorSize > cf.ArrDir(dirIndex).StreamSize \ cf.lShortSectorSize Then
            ReWriteStreamMiniFAT = "复合文档：改写的数据超过了原来的范围，并且超越了扇区"
            Exit Function
        End If
    End If
    
    Dim b() As Byte
    ReDim b(cf.lShortSectorSize - 1) As Byte
    
    '找到改写开始的位置
    Dim miniSID As Long
    miniSID = cf.ArrDir(dirIndex).StartingSectorID
    
    Dim i As Long
    Dim p As Long
    
    Do Until miniSID = End_Of_Chain_SID
        '设置改写的位置
        cf.r.SeekFile getOffsetByMiniFATSID(miniSID), OriginF
        
        For i = 0 To cf.lShortSectorSize - 1
            b(i) = WriteBytes(p)
            p = p + 1
            
            If p = ilen Then
                ReDim Preserve b(p Mod cf.lShortSectorSize - 1) As Byte
                cf.r.WriteFile b
                Exit Function
            End If
        Next
        cf.r.WriteFile b
        
        '下一个扇区
        miniSID = cf.MiniFAT(miniSID)
    Loop
End Function

Private Function ReWriteStreamFAT(dirIndex As Long, WriteBytes() As Byte) As String
    Dim ilen As Long
    ilen = UBound(WriteBytes) + 1
    
    '如果改写的数据超过了原来的范围，并且超越了扇区，需要报错
    If ilen > cf.ArrDir(dirIndex).StreamSize Then
        If ilen \ cf.lSectorSize > cf.ArrDir(dirIndex).StreamSize \ cf.lSectorSize Then
            ReWriteStreamFAT = "复合文档：改写的数据超过了原来的范围，并且超越了扇区"
            Exit Function
        End If
    End If
    
    '找到改写开始的位置
    Dim sid As Long
    sid = cf.ArrDir(dirIndex).StartingSectorID
    
    Dim i As Long
    Dim p As Long
    Dim b() As Byte
    ReDim b(cf.lSectorSize - 1) As Byte
    
    Do
        '设置改写的位置
        cf.r.SeekFile getOffsetBySID(sid), OriginF
        
        For i = 0 To cf.lSectorSize - 1
            b(i) = WriteBytes(p)
            p = p + 1
            
            If p = ilen Then
                ReDim Preserve b(p Mod cf.lSectorSize - 1) As Byte
                cf.r.WriteFile b
                Exit Function
            End If
        Next
        cf.r.WriteFile b

        '下一个扇区
        sid = cf.FAT(sid)
    Loop
End Function

'获取所有Stream，并保存为文件
'SavePath   保存文件的路径
'Return     返回出错信息
Function GetAllStream(SavePath As String) As String
    Dim ret As String
    Dim i As Long
    Dim b() As Byte
    Dim arrindex As Long
    
    If VBA.Right$(SavePath, 1) <> Application.PathSeparator Then SavePath = SavePath & Application.PathSeparator
    
    For i = 0 To UBound(cf.ArrDir)
        '1仓storage 2流 5根
        If cf.ArrDir(i).ObjectType = 2 Then
            ret = GetStream(cf.ArrDir(i).StrDirName, b)
            If VBA.Len(ret) Then
                GetAllStream = ret
                Exit Function
            End If
            
            ByteToFile makeDir(SavePath, cf.ArrDir(i).StrDirName), b
        End If
    Next
End Function


'读取某个数据流
'dir_name   需要读取的文件名称，是复合文档中的文件名称
'RetBytes   返回解压后的数据Byte数组
'Return     返回出错信息
Function GetStream(dir_name As String, RetBytes() As Byte) As String
    If cf.h.Exists(dir_name) Then
        GetStream = GetStreamByDirIndex(VBA.CLng(cf.h.GetItem(dir_name)), RetBytes)
    Else
        GetStream = "复合文档：不存在的目录"
        Exit Function
    End If
End Function
'读取数据流
Private Function GetStreamByDirIndex(dirIndex As Long, RetBytes() As Byte) As String
    '1仓storage 2流 5根
    If cf.ArrDir(dirIndex).ObjectType <> 2 Then
        GetStreamByDirIndex = "复合文档：不是数据流"
        Exit Function
    End If
    If cf.ArrDir(dirIndex).StartingSectorID = Free_SID Then
        GetStreamByDirIndex = "复合文档：流的大小为0"
        Exit Function
    End If
    
    ReDim RetBytes(cf.ArrDir(dirIndex).StreamSize - 1) As Byte
    If cf.ArrDir(dirIndex).StreamSize < cf.Header.MiniStreamSize Then
        GetStreamByDirIndex = GetStreamMiniFAT(dirIndex, RetBytes)
    Else
        GetStreamByDirIndex = GetStreamFAT(dirIndex, RetBytes)
    End If
    
End Function

'按照FAT读取数据
Private Function GetStreamFAT(dirIndex As Long, RetBytes() As Byte) As String
    Dim b() As Byte
    ReDim b(cf.lSectorSize - 1) As Byte
    
    '找到读取开始的位置
    Dim sid As Long
    sid = cf.ArrDir(dirIndex).StartingSectorID
    
    Dim i As Long
    Dim p As Long
    
    Do
        '设置读取的位置
        cf.r.SeekFile getOffsetBySID(sid), OriginF
        
        cf.r.Read b
        For i = 0 To cf.lSectorSize - 1
            RetBytes(p) = b(i)
            p = p + 1
            If p = cf.ArrDir(dirIndex).StreamSize Then Exit Function
        Next
        
        '下一个扇区
        sid = cf.FAT(sid)
    Loop
    
End Function

'按照MiniFAT读取数据
Private Function GetStreamMiniFAT(dirIndex As Long, RetBytes() As Byte) As String
    Dim b() As Byte
    ReDim b(cf.lShortSectorSize - 1) As Byte
    
    '找到读取开始的位置
    Dim miniSID As Long
    miniSID = cf.ArrDir(dirIndex).StartingSectorID
    
    Dim i As Long
    Dim p As Long
    
    Do Until miniSID = End_Of_Chain_SID
        '设置读取的位置
        cf.r.SeekFile getOffsetByMiniFATSID(miniSID), OriginF
        
        cf.r.Read b
        For i = 0 To cf.lShortSectorSize - 1
            RetBytes(p) = b(i)
            p = p + 1
            If p = cf.ArrDir(dirIndex).StreamSize Then Exit Do
        Next
        
        '下一个扇区
        miniSID = cf.MiniFAT(miniSID)
    Loop
End Function

'根据sid计算文件偏移位置
Private Function getOffsetBySID(ByVal sid As Long) As Long
    getOffsetBySID = CFHEADER_SIZE + sid * cf.lSectorSize
End Function
Private Function getOffsetByMiniFATSID(ByVal miniSID As Long) As Long
    Dim sid As Long
    
    '每个512Byte扇区，存放512/64=8个MiniFAT，所有每8个miniSID要转换一下SID
    sid = cf.ArrDir(0).StartingSectorID
    Do Until miniSID < cf.ssNumPerSector
        miniSID = miniSID - cf.ssNumPerSector
        sid = cf.FAT(sid)
    Loop
    
    getOffsetByMiniFATSID = CFHEADER_SIZE + sid * cf.lSectorSize + miniSID * cf.lShortSectorSize
End Function

Private Sub Class_Terminate()
    Erase cf.FAT, cf.DIFAT, cf.MiniFAT, cf.ArrDir
    
    Set cf.h = Nothing
End Sub

Private Function makeDir(SavePath As String, DirName As String)
    Dim arr
    Dim i As Long
    Dim tmp As String
    
    tmp = SavePath
    If VBA.Len(VBA.Dir(tmp, vbDirectory)) = 0 Then
        VBA.MkDir tmp
    End If
        
    arr = VBA.Split(DirName, Application.PathSeparator)
    For i = 0 To UBound(arr) - 1
        tmp = tmp & arr(i) & Application.PathSeparator
        If VBA.Len(VBA.Dir(tmp, vbDirectory)) = 0 Then
            VBA.MkDir tmp
        End If
    Next
    
    makeDir = tmp & arr(i)
End Function

Private Function ByteToFile(file_name As String, b() As Byte)
    Dim iFreefile As Integer
    
    iFreefile = VBA.FreeFile()
    Open file_name For Binary As iFreefile
    Put #iFreefile, 1, b
    Close iFreefile
End Function
Attribute VB_Name = "CFile"
Attribute VB_Base = "0{FCFB3D2A-A0FA-1068-A738-08002B3371B5}"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
Attribute VB_TemplateDerived = False
Attribute VB_Customizable = False
Option Explicit

Implements IReadWrite

Public Enum OpenAccess
    O_RDONLY
    O_WRONLY
    O_RDWR
End Enum

'注意大文件long类型会溢出
Private lFileLen As Long
Private num_file As Integer

Property Get GetSeek() As Long
    GetSeek = VBA.Seek(num_file)
End Property


'写入文件
Function WriteFile(b() As Byte) As Long
    Put #num_file, , b
End Function
Function WriteLong(l As Long) As Long
    Put #num_file, , l
End Function
Function WriteInteger(l As Integer) As Long
    Put #num_file, , l
End Function


'读取整个文件
Function ReadAll() As Byte()
    Dim b() As Byte
    ReDim b(lFileLen) As Byte
    
    Me.SeekFile 0, OriginF
    
    Me.Read b
    
    ReadAll = b
End Function

'读取len(b)个byte
Function Read(b() As Byte) As Long
    Dim ilen As Long
    ilen = UBound(b) - LBound(b) + 1
    
    Dim iseek As Long
    iseek = VBA.Seek(num_file)
    If iseek + ilen > lFileLen Then
        ilen = lFileLen - iseek + 1
    End If
    
    Get #num_file, , b
    
    Read = ilen
End Function
Function ReadByte() As Byte
    Dim i As Byte
    Get #num_file, , i
    ReadByte = i
End Function
'读取一个2Byte的整数
Function ReadInteger() As Integer
    Dim i As Integer
    Get #num_file, , i
    ReadInteger = i
End Function
'读取1个4Byte的整数
Function ReadLong() As Long
    Dim i As Long
    Get #num_file, , i
    ReadLong = i
End Function
'读取1个8Byte的Date
Function ReadDate() As Date
    Dim i As Date
    Get #num_file, , i
    ReadDate = i
End Function

'在offset处开始读取
Function ReadAt(b() As Byte, offset As Long) As Long
    SeekFile offset, 0
    ReadAt = Read(b)
End Function

'设置读取的位置
Function SeekFile(offset As Long, whence As SeekPos) As Long
    Dim iseek As Long
    iseek = VBA.Seek(num_file)
    
    'vba Seek是下标1开始
    If whence = SeekPos.OriginF Then
        iseek = 1 + offset
    ElseIf whence = SeekPos.CurrentF Then
        iseek = iseek + offset
    Else
        iseek = 1 + lFileLen - offset
    End If
    
    Seek #num_file, iseek
    
    SeekFile = iseek
End Function

'以字节方式读取文本
Function OpenFile(Filename As String, Optional m As OpenAccess = OpenAccess.O_RDWR) As Long
    '避免多次调用OpenFile的时候，前面的文件未关闭
    If num_file Then Close #num_file
    
    num_file = VBA.FreeFile
    
    Select Case m
        Case OpenAccess.O_RDONLY
        Open Filename For Binary Access Read As #num_file
        
        Case OpenAccess.O_WRONLY
        Open Filename For Binary Access Write As #num_file
        
        Case OpenAccess.O_RDWR
        Open Filename For Binary Access Read Write As #num_file
        
        Case Else
        
    End Select
    
    
    lFileLen = VBA.FileLen(Filename)
End Function

Function CloseFile()
    Close #num_file
End Function

Private Sub Class_Terminate()
    CloseFile
End Sub

Private Function IReadWrite_Read(b() As Byte) As Long
    IReadWrite_Read = Me.Read(b)
End Function

Private Function IReadWrite_ReadAt(b() As Byte, offset As Long) As Long
    IReadWrite_ReadAt = Me.ReadAt(b, offset)
End Function

Private Function IReadWrite_ReadByte() As Byte
    IReadWrite_ReadByte = Me.ReadByte()
End Function

Private Function IReadWrite_ReadDate() As Date
    IReadWrite_ReadDate = ReadDate()
End Function

Private Function IReadWrite_ReadInteger() As Integer
    IReadWrite_ReadInteger = Me.ReadInteger()
End Function

Private Function IReadWrite_ReadLong() As Long
    IReadWrite_ReadLong = Me.ReadLong()
End Function

Private Function IReadWrite_SeekFile(offsetZeroBase As Long, whence As SeekPos) As Long
    IReadWrite_SeekFile = Me.SeekFile(offsetZeroBase, whence)
End Function

Private Function IReadWrite_WriteFile(b() As Byte) As Long
    IReadWrite_WriteFile = Me.WriteFile(b)
End Function

Private Function IReadWrite_WriteLong(l As Long) As Long
    IReadWrite_WriteLong = Me.WriteLong(l)
End Function


Attribute VB_Name = "CHash"
Attribute VB_Base = "0{FCFB3D2A-A0FA-1068-A738-08002B3371B5}"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
Attribute VB_TemplateDerived = False
Attribute VB_Customizable = False
Option Explicit


Const MUL_FACTOR As Long = 211

Private Type HashNode
    Key As String
    Item As String
End Type

Private arr() As HashNode
Private ArrSize As Long     '数组的最大个数
Private KeyCount As Long    '实际的key的个数
Private KeyMaxCount As Long '最大的key的个数

Const dSCALE As Double = 1.2 '数组放大的比例

Property Let MaxCount(Value As Long)
    KeyMaxCount = Value
    ArrSize = Value * dSCALE
   
    Do Until IsPrime(ArrSize)
        ArrSize = ArrSize + 1
    Loop
   
    ReDim arr(ArrSize) As HashNode
End Property

Property Get Count() As Long
    Count = KeyCount
End Property

Property Get Keys() As String()
    Dim tmp() As String
    ReDim tmp(KeyCount - 1) As String
    
    Dim i As Long
    Dim p As Long
    For i = 0 To ArrSize - 1
        If VBA.Len(arr(i).Key) Then
            tmp(p) = arr(i).Key
            p = p + 1
        End If
    Next
    
    Keys = tmp
End Property

Sub Add(Key As String, Optional Item As Variant = "")
    Dim i_index As Long
   
    i_index = GetIndex(Key)
   
    '找到hash对应的空位置（添加），'或者是找到已经存在的key（替换Item）
    If VBA.Len(arr(i_index).Key) = 0 Then
        If KeyCount >= KeyMaxCount Then
            MsgBox "已达到Key的最大数量，不能添加。"
            Exit Sub
        End If
        KeyCount = KeyCount + 1
       
        arr(i_index).Key = Key
    End If
   
    arr(i_index).Item = Item
End Sub

Function GetItem(Key As String) As Variant
    Dim i_index As Long
   
    i_index = GetIndex(Key)
    '没找到的时候添加Key
    If VBA.Len(arr(i_index).Key) = 0 Then Add (Key)
    GetItem = arr(i_index).Item
End Function

Function Remove(Key As String)
    Dim i_index As Long
   
    i_index = GetIndex(Key)
    If VBA.Len(arr(i_index).Key) Then KeyCount = KeyCount - 1
    arr(i_index).Key = ""
    arr(i_index).Item = ""
End Function

Function Exists(Key As String) As Boolean
    Dim i_index As Long
   
    i_index = GetIndex(Key)
    Exists = (Key = arr(i_index).Key)
End Function
'找到Key所在数组Arr的下标，或者是找到hash对应的空位置
Private Function GetIndex(Key As String) As Long
    Dim i_index As Long
   
    i_index = Hash(Key) Mod ArrSize
   
    Do Until VBA.Len(arr(i_index).Key) = 0 Or arr(i_index).Key = Key
        i_index = Collision(i_index)
    Loop
    GetIndex = i_index
End Function
'处理冲突的方法
Private Function Collision(i_index As Long) As Long
    Collision = (i_index + 1) Mod ArrSize
End Function

Private Function Hash(Key As String) As Long
    Dim i As Long
    Const m As Long = 7158271

    Hash = 5381
    For i = 1 To Len(Key)
        Hash = (MUL_FACTOR * Hash + AscW(Mid$(Key, i, 1))) Mod m
    Next i
    Hash = Math.Abs(Hash)
End Function

'除留余数法
'f(key) = key mod p (p<=m) m是散列表长
'p通常为小于或等于m的最小质数
'Function Hash(key_num As Long, p As Long) As Long
'    Hash = key_num * MUL_FACTOR Mod p
'End Function

Private Function IsPrime(num As Long) As Boolean '判断一个数是否是质数
    Dim temp As Long
    Dim i As Long
   
    If num Mod 2 Then
        temp = Math.Sqr(num)
        For i = 2 To temp
            If (num Mod i) = 0 Then
                IsPrime = False
                Exit Function
            End If
        Next i
        IsPrime = True
    Else
        IsPrime = False
    End If
End Function

Private Sub Class_Initialize()
    Me.MaxCount = 10
End Sub

Private Sub Class_Terminate()
    Erase arr
End Sub



Attribute VB_Name = "IReadWrite"
Attribute VB_Base = "0{FCFB3D2A-A0FA-1068-A738-08002B3371B5}"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = True
Attribute VB_TemplateDerived = False
Attribute VB_Customizable = False
Option Explicit

Public Enum SeekPos
    OriginF
    CurrentF
    EndF
End Enum

Function Read(b() As Byte) As Long

End Function
Function ReadByte() As Byte

End Function
Function ReadInteger() As Integer

End Function
Function ReadLong() As Long

End Function
Function ReadDate() As Date

End Function
'在offset处开始读取
Function ReadAt(b() As Byte, offset As Long) As Long

End Function

'设置读取的位置
Function SeekFile(offsetZeroBase As Long, whence As SeekPos) As Long

End Function

'写入文件
Function WriteFile(b() As Byte) As Long
    
End Function
Function WriteLong(l As Long) As Long
    
End Function



Attribute VB_Name = "MAPI"
Option Explicit


'#If VBA7 Then
'
'Public Declare PtrSafe Function GetCurrentThreadId Lib "kernel32" () As Long
'
'#Else

Public Declare Function GetCurrentThreadId Lib "kernel32" () As Long
Public Declare Function LoadLibrary Lib "kernel32" Alias "LoadLibraryA" (ByVal lpLibFileName As String) As Long
Public Declare Function FreeLibrary Lib "kernel32" (ByVal hLibModule As Long) As Long
Public Declare Sub CopyMemory Lib "kernel32" Alias "RtlMoveMemory" (ByVal Destination As Long, ByVal Source As Long, ByVal Length As Long)

'#End If


Public Declare Function gosprintf Lib "godllForVBA32.dll" (ByVal pFormat As Long, ByVal pVBAVariant As Long, ByVal nCount As Long) As MyString
Public Declare Function sum Lib "godllForVBA32.dll" (ByVal a As Long, ByVal b As Long) As Long
Private Declare Sub cfree Lib "godllForVBA32.dll" (ByVal p As Long)
Private Declare Function retVarPtr Lib "godllForVBA32.dll" () As Long


Type MyString
    pUCS2 As Long
    Len As Long
End Type


Private Const b_1000_0000 As Byte = 128
Private Const b_1100_0000 As Byte = 192
Private Const b_1110_0000 As Byte = 224
Private Const b_1111_0000 As Byte = 240
Private Const b_0001_1100 As Byte = 28
Private Const b_0000_0111 As Byte = 7
Private Const b_0000_0011 As Byte = 3
Private Const b_0011_1111 As Byte = 63
Private Const b_0000_1111 As Byte = 15
Private Const b_0011_1100 As Byte = 60
Private Const b_0000_0010 As Byte = 2

Public Function NewCBytes() As CBytes
    Set NewCBytes = New CBytes
'    NewCBytes.SetData = b  '内部错误？
End Function

'// UCS-2转UTF-8
'// 1 对于不大于0x007F（即00000000 01111111）的，直接把它转成一个字节，变成ASCII
'// 2 对于不大于0x07FF（即00000111 11111111）的，转换成两个字节
'//   转换的时候把右边的11位分别放到110xxxxx 10yyyyyy里边
'//   即0000 0aaa bbbb bbbb ==> 110a aabb   10bb bbbb
'// 3 剩下的会转换成三个字节，转换的时候也是把16个位分别填写到那三个字节里面
'//   即aaaaaaaa bbbbbbbb ==> 1110 aaaa   10aa aabb   10bb bbbb
Function ToUTF8(SrcUCS2() As Byte, RetUTF8() As Byte) As String
    Dim ilensrc As Long
    ilensrc = UBound(SrcUCS2) + 1
    
    If ilensrc < 2 Then
        ToUTF8 = "输入的UCS2字节数组太小了！"
        Exit Function
    End If
    
    Dim i As Long
    Dim iStart As Long
    '如果是从txt文件中读取的，可能会有BOM头
    If SrcUCS2(i) = &HFF And SrcUCS2(i + 1) = &HFE Then
        iStart = 2
    End If
    
    If ilensrc Mod 2 Then
        ToUTF8 = "输入的UCS2字节数组不是偶数！"
        Exit Function
    End If
    
    ReDim RetUTF8(ilensrc / 2 * 3 - 1) As Byte
    Dim p As Long
    
    Dim tmp As Long
    Dim l1 As Long, l2 As Long
    For i = iStart To ilensrc - 1 Step 2
        l1 = VBA.CLng(SrcUCS2(i + 1))
        l2 = VBA.CLng(SrcUCS2(i))
        
        tmp = l1 * 2 ^ 8 Or l2
        
        If tmp <= &H7F Then
            RetUTF8(p) = VBA.CByte(tmp)
            p = p + 1
        ElseIf tmp <= &H7FF Then
            RetUTF8(p) = b_1100_0000 Or (SrcUCS2(i + 1) * (2 ^ 2)) Or (SrcUCS2(i) \ (2 ^ 6))
            p = p + 1
            
            RetUTF8(p) = b_1000_0000 Or (SrcUCS2(i) And b_0011_1111)
            p = p + 1
        Else
            RetUTF8(p) = b_1110_0000 Or (SrcUCS2(i + 1) \ (2 ^ 4))
            p = p + 1
                
            RetUTF8(p) = b_1000_0000 Or ((SrcUCS2(i + 1) And b_0000_1111) * (2 ^ 2)) Or (SrcUCS2(i) \ (2 ^ 6))
            p = p + 1
            
            RetUTF8(p) = b_1000_0000 Or (SrcUCS2(i) And b_0011_1111)
            p = p + 1
        End If
    Next
    
    ReDim Preserve RetUTF8(p - 1) As Byte
End Function

Function FromUTF8(SrcUTF8() As Byte, RetUCS2() As Byte) As String
    Dim ilensrc As Long
    ilensrc = UBound(SrcUTF8) + 1
    
    Dim i As Long
    Dim iStart As Long
    '如果是从txt文件中读取的，可能会有BOM头
    If SrcUTF8(i) = &HEF And SrcUTF8(i + 1) = &HBB And SrcUTF8(i + 2) = &HBF Then
        iStart = 3
    End If
    
    ReDim RetUCS2(ilensrc * 2 - 1) As Byte
    Dim p As Long
    
    Dim tmp As Long
    Dim b1 As Byte, b2 As Byte, b3 As Byte
    i = iStart
    Do While i < ilensrc
        b1 = SrcUTF8(i)
        i = i + 1
        
        'UCS2 只有2个字节，只能转换3字节以下的UTF8
        If b1 >= b_1111_0000 Then
            FromUTF8 = "UCS2 只有2个字节，只能转换3字节以下的UTF8"
            Exit Function
            
        ElseIf b1 >= b_1110_0000 Then
            '// 1110 aaaa 10bb bbbb 10cc cccc ==> aaaa bbbb  bbcc cccc
            '// 需要再读取2个字节
            b2 = SrcUTF8(i)
            i = i + 1
        
            b3 = SrcUTF8(i)
            i = i + 1
            
            b1 = ((b1 And b_0000_1111) * 2 ^ 4) Or ((b2 And b_0011_1111) \ 2 ^ 2)
            b2 = ((b2 And b_0000_0011) * 2 ^ 6) Or (b3 And b_0011_1111)
        ElseIf b1 >= b_1100_0000 Then
            '// 110a aaaa 10bb bbbb ==> 0000 0aaa  aabb bbbb
            '// 需要再读取1个字节
            b2 = SrcUTF8(i)
            i = i + 1
            
            b2 = ((b1 And b_0000_0011) * 2 ^ 6) Or (b2 And b_0011_1111)
            b1 = (b1 And b_0011_1111) \ 2 ^ 2
            
        Else
            '// 0aaa aaaa ==> 0000 0000  0aaa aaaa
            b2 = b1
            b1 = 0
        End If
        
        RetUCS2(p) = b2
        RetUCS2(p + 1) = b1
        p = p + 2
    Loop
    
    ReDim Preserve RetUCS2(p - 1) As Byte
End Function

'以字节方式读取文本
Function ReadTxtByOpenBin(fn As String) As Byte()
    Dim num_file As Integer
    Dim str As String
    Dim b() As Byte
    
    num_file = VBA.FreeFile
    
    Open fn For Binary Access Read As #num_file
    ReDim b(VBA.LOF(num_file) - 1) As Byte
    Get #num_file, 1, b
    
    Close #num_file

    ReadTxtByOpenBin = b
End Function



Private Sub TestBitMoveLeftInt()
    Dim b(1) As Byte
    
    b(0) = &HFE
    b(1) = &HFF
    
    Debug.Print BitMoveLeftInt(VBA.CInt(b(1)), 8) Or b(0)
    
End Sub
    

Function BitMoveLeftInt(ByRef v As Integer, num As Long) As Long
    Dim i As Long
    Dim flag As Boolean '是否要把第16位转换为1
    
    For i = 1 To num
        '判断第15位是否=1
        If v > &H4000 Then
            flag = True
            '把第15为置换为0
            v = v And &H3FFF
        Else
            flag = False
        End If
        
        v = v * 2
    Next
    
    If flag Then
        v = v Or &H8000
    End If
    
    BitMoveLeftInt = v
End Function

Function BitMoveLeft(ByRef v As Long, num As Long) As Long
    Dim i As Long
    Dim flag As Boolean '是否要把第32位转换为1
    
    For i = 1 To num
        '判断第31位是否=1
        If v > &H40000000 Then
            flag = True
            '把第31位置换为0
            v = v And &H3FFFFFFF
        Else
            flag = False
        End If
        
        v = v * 2
    Next
    
    If flag Then
        v = v Or &H80000000
    End If
    
    BitMoveLeft = v
End Function


Private Sub TestBitMoveRight()
    Dim l As Long
    l = &HFFFFFFFF
    
    Debug.Print BitMoveRight(&HFFFFFFFF, 1)
    
End Sub
    
Function BitMoveRight(ByRef l As Long, num As Long) As Long
    Dim iStart As Long
    
    iStart = 1
    If l < 0 Then
        '第32位置换为0
        l = l And &H7FFFFFFF
        l = l \ 2
        
        '第31位置换为1
        l = l Or &H40000000
        
        iStart = 2
    End If
    
    Dim i As Long
    For i = iStart To num
        l = l \ 2
    Next
    
    BitMoveRight = l
End Function

'取某一位的Bit
Function GetBitFromByte(b As Byte, ZeroBaseIndex As Long) As Long
    GetBitFromByte = VBA.CLng(b) And (2 ^ ZeroBaseIndex)
    If GetBitFromByte > 0 Then
        GetBitFromByte = 1
    Else
        GetBitFromByte = 0
    End If
End Function


'取某一位的Bit
Function GetBit(b() As Byte, ZeroBaseIndex As Long) As Long
    '数组b中，开始的下标
    Dim bindex As Long
    bindex = ZeroBaseIndex \ 8

    GetBit = VBA.CLng(b(bindex)) And (2 ^ (ZeroBaseIndex Mod 8))
    If GetBit > 0 Then
        GetBit = 1
    Else
        GetBit = 0
    End If
End Function

Sub TestGetBits()
    Dim b(3) As Byte
    
    VBA.Randomize
    b(0) = VBA.Rnd() * 256
    b(1) = VBA.Rnd() * 256
    b(2) = VBA.Rnd() * 256
    b(3) = VBA.Rnd() * 256
    
    Dim tmp As Long
    tmp = GetBit(b, 10)
    
End Sub
' 0000 0000     0000 0000
' 7654 3210     fedc ba98
Function GetBits(b() As Byte, IndexFromZeroBase As Long, iBits As Long) As Long
    Dim i As Long
    Dim tmp As Long
    
    For i = 0 To iBits - 1
        tmp = GetBit(b, IndexFromZeroBase + i)
        tmp = BitMoveLeft(tmp, i)
        GetBits = GetBits Or tmp
    Next
End Function

Function GetBitsRev(b() As Byte, IndexFromZeroBase As Long, iBits As Long) As Long
    Dim i As Long
    Dim tmp As Long
    
    For i = 0 To iBits - 1
        tmp = GetBit(b, IndexFromZeroBase + i)
        tmp = BitMoveLeft(tmp, iBits - i - 1)
        GetBitsRev = GetBitsRev Or tmp
    Next
End Function

Public Function NewCHash(MaxCount As Long) As CHash
    Set NewCHash = New CHash
    NewCHash.MaxCount = MaxCount
End Function


Private Function ReadTxt(fn As String) As Byte()
    Dim num_file As Integer
    Dim str As String
    Dim b() As Byte
    
    num_file = VBA.FreeFile
    
    Open fn For Binary Access Read As #num_file
    ReDim b(VBA.LOF(num_file) - 1) As Byte
    Get #num_file, 1, b
    
    Close #num_file
    
    ReadTxt = b
    
End Function








Public Function NewCFile() As CFile
    Set NewCFile = New CFile
End Function


Attribute VB_Name = "MTest"
Option Explicit

Public Function NewCCompoundFile() As CCompoundFile
    Set NewCCompoundFile = New CCompoundFile
End Function

Private Sub Test_PrintDirs()
    Dim f As CFile
    
    Set f = NewCFile()
    f.OpenFile ThisWorkbook.path & Application.PathSeparator & "testdata" & Application.PathSeparator & "test.xls"
    
    Dim cf As CCompoundFile
    
    Set cf = New CCompoundFile
    
    Dim ret As String
    ret = cf.Parse(f)
    If VBA.Len(ret) Then
        Debug.Print ret
    End If
    
    Dim fs() As String
    fs = cf.DirsName()
    
    Dim i As Long
    For i = 0 To UBound(fs)
        Debug.Print i, fs(i)
    Next
    
    
    Set cf = Nothing
    Set f = Nothing
End Sub

Private Sub Test_CBytes_PrintDirs()
    Dim f As CFile
    
    Set f = NewCFile()
    f.OpenFile ThisWorkbook.path & Application.PathSeparator & "testdata" & Application.PathSeparator & "test.xls"
    
    Dim b() As Byte
    b = f.ReadAll()
    Set f = Nothing
    
    Dim cb As CBytes
    Set cb = NewCBytes()
    cb.SetData = b
    
    Dim cf As CCompoundFile
    Set cf = New CCompoundFile
    
    Dim ret As String
    ret = cf.Parse(cb)
    If VBA.Len(ret) Then
        Debug.Print ret
        Exit Sub
    End If
    
    Dim fs() As String
    fs = cf.DirsName()
    
    Dim i As Long
    For i = 0 To UBound(fs)
        Debug.Print i, fs(i)
    Next
    
    Dim sb() As Byte
    ret = cf.GetStream("Root Entry\_VBA_PROJECT_CUR\PROJECT", sb)
    If VBA.Len(ret) Then
        Debug.Print ret
    End If
    
    Debug.Print VBA.StrConv(sb, vbUnicode)
    
    Set cf = Nothing
    Set cb = Nothing
End Sub

Private Sub Test_GetStream()
    Dim f As CFile
    
    Set f = NewCFile()
    f.OpenFile ThisWorkbook.path & Application.PathSeparator & "testdata" & Application.PathSeparator & "test.xls"
    
    Dim cf As CCompoundFile
    
    Set cf = New CCompoundFile
    
    Dim ret As String
    ret = cf.Parse(f)
    If VBA.Len(ret) Then
        Debug.Print ret
    End If
    
    Dim b() As Byte
    ret = cf.GetStream("Root Entry\_VBA_PROJECT_CUR\PROJECT", b)
    If VBA.Len(ret) Then
        Debug.Print ret
    End If
    
    Debug.Print VBA.StrConv(b, vbUnicode)
    
    Set cf = Nothing
    Set f = Nothing
End Sub

Private Sub Test_ReWriteStream()
    Dim f As CFile
    
    Set f = NewCFile()
    f.OpenFile ThisWorkbook.path & Application.PathSeparator & "testdata" & Application.PathSeparator & "test.xls"
    
    Dim cf As CCompoundFile
    
    Set cf = New CCompoundFile
    
    Dim ret As String
    ret = cf.Parse(f)
    If VBA.Len(ret) Then
        Debug.Print ret
    End If
    
    Dim b() As Byte
    ret = cf.GetStream("Root Entry\_VBA_PROJECT_CUR\PROJECT", b)
    If VBA.Len(ret) Then
        Debug.Print ret
    End If
    
    Dim strSrc As String
    strSrc = VBA.StrConv(b, vbUnicode)
    '替换后模块将被隐藏
    strSrc = VBA.Replace(strSrc, "Module=MMain" & vbNewLine, "")
    Debug.Print strSrc
    
    b = VBA.StrConv(strSrc, vbFromUnicode)
    
    ret = b
    Debug.Print ret
    
    ret = cf.ReWriteStream("Root Entry\_VBA_PROJECT_CUR\PROJECT", b)
    If VBA.Len(ret) Then
        Debug.Print ret
    End If
    
    
    Set cf = Nothing
    Set f = Nothing
End Sub

Private Sub Test_GetAllStream()
    Dim f As CFile
    
    Set f = NewCFile()
    f.OpenFile ThisWorkbook.path & Application.PathSeparator & "testdata" & Application.PathSeparator & "test.xls"
    
    Dim cf As CCompoundFile
    
    Set cf = New CCompoundFile
    
    Dim ret As String
    ret = cf.Parse(f)
    If VBA.Len(ret) Then
        Debug.Print ret
    End If
    
    ret = cf.GetAllStream(ThisWorkbook.path & Application.PathSeparator & "testdata" & Application.PathSeparator & "test")
    If VBA.Len(ret) Then
        Debug.Print ret
    End If
    
    
    Set cf = Nothing
    Set f = Nothing
End Sub

Private Sub Test_ParseThumbs()
    Dim f As CFile
    
    Set f = NewCFile()
    f.OpenFile "C:\Users\<USER>\Pictures\dyh\Thumbs.db"
    
    Dim cf As CCompoundFile
    
    Set cf = New CCompoundFile
    
    Dim ret As String
    ret = cf.Parse(f)
    If VBA.Len(ret) Then
        Debug.Print ret
        Exit Sub
    End If
    
    Dim fs() As String
    fs = cf.DirsName()
    
    Dim b() As Byte, bpic() As Byte
    Dim i As Long, j As Long
    For i = 0 To UBound(fs)
        If fs(i) <> "Root Entry" Then
            ret = cf.GetStream(fs(i), b)
            If VBA.Len(ret) Then
                Debug.Print ret
                Exit Sub
            End If
            
            '保存
    '        每一个缩略图IStream的前3个整形不是缩略图的内容，64位电脑就是24个Byte
            ReDim bpic(UBound(b) - 24) As Byte
            For j = 24 To UBound(b)
                bpic(j - 24) = b(j)
            Next
            
            ByteToFile "C:\Users\<USER>\Pictures\dyh\VBAParse\" & fs(i) & ".jpg", bpic
        End If
    Next
    
    
    Set cf = Nothing
    Set f = Nothing
End Sub

Function ByteToFile(file_name As String, b() As Byte)
    Dim iFreefile As Integer
    
    iFreefile = VBA.FreeFile()
    Open file_name For Binary As iFreefile
    Put #iFreefile, 1, b
    Close iFreefile
End Function


'UnProtectProject
'CMG=
Attribute VB_Name = "Sheet1"
Attribute VB_Base = "0{00020820-0000-0000-C000-000000000046}"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = True
Attribute VB_TemplateDerived = False
Attribute VB_Customizable = True
Option Explicit

Attribute VB_Name = "ThisWorkbook"
Attribute VB_Base = "0{00020819-0000-0000-C000-000000000046}"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = True
Attribute VB_Exposed = True
Attribute VB_TemplateDerived = False
Attribute VB_Customizable = True
Option Explicit

