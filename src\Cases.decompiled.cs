using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Text;
using GUT.Architecture;
using GUT.DataFormats.MSODrawing;
using GUT.DataFormats.OLESS;

[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("Microsoft")]
[assembly: AssemblyTitle("Cases")]
[assembly: AssemblyProduct("Cases")]
[assembly: AssemblyCopyright("Copyright © Microsoft 2009")]
[assembly: AssemblyTrademark("")]
[assembly: ComVisible(false)]
[assembly: Guid("97107922-9cee-41aa-b7dc-299df768b3b3")]
[assembly: AssemblyFileVersion("*******")]
[assembly: CompilationRelaxations(8)]
[assembly: RuntimeCompatibility(WrapNonExceptionThrows = true)]
[assembly: AssemblyVersion("*******")]
namespace GUT.DataFormats.ExcelBIFF8BinaryFormat
{
	public enum BIFFRecordType : ushort
	{
		Formula = 6,
		EOF = 10,
		CalcCount = 12,
		CalcMode = 13,
		CalcPrecision = 14,
		CalcRefMode = 15,
		CalcDelta = 16,
		CalcIter = 17,
		Protect = 18,
		Password = 19,
		Header = 20,
		Footer = 21,
		ExternSheet = 23,
		Lbl = 24,
		WinProtect = 25,
		VerticalPageBreaks = 26,
		HorizontalPageBreaks = 27,
		Note = 28,
		Selection = 29,
		Date1904 = 34,
		ExternName = 35,
		LeftMargin = 38,
		RightMargin = 39,
		TopMargin = 40,
		BottomMargin = 41,
		PrintRowCol = 42,
		PrintGrid = 43,
		FilePass = 47,
		Font = 49,
		PrintSize = 51,
		Continue = 60,
		Window1 = 61,
		Backup = 64,
		Pane = 65,
		CodePage = 66,
		Pls = 77,
		DCon = 80,
		DConRef = 81,
		DConName = 82,
		DefColWidth = 85,
		XCT = 89,
		CRN = 90,
		FileSharing = 91,
		WriteAccess = 92,
		Obj = 93,
		Uncalced = 94,
		CalcSaveRecalc = 95,
		Template = 96,
		Intl = 97,
		ObjProtect = 99,
		ColInfo = 125,
		Guts = 128,
		WsBool = 129,
		GridSet = 130,
		HCenter = 131,
		VCenter = 132,
		BoundSheet8 = 133,
		WriteProtect = 134,
		Country = 140,
		HideObj = 141,
		Sort = 144,
		Palette = 146,
		LhnGraph = 149,
		Sync = 151,
		LPr = 152,
		DxGCol = 153,
		FnGroupName = 154,
		FilterMode = 155,
		BuiltInFnGroupCount = 156,
		AutoFilterInfo = 157,
		AutoFilter = 158,
		Scl = 160,
		Setup = 161,
		ScenMan = 174,
		SCENARIO = 175,
		SxView = 176,
		Sxvd = 177,
		SXVI = 178,
		SxIvd = 180,
		SXLI = 181,
		SXPI = 182,
		DocRoute = 184,
		RecipName = 185,
		MulRk = 189,
		MulBlank = 190,
		Mms = 193,
		SXDI = 197,
		SXDB = 198,
		SXFDB = 199,
		SXDBB = 200,
		SXNum = 201,
		SxBool = 202,
		SxErr = 203,
		SXInt = 204,
		SXString = 205,
		SXDtr = 206,
		SxNil = 207,
		SXTbl = 208,
		SXTBRGIITM = 209,
		SxTbpg = 210,
		ObProj = 211,
		SXStreamID = 213,
		DBCell = 215,
		SXRng = 216,
		SxIsxoper = 217,
		BookBool = 218,
		DbOrParamQry = 220,
		ScenarioProtect = 221,
		OleObjectSize = 222,
		UDDESC = 223,
		XF = 224,
		InterfaceHdr = 225,
		InterfaceEnd = 226,
		SXVS = 227,
		MergeCells = 229,
		BkHim = 233,
		MsoDrawingGroup = 235,
		MsoDrawing = 236,
		MsoDrawingSelection = 237,
		PhoneticInfo = 239,
		SxRule = 240,
		SXEx = 241,
		SxFilt = 242,
		SxDXF = 244,
		SxItm = 245,
		SxName = 246,
		SxSelect = 247,
		SXPair = 248,
		SxFmla = 249,
		SxFormat = 251,
		SST = 252,
		LabelSst = 253,
		ExtSST = 255,
		SXVDEx = 256,
		SXFormula = 259,
		SXDBEx = 290,
		RRDInsDel = 311,
		RRDHead = 312,
		RRDChgCell = 315,
		RRTabId = 317,
		RRDRenSheet = 318,
		RRSort = 319,
		RRDMove = 320,
		RRFormat = 330,
		RRAutoFmt = 331,
		RRInsertSh = 333,
		RRDMoveBegin = 334,
		RRDMoveEnd = 335,
		RRDInsDelBegin = 336,
		RRDInsDelEnd = 337,
		RRDConflict = 338,
		RRDDefName = 339,
		RRDRstEtxp = 340,
		LRng = 351,
		UsesELFs = 352,
		DSF = 353,
		CUsr = 401,
		CbUsr = 402,
		UsrInfo = 403,
		UsrExcl = 404,
		FileLock = 405,
		RRDInfo = 406,
		BCUsrs = 407,
		UsrChk = 408,
		UserBView = 425,
		UserSViewBegin = 426,
		UserSViewBegin_Chart = 426,
		UserSViewEnd = 427,
		RRDUserView = 428,
		Qsi = 429,
		SupBook = 430,
		Prot4Rev = 431,
		CondFmt = 432,
		CF = 433,
		DVal = 434,
		DConBin = 437,
		TxO = 438,
		RefreshAll = 439,
		HLink = 440,
		Lel = 441,
		CodeName = 442,
		SXFDBType = 443,
		Prot4RevPass = 444,
		ObNoMacros = 445,
		Dv = 446,
		Excel9File = 448,
		RecalcId = 449,
		EntExU2 = 450,
		Dimensions = 512,
		Blank = 513,
		Number = 515,
		Label = 516,
		BoolErr = 517,
		String = 519,
		Row = 520,
		Index = 523,
		Array = 545,
		DefaultRowHeight = 549,
		Table = 566,
		Window2 = 574,
		RK = 638,
		Style = 659,
		BigName = 1048,
		Format = 1054,
		ContinueBigName = 1084,
		ShrFmla = 1212,
		HLinkTooltip = 2048,
		WebPub = 2049,
		QsiSXTag = 2050,
		DBQueryExt = 2051,
		ExtString = 2052,
		TxtQry = 2053,
		Qsir = 2054,
		Qsif = 2055,
		RRDTQSIF = 2056,
		BOF = 2057,
		OleDbConn = 2058,
		WOpt = 2059,
		SXViewEx = 2060,
		SXTH = 2061,
		SXPIEx = 2062,
		SXVDTEx = 2063,
		SXViewEx9 = 2064,
		ContinueFrt = 2066,
		RealTimeData = 2067,
		ChartFrtInfo = 2128,
		FrtWrapper = 2129,
		StartBlock = 2130,
		EndBlock = 2131,
		StartObject = 2132,
		EndObject = 2133,
		CatLab = 2134,
		YMult = 2135,
		SXViewLink = 2136,
		PivotChartBits = 2137,
		FrtFontList = 2138,
		SheetExt = 2146,
		BookExt = 2147,
		SXAddl = 2148,
		CrErr = 2149,
		HFPicture = 2150,
		FeatHdr = 2151,
		Feat = 2152,
		DataLabExt = 2154,
		DataLabExtContents = 2155,
		CellWatch = 2156,
		FeatHdr11 = 2161,
		Feature11 = 2162,
		DropDownObjIds = 2164,
		ContinueFrt11 = 2165,
		DConn = 2166,
		List12 = 2167,
		Feature12 = 2168,
		CondFmt12 = 2169,
		CF12 = 2170,
		CFEx = 2171,
		XFCRC = 2172,
		XFExt = 2173,
		AutoFilter12 = 2174,
		ContinueFrt12 = 2175,
		MDTInfo = 2180,
		MDXStr = 2181,
		MDXTuple = 2182,
		MDXSet = 2183,
		MDXProp = 2184,
		MDXKPI = 2185,
		MDB = 2186,
		PLV = 2187,
		Compat12 = 2188,
		DXF = 2189,
		TableStyles = 2190,
		TableStyle = 2191,
		TableStyleElement = 2192,
		StyleExt = 2194,
		NamePublish = 2195,
		NameCmt = 2196,
		SortData = 2197,
		Theme = 2198,
		GUIDTypeLib = 2199,
		FnGrp12 = 2200,
		NameFnGrp12 = 2201,
		MTRSettings = 2202,
		CompressPictures = 2203,
		HeaderFooter = 2204,
		CrtLayout12 = 2205,
		CrtMlFrt = 2206,
		CrtMlFrtContinue = 2207,
		ForceFullCalculation = 2211,
		ShapePropsStream = 2212,
		TextPropsStream = 2213,
		RichTextStream = 2214,
		CrtLayout12A = 2215,
		Units = 4097,
		Chart = 4098,
		Series = 4099,
		DataFormat = 4102,
		LineFormat = 4103,
		MarkerFormat = 4105,
		AreaFormat = 4106,
		PieFormat = 4107,
		AttachedLabel = 4108,
		SeriesText = 4109,
		ChartFormat = 4116,
		Legend = 4117,
		SeriesList = 4118,
		Bar = 4119,
		Line = 4120,
		Pie = 4121,
		Area = 4122,
		Scatter = 4123,
		CrtLine = 4124,
		Axis = 4125,
		Tick = 4126,
		ValueRange = 4127,
		CatSerRange = 4128,
		AxisLine = 4129,
		CrtLink = 4130,
		DefaultText = 4132,
		Text = 4133,
		FontX = 4134,
		ObjectLink = 4135,
		Frame = 4146,
		Begin = 4147,
		End = 4148,
		PlotArea = 4149,
		Chart3d = 4154,
		PicF = 4156,
		DropBar = 4157,
		Radar = 4158,
		Surf = 4159,
		RadarArea = 4160,
		AxisParent = 4161,
		LegendException = 4163,
		ShtProps = 4164,
		SerToCrt = 4165,
		AxesUsed = 4166,
		SBaseRef = 4168,
		SerParent = 4170,
		SerAuxTrend = 4171,
		IFmtRecord = 4174,
		Pos = 4175,
		AlRuns = 4176,
		BRAI = 4177,
		SerAuxErrBar = 4187,
		ClrtClient = 4188,
		SerFmt = 4189,
		Chart3DBarShape = 4191,
		Fbi = 4192,
		BopPop = 4193,
		AxcExt = 4194,
		Dat = 4195,
		PlotGrowth = 4196,
		SIIndex = 4197,
		GelFrame = 4198,
		BopPopCustom = 4199,
		Fbi2 = 4200
	}
	public class BIFFRecord : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 Type;

		[Order(1uL)]
		public DataItem_UInt16 Length;

		public BIFFRecord()
		{
		}

		public BIFFRecord(DataInByteArray Data)
			: base(Data)
		{
		}

		public override string ToString()
		{
			if (Type != null)
			{
				return ((BIFFRecordType)Type.Value.Value).ToString();
			}
			return base.ToString();
		}
	}
	public class TabIndex : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 itab;

		public TabIndex(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class PHS : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 ifnt;

		[BeginBitField(2u)]
		[BitFieldSize(2u)]
		[Order(1uL)]
		public DataItem_UInt16 phType;

		[Order(2uL)]
		[BitFieldSize(2u)]
		public DataItem_UInt16 alcH;

		[BitFieldSize(12u)]
		[Order(3uL)]
		public DataItem_UInt16 unused;

		public PHS(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class FormatRun : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 ich;

		[Order(1uL)]
		public DataItem_UInt16 ifnt;

		public FormatRun(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class LPWideString : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 cchCharacter;

		[Order(1uL)]
		public DataItem_UnicodeString rgchData;

		public LPWideString(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			cchCharacter = new DataItem_UInt16(DataToRead);
			if (cchCharacter != null && ((int?)cchCharacter.Value).HasValue)
			{
				rgchData = new DataItem_UnicodeString(DataToRead, cchCharacter.Value.Value);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}

		public LPWideString(DataInByteArray DataToRead, ref ulong EndOfBIFFRecord, out uint ContinueBlockLength)
		{
			ContinueBlockLength = 0u;
			base.DataStructureOffset = DataToRead.CurrentPosition;
			cchCharacter = new DataItem_UInt16(DataToRead);
			HandleSpanningRecordBoundaries(DataToRead, cchCharacter.Value.Value, 2uL, ref EndOfBIFFRecord, out var CorrectBeginningOffset, out var CorrectLength, out var DataToReadForThisField, out ContinueBlockLength);
			rgchData = new DataItem_UnicodeString(DataToReadForThisField);
			rgchData.Offset = CorrectBeginningOffset;
			rgchData.Length = CorrectLength;
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}

		private void HandleSpanningRecordBoundaries(DataInByteArray DataToRead, ulong CharactersToRead, ulong BytesPerCharacter, ref ulong EndOfBIFFRecord, out ulong CorrectBeginningOffset, out ulong CorrectLength, out DataInByteArray DataToReadForThisField, out uint continueBlockLength)
		{
			continueBlockLength = 0u;
			CorrectBeginningOffset = DataToRead.CurrentPosition;
			long num = (long)(CharactersToRead * BytesPerCharacter);
			MemoryStream memoryStream = new MemoryStream();
			while (num > 0 && (ulong)((long)DataToRead.CurrentPosition + num) > EndOfBIFFRecord)
			{
				if (DataToRead.PeekUInt16(EndOfBIFFRecord - DataToRead.CurrentPosition).Value != 60)
				{
					AddParsingNote(ParsingNoteType.Error, "Expected a continue record, but found something else!", DataToRead.CurrentPosition);
					DataToReadForThisField = DataToRead;
					CorrectLength = 0uL;
					return;
				}
				long num2 = (long)(EndOfBIFFRecord - DataToRead.CurrentPosition);
				if (num2 > 0)
				{
					num -= num2;
					memoryStream.Write(DataToRead.Data, (int)DataToRead.CurrentPosition, (int)num2);
					DataToRead.Seek((ulong)((long)DataToRead.CurrentPosition + num2 + 2));
					DataItem_UInt16 dataItem_UInt = new DataItem_UInt16(DataToRead);
					if (dataItem_UInt == null || !((int?)dataItem_UInt.Value).HasValue)
					{
						AddParsingNote(ParsingNoteType.Error, "Unable to read Continue record length!", DataToRead.CurrentPosition);
						DataToReadForThisField = DataToRead;
						CorrectLength = 0uL;
						return;
					}
					EndOfBIFFRecord = (DataToRead.CurrentPosition + dataItem_UInt.Value).Value;
					continueBlockLength = (uint)(dataItem_UInt.Value.Value + 4);
					continue;
				}
				AddParsingNote(ParsingNoteType.Error, "Current file position (" + DataToRead.CurrentPosition + ") is beyond 'EndOfBIFFRecord' (" + EndOfBIFFRecord + ") causing a negative 'BytesToReadInThisRecord' condition!", DataToRead.CurrentPosition);
				DataToReadForThisField = DataToRead;
				CorrectLength = 0uL;
				return;
			}
			memoryStream.Write(DataToRead.Data, (int)DataToRead.CurrentPosition, (int)num);
			DataToRead.Seek(DataToRead.CurrentPosition + (ulong)num);
			CorrectLength = DataToRead.CurrentPosition - CorrectBeginningOffset;
			DataToReadForThisField = new DataInByteArray(memoryStream.ToArray());
		}

		public override string ToString()
		{
			if (rgchData != null && rgchData.Value != null)
			{
				return rgchData.Value;
			}
			return "";
		}
	}
	public class RPHSSub : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 crun;

		[Order(1uL)]
		public DataItem_UInt16 cch;

		[Order(2uL)]
		public LPWideString st;

		public RPHSSub(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class PhRuns : DataStructure
	{
		[Order(0uL)]
		public DataItem_Int16 ichFirst;

		[Order(1uL)]
		public DataItem_Int16 ichMom;

		[Order(2uL)]
		public DataItem_Int16 cchMom;

		public PhRuns(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class EXTRST : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 reserved;

		[Order(1uL)]
		public DataItem_UInt16 cb;

		[Order(2uL)]
		public PHS phs;

		[Order(3uL)]
		public RPHSSub rphssub;

		[Order(4uL)]
		public List<PhRuns> rgphruns;

		public EXTRST(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			reserved = new DataItem_UInt16(DataToRead);
			cb = new DataItem_UInt16(DataToRead);
			phs = new PHS(DataToRead);
			rphssub = new RPHSSub(DataToRead);
			if (rphssub != null && rphssub.crun != null && ((int?)rphssub.crun.Value).HasValue)
			{
				rgphruns = new List<PhRuns>();
				ushort num = 0;
				while (DataToRead.HasDataLeftToRead && num < rphssub.crun.Value)
				{
					rgphruns.Add(new PhRuns(DataToRead));
					num++;
				}
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class XLUnicodeRichExtendedString : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 cch;

		[Order(1uL)]
		public XLUnicodeRichExtendedStringFlags Flags;

		[Order(2uL)]
		public DataItem_UInt16 cRun;

		[Order(3uL)]
		public DataItem_Int32 cbExtRst;

		[Order(4uL)]
		public DataItem_ASCIIString rgbASCIIString;

		[Order(5uL)]
		public DataItem_UnicodeString rgbUnicodeString;

		[Order(6uL)]
		public List<FormatRun> rgRun;

		[Order(7uL)]
		public EXTRST ExtRst;

		public XLUnicodeRichExtendedString(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			cch = new DataItem_UInt16(DataToRead);
			Flags = new XLUnicodeRichExtendedStringFlags(DataToRead);
			if (Flags != null && Flags.fRichSt != null && Flags.fRichSt.Value == 1)
			{
				cRun = new DataItem_UInt16(DataToRead);
			}
			if (Flags != null && Flags.fExtSt != null && Flags.fExtSt.Value == 1)
			{
				cbExtRst = new DataItem_Int32(DataToRead);
			}
			if (cch != null && ((int?)cch.Value).HasValue && Flags != null && Flags.fHighByte != null)
			{
				byte? value = Flags.fHighByte.Value;
				ulong num = (ulong)((value.GetValueOrDefault() == 0 && value.HasValue) ? 1 : 2);
				List<ulong> list = new List<ulong>();
				ulong num2 = 0uL;
				if (DataToRead.OffsetMappings != null)
				{
					foreach (OffsetMapping offsetMapping in DataToRead.OffsetMappings)
					{
						ulong num3 = offsetMapping.OffsetInStoredByteArray + num2 + DataToRead.OffsetOfBeginningOfData;
						if (num3 > DataToRead.CurrentPosition && num3 < DataToRead.CurrentPosition + num * cch.Value.Value)
						{
							list.Add(num3);
						}
						num2 = offsetMapping.AdditionalOffsetInActualData;
					}
				}
				if (list.Count == 0)
				{
					if (num == 1)
					{
						rgbASCIIString = new DataItem_ASCIIString(DataToRead, cch.Value.Value);
					}
					else if (Flags.fHighByte.Value == 1)
					{
						rgbUnicodeString = new DataItem_UnicodeString(DataToRead, cch.Value.Value);
					}
				}
				else
				{
					ReadStringAcrossContinueBoundaryRespectingEncodingChange(DataToRead, cch.Value.Value, num, list);
				}
			}
			if (cRun != null && ((int?)cRun.Value).HasValue && Flags != null && Flags.fRichSt != null && Flags.fRichSt.Value == 1)
			{
				rgRun = new List<FormatRun>();
				ushort num4 = 0;
				while (DataToRead.HasDataLeftToRead && num4 < cRun.Value)
				{
					rgRun.Add(new FormatRun(DataToRead));
					num4++;
				}
			}
			if (cbExtRst != null && cbExtRst.Value.HasValue && Flags != null && Flags.fExtSt != null && Flags.fExtSt.Value == 1)
			{
				ulong currentPosition = DataToRead.CurrentPosition;
				ulong num5 = 0uL;
				ulong num6 = 0uL;
				if (DataToRead.OffsetMappings != null)
				{
					foreach (OffsetMapping offsetMapping2 in DataToRead.OffsetMappings)
					{
						ulong num7 = offsetMapping2.OffsetInStoredByteArray + num6 + DataToRead.OffsetOfBeginningOfData;
						if (num7 > currentPosition && num7 < (ulong)((long)currentPosition + (long)cbExtRst.Value.Value))
						{
							num5 += 4;
						}
						num6 = offsetMapping2.AdditionalOffsetInActualData;
					}
				}
				ExtRst = new EXTRST(DataToRead);
				DataToRead.Seek((ulong)((long)currentPosition + (long)cbExtRst.Value.Value) + num5);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}

		private void ReadStringAcrossContinueBoundaryRespectingEncodingChange(DataInByteArray DataToRead, ulong TotalCharactersToRead, ulong OriginalBytesPerCharacter, List<ulong> Gaps)
		{
			MemoryStream memoryStream = new MemoryStream();
			ulong currentPosition = DataToRead.CurrentPosition;
			ulong num = OriginalBytesPerCharacter;
			if (OriginalBytesPerCharacter == 1)
			{
				foreach (ulong Gap in Gaps)
				{
					DataToRead.Seek(Gap + 4);
					ulong num2 = DataToRead.PeekUInt8().Value;
					if (num2 != 0 && num2 != 1)
					{
						AddParsingNote(ParsingNoteType.Error, "Expected either a 0 or a 1 in the first byte of the continue record's data to indicate if this section of data is ASCII on Unicode, but read a different value.", DataToRead.CurrentPosition + Gap + 4 - DataToRead.CurrentPosition, 1uL);
						return;
					}
					num2++;
					if (num2 == 2)
					{
						num = 2uL;
						break;
					}
				}
				DataToRead.Seek(currentPosition);
			}
			DataItem_UByteArray dataItem_UByteArray = new DataItem_UByteArray(DataToRead, Gaps[0] - DataToRead.CurrentPosition);
			ulong num3 = 0uL;
			if (num == 2 && OriginalBytesPerCharacter == 1)
			{
				byte[] bytes = Encoding.Unicode.GetBytes(Encoding.ASCII.GetString(dataItem_UByteArray.Value, 0, (int)dataItem_UByteArray.Length));
				memoryStream.Write(bytes, 0, bytes.Length);
			}
			else
			{
				memoryStream.Write(dataItem_UByteArray.Value, 0, (int)dataItem_UByteArray.Length);
			}
			num3 += dataItem_UByteArray.Length / OriginalBytesPerCharacter;
			for (int i = 0; i < Gaps.Count; i++)
			{
				DataToRead.Seek(Gaps[i] + 4);
				ulong num4 = DataToRead.ReadUInt8().Value;
				if (num4 != 0 && num4 != 1)
				{
					AddParsingNote(ParsingNoteType.Error, "Expected either a 0 or a 1 in the first byte of the continue record's data to indicate if this section of data is ASCII on Unicode, but read a different value.", DataToRead.CurrentPosition - 1, 1uL);
					return;
				}
				num4++;
				ulong num5 = 0uL;
				num5 = ((i != Gaps.Count - 1) ? (Gaps[i + 1] - DataToRead.CurrentPosition) : ((TotalCharactersToRead - num3) * num4));
				dataItem_UByteArray = new DataItem_UByteArray(DataToRead, num5);
				if (dataItem_UByteArray.Value == null)
				{
					AddParsingNote(ParsingNoteType.Error, "Ran out of data while reading an XLUnicodeRichExtendedString", currentPosition);
					break;
				}
				if (num4 != num)
				{
					AddParsingNote(ParsingNoteType.Comment, "Noticed the encoding changed during the read of a string across continue records", DataToRead.CurrentPosition - dataItem_UByteArray.Length, dataItem_UByteArray.Length);
					if (num4 == 1)
					{
						byte[] bytes2 = Encoding.Unicode.GetBytes(Encoding.ASCII.GetString(dataItem_UByteArray.Value, 0, (int)dataItem_UByteArray.Length));
						memoryStream.Write(bytes2, 0, bytes2.Length);
					}
					else if (num5 == 2)
					{
						byte[] bytes3 = Encoding.ASCII.GetBytes(Encoding.Unicode.GetString(dataItem_UByteArray.Value, 0, (int)dataItem_UByteArray.Length));
						memoryStream.Write(bytes3, 0, bytes3.Length);
					}
				}
				else
				{
					memoryStream.Write(dataItem_UByteArray.Value, 0, (int)dataItem_UByteArray.Length);
				}
			}
			DataInByteArray dataSource = new DataInByteArray(memoryStream.ToArray(), currentPosition);
			switch (num)
			{
			case 1uL:
				rgbASCIIString = new DataItem_ASCIIString(dataSource, TotalCharactersToRead);
				break;
			case 2uL:
				rgbUnicodeString = new DataItem_UnicodeString(dataSource, TotalCharactersToRead);
				break;
			}
		}

		public override string ToString()
		{
			if (rgbASCIIString != null && rgbASCIIString.Value != null)
			{
				return rgbASCIIString.Value;
			}
			if (rgbUnicodeString != null && rgbUnicodeString.Value != null)
			{
				return rgbUnicodeString.Value;
			}
			return "";
		}
	}
	public class SubStream : DataStructure
	{
		[Order(0uL)]
		public List<BIFFRecord> BIFFRecords;

		public SubStream(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			BIFFRecords = new List<BIFFRecord>();
			int num = 0;
			do
			{
				BIFFRecord bIFFRecord = ExcelBIFF8BinaryFormat.FigureOutWhatTypeOfRecordThisIs(DataToRead);
				if (bIFFRecord == null || bIFFRecord.Length == null || !((int?)bIFFRecord.Length.Value).HasValue)
				{
					break;
				}
				if (bIFFRecord is BOF)
				{
					num++;
				}
				if (num > 0)
				{
					BIFFRecords.Add(bIFFRecord);
					if (DataToRead.CurrentPosition != bIFFRecord.DataStructureOffset + 4 + bIFFRecord.Length.Value)
					{
						if (!DataToRead.Seek(bIFFRecord.DataStructureOffset + 4 + bIFFRecord.Length.Value.Value))
						{
							AddParsingNote(ParsingNoteType.Warning, "A BIFF record of type " + bIFFRecord.ToString() + " ran off the end of the data available.", bIFFRecord.DataStructureOffset);
							break;
						}
						if (DataToRead.PeekUInt16(0uL).Value != 60)
						{
							AddParsingNote(ParsingNoteType.Warning, "A BIFF record of type " + bIFFRecord.ToString() + " was parsed as a different size than its header claimed - moving to the correct position in the file to read the next BIFF record.", bIFFRecord.DataStructureOffset);
						}
					}
				}
				if (bIFFRecord is EOF)
				{
					num--;
				}
			}
			while (num > 0);
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class Cell : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 row;

		[Order(1uL)]
		public DataItem_UInt16 col;

		[Order(2uL)]
		public DataItem_UInt16 ifxcell;

		public Cell(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class XLUnicodeStringFlags : DataStructure
	{
		[BeginBitField(1u)]
		[Order(0uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fHighByte;

		[BitFieldSize(7u)]
		[Order(1uL)]
		public DataItem_UInt8 reserved;

		public XLUnicodeStringFlags(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class XLUnicodeString : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 cch;

		[Order(1uL)]
		public XLUnicodeStringFlags Flags;

		[Order(2uL)]
		public DataItem_ASCIIString rgb_ASCII;

		[Order(3uL)]
		public DataItem_UnicodeString rgb_Unicode;

		public XLUnicodeString(DataInByteArray DataToRead, uint limit)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			cch = new DataItem_UInt16(DataToRead);
			Flags = new XLUnicodeStringFlags(DataToRead);
			uint num = 0u;
			num = (((int?)cch.Value).HasValue ? ((cch.Value <= limit) ? cch.Value.Value : limit) : limit);
			if (((int?)cch.Value).HasValue && Flags.fHighByte != null && ((int?)Flags.fHighByte.Value).HasValue)
			{
				if (Flags.fHighByte.Value == 1)
				{
					rgb_Unicode = new DataItem_UnicodeString(DataToRead, num);
				}
				else
				{
					rgb_ASCII = new DataItem_ASCIIString(DataToRead, num);
				}
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}

		public XLUnicodeString(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			cch = new DataItem_UInt16(DataToRead);
			Flags = new XLUnicodeStringFlags(DataToRead);
			if (((int?)cch.Value).HasValue && Flags.fHighByte != null && ((int?)Flags.fHighByte.Value).HasValue)
			{
				if (Flags.fHighByte.Value == 1)
				{
					rgb_Unicode = new DataItem_UnicodeString(DataToRead, cch.Value.Value);
				}
				else
				{
					rgb_ASCII = new DataItem_ASCIIString(DataToRead, cch.Value.Value);
				}
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}

		public override string ToString()
		{
			if (Flags != null && Flags.fHighByte != null && ((int?)Flags.fHighByte.Value).HasValue)
			{
				byte? value = Flags.fHighByte.Value;
				if (value.GetValueOrDefault() == 0 && value.HasValue)
				{
					if (rgb_ASCII != null && rgb_ASCII.Length != 0 && rgb_ASCII.Value != null)
					{
						return "\"" + rgb_ASCII.Value + "\"";
					}
				}
				else if (rgb_Unicode != null && rgb_Unicode.Length != 0 && rgb_Unicode.Value != null)
				{
					return "\"" + rgb_Unicode.Value + "\"";
				}
			}
			return base.ToString();
		}
	}
	public class XLUnicodeStringNoCch : DataStructure
	{
		[BeginBitField(1u)]
		[BitFieldSize(1u)]
		[Order(0uL)]
		public DataItem_UInt8 fHighByte;

		[Order(1uL)]
		[BitFieldSize(7u)]
		public DataItem_UInt8 reserved;

		[DoNotAutoProcess]
		[Order(2uL)]
		public DataItem_UByteArray rgb;

		public XLUnicodeStringNoCch(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}

		protected override void ParseData(DataInByteArray DataToRead)
		{
			base.ParseData(DataToRead);
			if (fHighByte == null || !((int?)fHighByte.Value).HasValue)
			{
				return;
			}
			_ = DataToRead.CurrentPosition;
			ulong num = 0uL;
			while (DataToRead.HasDataLeftToRead)
			{
				byte? value = fHighByte.Value;
				if (value.GetValueOrDefault() == 0 && value.HasValue)
				{
					if (DataToRead.PeekUInt8(num).Value == 0)
					{
						break;
					}
				}
				else if (DataToRead.PeekUInt16(num).Value == 0)
				{
					break;
				}
				num++;
			}
			if (num != 0)
			{
				byte? value2 = fHighByte.Value;
				if (value2.GetValueOrDefault() == 0 && value2.HasValue)
				{
					rgb = new DataItem_UByteArray(DataToRead, num + 1);
				}
				else
				{
					rgb = new DataItem_UByteArray(DataToRead, (num + 1) * 2);
				}
			}
		}

		public override string ToString()
		{
			if (rgb != null && rgb.Value != null)
			{
				return string.Concat("\"", rgb.Value, "\"");
			}
			return base.ToString();
		}
	}
	public class ShortXLUnicodeString : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt8 cch;

		[Order(1uL)]
		public DataItem_UInt8 A;

		[Order(2uL)]
		public DataItem_ASCIIString SheetName_ASCII;

		[Order(3uL)]
		public DataItem_UnicodeString SheetName_Unicode;

		public ShortXLUnicodeString(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			cch = new DataItem_UInt8(DataToRead);
			A = new DataItem_UInt8(DataToRead);
			if (cch != null && A != null)
			{
				if (A.Value == 1)
				{
					SheetName_Unicode = new DataItem_UnicodeString(DataToRead, cch.Value.Value);
				}
				else
				{
					SheetName_ASCII = new DataItem_ASCIIString(DataToRead, cch.Value.Value);
				}
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class XLUnicodeRichExtendedStringFlags : DataStructure
	{
		[Order(0uL)]
		[BeginBitField(1u)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fHighByte;

		[BitFieldSize(1u)]
		[Order(1uL)]
		public DataItem_UInt8 reserved;

		[BitFieldSize(1u)]
		[Order(2uL)]
		public DataItem_UInt8 fExtSt;

		[Order(3uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fRichSt;

		[BitFieldSize(4u)]
		[Order(4uL)]
		public DataItem_UInt8 reserved2;

		public XLUnicodeRichExtendedStringFlags(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class Ref8u : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 rwFirst;

		[Order(1uL)]
		public DataItem_UInt16 rwLast;

		[Order(2uL)]
		public DataItem_UInt16 colFirst;

		[Order(3uL)]
		public DataItem_UInt16 colLast;

		public Ref8u(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class HyperlinkString : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt32 length;

		[Order(1uL)]
		public DataItem_UnicodeString unicodestring;

		public HyperlinkString(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			length = new DataItem_UInt32(DataToRead);
			if (length.Value.HasValue)
			{
				uint? value = length.Value;
				if (value.GetValueOrDefault() != 0 && value.HasValue)
				{
					unicodestring = new DataItem_UnicodeString(DataToRead, length.Value.Value);
				}
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class LongRGB : DataStructure
	{
		[Order(0uL)]
		public DataItem_Int8 red;

		[Order(1uL)]
		public DataItem_Int8 green;

		[Order(2uL)]
		public DataItem_Int8 blue;

		[Order(3uL)]
		public DataItem_Int8 reserved;

		public LongRGB(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class CMOflagsBIFF8 : DataStructure
	{
		[BeginBitField(2u)]
		[Order(0uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fLocked;

		[Order(1uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 reserved1;

		[BitFieldSize(1u)]
		[Order(2uL)]
		public DataItem_UInt16 fDefaultSize;

		[Order(3uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fPublished;

		[BitFieldSize(1u)]
		[Order(4uL)]
		public DataItem_UInt16 fPrint;

		[Order(5uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 unused1;

		[BitFieldSize(1u)]
		[Order(6uL)]
		public DataItem_UInt16 unused2;

		[Order(7uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fDisabled;

		[Order(8uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fUIObj;

		[Order(9uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fRecalcObj;

		[Order(10uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 unused3;

		[BitFieldSize(1u)]
		[Order(11uL)]
		public DataItem_UInt16 unused4;

		[Order(12uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fRecalcAlways;

		[BitFieldSize(1u)]
		[Order(13uL)]
		public DataItem_UInt16 unused5;

		[Order(14uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 unused6;

		[Order(15uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 unused7;

		public CMOflagsBIFF8(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class CMO : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 ft;

		[Order(1uL)]
		public DataItem_UInt16 cb;

		[Order(2uL)]
		public DataItem_UInt16 ot;

		[Order(3uL)]
		public DataItem_UInt16 id;

		[Order(4uL)]
		public CMOflagsBIFF8 cmoFlags;

		[Order(5uL)]
		public DataItem_UInt32 unused8;

		[Order(6uL)]
		public DataItem_UInt32 unused9;

		[Order(7uL)]
		public DataItem_UInt32 unused10;

		public CMO(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			if (((int?)cb.Value).HasValue)
			{
				ft = new DataItem_UInt16(DataToRead);
				cb = new DataItem_UInt16(DataToRead);
				ot = new DataItem_UInt16(DataToRead);
				id = new DataItem_UInt16(DataToRead);
				cmoFlags = new CMOflagsBIFF8(DataToRead);
				unused8 = new DataItem_UInt32(DataToRead);
				unused9 = new DataItem_UInt32(DataToRead);
				unused10 = new DataItem_UInt32(DataToRead);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class URICreateFlags : DataStructure
	{
		[BitFieldSize(1u)]
		[BeginBitField(2u)]
		[Order(0uL)]
		public DataItem_UInt8 createAllowRelative;

		[BitFieldSize(1u)]
		[Order(1uL)]
		public DataItem_UInt8 createAllowImplicitWildcarsScheme;

		[BitFieldSize(1u)]
		[Order(2uL)]
		public DataItem_UInt8 createAllowImplicitFileScheme;

		[Order(3uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 createNoFrag;

		[BitFieldSize(1u)]
		[Order(4uL)]
		public DataItem_UInt8 createNoCanonicalize;

		[BitFieldSize(1u)]
		[Order(5uL)]
		public DataItem_UInt8 createCanonicalize;

		[BitFieldSize(1u)]
		[Order(6uL)]
		public DataItem_UInt8 createFileUseDosPath;

		[BitFieldSize(1u)]
		[Order(7uL)]
		public DataItem_UInt8 createDecodeExtraInfo;

		[BitFieldSize(1u)]
		[Order(8uL)]
		public DataItem_UInt8 createNoDecodeExtraInfo;

		[BitFieldSize(1u)]
		[Order(9uL)]
		public DataItem_UInt8 createCrackUnknownSchemes;

		[Order(10uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 createNoCrackUnknownSchemes;

		[BitFieldSize(1u)]
		[Order(11uL)]
		public DataItem_UInt8 createPreProcessHtmlUri;

		[BitFieldSize(1u)]
		[Order(12uL)]
		public DataItem_UInt8 createNoPreProcessHtmlUri0;

		[Order(13uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 createIESettings;

		[BitFieldSize(1u)]
		[Order(14uL)]
		public DataItem_UInt8 createNoIESettings;

		[BitFieldSize(1u)]
		[Order(15uL)]
		public DataItem_UInt8 createNoEncodeForbiddenCharacters;

		public URICreateFlags(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class URLMoniker : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt32 length;

		[Order(1uL)]
		public DataItem_UnicodeString url;

		[Order(2uL)]
		public DataItem_GUID serialGUID;

		[Order(3uL)]
		public DataItem_UInt32 serialVersion;

		[Order(4uL)]
		public URICreateFlags uriFlags;

		public URLMoniker(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			length = new DataItem_UInt32(DataToRead);
			url = new DataItem_UnicodeString(DataToRead);
			if (length.Value > url.Length)
			{
				serialGUID = new DataItem_GUID(DataToRead);
				new DataItem_UInt8(DataToRead);
				serialVersion = new DataItem_UInt32(DataToRead);
				uriFlags = new URICreateFlags(DataToRead);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class FileMoniker : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 cAnti;

		[Order(1uL)]
		public DataItem_UInt32 ansiLength;

		[Order(2uL)]
		public DataItem_ASCIIString ansiPath;

		[Order(3uL)]
		public DataItem_UInt16 endServer;

		[Order(4uL)]
		public DataItem_UInt16 versionNumber;

		[Order(5uL)]
		public DataItem_UInt64 reserved1A;

		[Order(6uL)]
		public DataItem_UInt64 reserved1B;

		[Order(7uL)]
		public DataItem_UInt32 reserved2;

		[Order(8uL)]
		public DataItem_UInt32 cbUnicodePathSize;

		[Order(9uL)]
		public DataItem_UInt32 cbUnicodePathBytes;

		[Order(10uL)]
		public DataItem_UInt16 usKeyValue;

		[Order(11uL)]
		public DataItem_UByteArray unicodePath;

		public FileMoniker(DataInByteArray DataToRead)
		{
			cAnti = new DataItem_UInt16(DataToRead);
			ansiLength = new DataItem_UInt32(DataToRead);
			if (ansiLength != null && ansiLength.Value.HasValue)
			{
				uint? value = ansiLength.Value;
				if (value.GetValueOrDefault() != 0 && value.HasValue)
				{
					ansiPath = new DataItem_ASCIIString(DataToRead, ansiLength.Value.Value);
				}
			}
			endServer = new DataItem_UInt16(DataToRead);
			versionNumber = new DataItem_UInt16(DataToRead);
			reserved1A = new DataItem_UInt64(DataToRead);
			reserved1B = new DataItem_UInt64(DataToRead);
			reserved2 = new DataItem_UInt32(DataToRead);
			cbUnicodePathSize = new DataItem_UInt32(DataToRead);
			if (cbUnicodePathSize != null && cbUnicodePathSize.Value.HasValue)
			{
				uint? value2 = cbUnicodePathSize.Value;
				if (value2.GetValueOrDefault() != 0 && value2.HasValue)
				{
					cbUnicodePathBytes = new DataItem_UInt32(DataToRead);
					usKeyValue = new DataItem_UInt16(DataToRead);
					unicodePath = new DataItem_UByteArray(DataToRead, cbUnicodePathBytes.Value.Value);
				}
			}
		}
	}
	public class CompositeMoniker : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt32 cMonikers;

		[Order(1uL)]
		public List<HyperlinkMoniker> monikerArray;

		public CompositeMoniker(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			cMonikers = new DataItem_UInt32(DataToRead);
			if (cMonikers != null && cMonikers.Value.HasValue)
			{
				uint? value = cMonikers.Value;
				if (value.GetValueOrDefault() != 0 && value.HasValue)
				{
					monikerArray = new List<HyperlinkMoniker>((int)cMonikers.Value.Value);
					for (int i = 0; i < cMonikers.Value; i++)
					{
						monikerArray.Add(new HyperlinkMoniker(DataToRead));
					}
				}
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class AntiMoniker : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt32 count;

		public AntiMoniker(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class ItemMoniker : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt32 delimiterLength;

		[Order(1uL)]
		public DataItem_ASCIIString delimiterAnsi;

		[Order(2uL)]
		public DataItem_UnicodeString delimiterUnicode;

		[Order(3uL)]
		public DataItem_UInt32 itemLength;

		[Order(4uL)]
		public DataItem_ASCIIString itemAnsi;

		[Order(5uL)]
		public DataItem_UnicodeString itemUnicode;

		public ItemMoniker(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			delimiterLength = new DataItem_UInt32(DataToRead);
			delimiterAnsi = new DataItem_ASCIIString(DataToRead);
			if (delimiterLength.Value > delimiterAnsi.Length)
			{
				delimiterUnicode = new DataItem_UnicodeString(DataToRead, (delimiterLength.Value - delimiterAnsi.Length).Value);
			}
			itemLength = new DataItem_UInt32(DataToRead);
			itemAnsi = new DataItem_ASCIIString(DataToRead);
			if (itemLength.Value > itemAnsi.Length)
			{
				itemUnicode = new DataItem_UnicodeString(DataToRead, (itemLength.Value - itemAnsi.Length).Value);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class HyperlinkMoniker : DataStructure
	{
		[Order(0uL)]
		public DataItem_GUID monikerClsid;

		[Order(1uL)]
		public URLMoniker udata;

		[Order(2uL)]
		public FileMoniker fdata;

		[Order(3uL)]
		public CompositeMoniker cdata;

		[Order(4uL)]
		public AntiMoniker adata;

		[Order(5uL)]
		public ItemMoniker idata;

		public HyperlinkMoniker(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			monikerClsid = new DataItem_GUID(DataToRead);
			switch (monikerClsid.ToString().Replace("-", string.Empty))
			{
			case "79eac9e0baf911ce8c8200aa004ba90b":
				udata = new URLMoniker(DataToRead);
				break;
			case "0000030300000000c000000000000046":
				fdata = new FileMoniker(DataToRead);
				break;
			case "0000030900000000c000000000000046":
				cdata = new CompositeMoniker(DataToRead);
				break;
			case "0000030500000000c000000000000046":
				adata = new AntiMoniker(DataToRead);
				break;
			case "0000030400000000c000000000000046":
				idata = new ItemMoniker(DataToRead);
				break;
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class HyperlinkFlags : DataStructure
	{
		[BitFieldSize(1u)]
		[BeginBitField(2u)]
		[Order(0uL)]
		public DataItem_UInt8 hlstmfHasMoniker;

		[Order(1uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 hlstmfIsAbsolute;

		[Order(2uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 hlstmfSiteGaveDisplayName;

		[BitFieldSize(1u)]
		[Order(3uL)]
		public DataItem_UInt8 hlstmfHasLocationStr;

		[Order(4uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 hlstmfHasDisplayName;

		[BitFieldSize(1u)]
		[Order(5uL)]
		public DataItem_UInt8 hlstmfHasGUID;

		[BitFieldSize(1u)]
		[Order(6uL)]
		public DataItem_UInt8 hlstmfHasCreationTime;

		[Order(7uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 hlstmfHasFrameName;

		[BitFieldSize(1u)]
		[Order(8uL)]
		public DataItem_UInt8 hlstmfMonikerSavedAsStr;

		[BitFieldSize(1u)]
		[Order(9uL)]
		public DataItem_UInt8 hlstmfAbsFromGetdataRel;

		[BitFieldSize(6u)]
		[Order(10uL)]
		public DataItem_UInt8 unsused;

		[Order(11uL)]
		public DataItem_UInt16 reserved;

		public HyperlinkFlags(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class Hyperlink : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt32 streamVersion;

		[Order(1uL)]
		public HyperlinkFlags hyperlinkBitFields;

		[Order(2uL)]
		public HyperlinkString displayName;

		[Order(3uL)]
		public HyperlinkString targetFrameName;

		[Order(4uL)]
		public HyperlinkString moniker;

		[Order(5uL)]
		public HyperlinkMoniker oleMoniker;

		[Order(6uL)]
		public HyperlinkString location;

		[Order(7uL)]
		public DataItem_GUID guid;

		[Order(8uL)]
		public DataItem_UInt64 fileTime;

		public Hyperlink(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			streamVersion = new DataItem_UInt32(DataToRead);
			hyperlinkBitFields = new HyperlinkFlags(DataToRead);
			if (hyperlinkBitFields.hlstmfHasDisplayName != null && ((int?)hyperlinkBitFields.hlstmfHasDisplayName.Value).HasValue)
			{
				byte? value = hyperlinkBitFields.hlstmfHasDisplayName.Value;
				if (value.GetValueOrDefault() != 0 || !value.HasValue)
				{
					displayName = new HyperlinkString(DataToRead);
				}
			}
			if (hyperlinkBitFields.hlstmfHasFrameName != null && ((int?)hyperlinkBitFields.hlstmfHasFrameName.Value).HasValue)
			{
				byte? value2 = hyperlinkBitFields.hlstmfHasFrameName.Value;
				if (value2.GetValueOrDefault() != 0 || !value2.HasValue)
				{
					targetFrameName = new HyperlinkString(DataToRead);
				}
			}
			if (hyperlinkBitFields.hlstmfHasMoniker != null && ((int?)hyperlinkBitFields.hlstmfHasMoniker.Value).HasValue && hyperlinkBitFields.hlstmfMonikerSavedAsStr != null && ((int?)hyperlinkBitFields.hlstmfMonikerSavedAsStr.Value).HasValue)
			{
				byte? value3 = hyperlinkBitFields.hlstmfHasMoniker.Value;
				if (value3.GetValueOrDefault() != 0 || !value3.HasValue)
				{
					byte? value4 = hyperlinkBitFields.hlstmfMonikerSavedAsStr.Value;
					if (value4.GetValueOrDefault() != 0 || !value4.HasValue)
					{
						moniker = new HyperlinkString(DataToRead);
					}
				}
			}
			if (hyperlinkBitFields.hlstmfHasMoniker != null && ((int?)hyperlinkBitFields.hlstmfHasMoniker.Value).HasValue && hyperlinkBitFields.hlstmfMonikerSavedAsStr != null && ((int?)hyperlinkBitFields.hlstmfMonikerSavedAsStr.Value).HasValue)
			{
				byte? value5 = hyperlinkBitFields.hlstmfHasMoniker.Value;
				if (value5.GetValueOrDefault() != 0 || !value5.HasValue)
				{
					byte? value6 = hyperlinkBitFields.hlstmfMonikerSavedAsStr.Value;
					if (value6.GetValueOrDefault() == 0 && value6.HasValue)
					{
						oleMoniker = new HyperlinkMoniker(DataToRead);
					}
				}
			}
			if (hyperlinkBitFields.hlstmfHasLocationStr != null && ((int?)hyperlinkBitFields.hlstmfHasLocationStr.Value).HasValue)
			{
				byte? value7 = hyperlinkBitFields.hlstmfHasLocationStr.Value;
				if (value7.GetValueOrDefault() != 0 || !value7.HasValue)
				{
					location = new HyperlinkString(DataToRead);
				}
			}
			if (hyperlinkBitFields.hlstmfHasGUID != null && ((int?)hyperlinkBitFields.hlstmfHasGUID.Value).HasValue)
			{
				byte? value8 = hyperlinkBitFields.hlstmfHasGUID.Value;
				if (value8.GetValueOrDefault() != 0 || !value8.HasValue)
				{
					guid = new DataItem_GUID(DataToRead);
				}
			}
			if (hyperlinkBitFields.hlstmfHasCreationTime != null && ((int?)hyperlinkBitFields.hlstmfHasCreationTime.Value).HasValue)
			{
				byte? value9 = hyperlinkBitFields.hlstmfHasCreationTime.Value;
				if (value9.GetValueOrDefault() != 0 || !value9.HasValue)
				{
					fileTime = new DataItem_UInt64(DataToRead);
				}
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class FTCmo : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 ft;

		[Order(1uL)]
		public DataItem_UInt16 cb;

		[Order(2uL)]
		public DataItem_UInt16 ot;

		[Order(3uL)]
		public DataItem_UInt16 id;

		[Order(4uL)]
		public DataItem_UInt16 flags;

		[Order(5uL)]
		public DataItem_UInt32 unused8;

		[Order(6uL)]
		public DataItem_UInt32 unused9;

		[Order(7uL)]
		public DataItem_UInt32 unused10;

		public FTCmo(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class FTData : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 ft;

		[Order(1uL)]
		public DataItem_UInt16 cb;

		[Order(2uL)]
		public DataItem_UInt16 cf;

		public FTData(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class FTRboData : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 ft;

		[Order(1uL)]
		public DataItem_UInt16 cb;

		[Order(2uL)]
		public DataItem_UInt16 idradnext;

		[Order(3uL)]
		public DataItem_UInt16 ffirstbtn;

		public FTRboData(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class FTGboData : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 ft;

		[Order(1uL)]
		public DataItem_UInt16 cb;

		[Order(2uL)]
		public DataItem_UInt16 accel;

		[Order(3uL)]
		public DataItem_UInt16 reserved;

		[Order(4uL)]
		public DataItem_UInt16 flags;

		public FTGboData(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class FTEboData : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 ft;

		[Order(1uL)]
		public DataItem_UInt16 cb;

		[Order(2uL)]
		public DataItem_UInt16 ivtedit;

		[Order(3uL)]
		public DataItem_UInt16 fmultiline;

		[Order(4uL)]
		public DataItem_UInt16 fvscroll;

		[Order(5uL)]
		public DataItem_UInt16 id;

		public FTEboData(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class FtCbls : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 ft;

		[Order(1uL)]
		public DataItem_UInt16 cb;

		[Order(2uL)]
		public DataItem_UInt32 unused1;

		[Order(3uL)]
		public DataItem_UInt32 unused2;

		[Order(4uL)]
		public DataItem_UInt32 unused3;

		public FtCbls(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class FTRbo : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 ft;

		[Order(1uL)]
		public DataItem_UInt16 cb;

		[Order(2uL)]
		public DataItem_UInt32 unused1;

		[Order(3uL)]
		public DataItem_UInt16 unused2;

		public FTRbo(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class FTSbs : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 ft;

		[Order(1uL)]
		public DataItem_UInt16 cb;

		[Order(2uL)]
		public DataItem_UInt32 unused1;

		[Order(3uL)]
		public DataItem_UInt16 ival;

		[Order(4uL)]
		public DataItem_UInt16 imin;

		[Order(5uL)]
		public DataItem_UInt16 imax;

		[Order(6uL)]
		public DataItem_UInt16 dinc;

		[Order(7uL)]
		public DataItem_UInt16 dpage;

		[Order(8uL)]
		public DataItem_UInt16 fhoriz;

		[Order(9uL)]
		public DataItem_UInt16 dxscroll;

		[Order(10uL)]
		public DataItem_UInt16 flags;

		public FTSbs(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class FtNts : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 ft;

		[Order(1uL)]
		public DataItem_UInt16 cb;

		[Order(2uL)]
		public DataItem_GUID guid;

		[Order(3uL)]
		public DataItem_UInt16 fsharednote;

		[Order(4uL)]
		public DataItem_UInt32 unused;

		public FtNts(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class ObjectParsedFormula : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 cce;

		[Order(1uL)]
		public DataItem_UInt32 unused;

		[Order(2uL)]
		public DataItem_UByteArray rgce;

		public ObjectParsedFormula(DataInByteArray Data)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			cce = new DataItem_UInt16(Data);
			unused = new DataItem_UInt32(Data);
			if (cce != null && ((int?)cce.Value).HasValue)
			{
				cce.Value = (ushort)(cce.Value.Value & (ushort)(Math.Pow(2.0, 15.0) - 1.0));
				ushort? value = cce.Value;
				if (value.GetValueOrDefault() != 0 || !value.HasValue)
				{
					rgce = new DataItem_UByteArray(Data, cce.Value.Value);
				}
			}
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class ObjFmla : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 cbfmla;

		[Order(1uL)]
		public ObjectParsedFormula fmla;

		[Order(2uL)]
		public DataItem_UByteArray remaining;

		public ObjFmla(DataInByteArray Data)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			cbfmla = new DataItem_UInt16(Data);
			if (cbfmla != null && cbfmla.Value > 0)
			{
				ulong currentPosition = Data.CurrentPosition;
				fmla = new ObjectParsedFormula(Data);
				ulong currentPosition2 = Data.CurrentPosition;
				ulong num = currentPosition2 - currentPosition;
				if ((long)(cbfmla.Value.Value - num) > 0L)
				{
					remaining = new DataItem_UByteArray(Data, cbfmla.Value.Value - num);
				}
			}
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class ObjLinkFmla : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 ft;

		[Order(1uL)]
		public ObjFmla fmla;

		public ObjLinkFmla(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class PictFmlaKey : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt32 cbkey;

		[Order(1uL)]
		public DataItem_UByteArray keyBuf;

		[Order(2uL)]
		public ObjFmla fmlalinkedcell;

		[Order(3uL)]
		public ObjFmla fmlalistfillrange;

		public PictFmlaKey(DataInByteArray Data)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			cbkey = new DataItem_UInt32(Data);
			if (cbkey != null)
			{
				uint? value = cbkey.Value;
				if (value.GetValueOrDefault() != 0 && value.HasValue)
				{
					keyBuf = new DataItem_UByteArray(Data, cbkey.Value.Value);
				}
			}
			fmlalinkedcell = new ObjFmla(Data);
			fmlalistfillrange = new ObjFmla(Data);
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class FTPictFmla : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 ft;

		[Order(1uL)]
		public DataItem_UInt16 cb;

		[Order(2uL)]
		public DataItem_UByteArray tempVacuum;

		public FTPictFmla(FtPioGrbit pictFlags, DataInByteArray Data)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			ft = new DataItem_UInt16(Data);
			cb = new DataItem_UInt16(Data);
			if (cb != null && ((int?)cb.Value).HasValue)
			{
				tempVacuum = new DataItem_UByteArray(Data, cb.Value.Value);
			}
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class FtMacro : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 ft;

		[Order(1uL)]
		public ObjFmla fmla;

		public FtMacro(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class FTCblsData : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 ft;

		[Order(1uL)]
		public DataItem_UInt16 cb;

		[Order(2uL)]
		public DataItem_UInt16 fchecked;

		[Order(3uL)]
		public DataItem_UInt16 accel;

		[Order(4uL)]
		public DataItem_UInt16 reserved;

		[Order(5uL)]
		public DataItem_UInt16 flags;

		public FTCblsData(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class LBSDropData : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 flags;

		[Order(1uL)]
		public DataItem_UInt16 cline;

		[Order(2uL)]
		public ObjFmla dxmin;

		[Order(3uL)]
		public XLUnicodeString str;

		[Order(4uL)]
		public DataItem_UInt8 unused3;

		public LBSDropData(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class FlagsandLct : DataStructure
	{
		[BitFieldSize(1u)]
		[Order(0uL)]
		[BeginBitField(1u)]
		public DataItem_UInt8 fusecb;

		[BitFieldSize(1u)]
		[Order(1uL)]
		public DataItem_UInt8 fvalidplex;

		[BitFieldSize(1u)]
		[Order(2uL)]
		public DataItem_UInt8 fvalidids;

		[Order(3uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fno3d;

		[Order(4uL)]
		[BitFieldSize(2u)]
		public DataItem_UInt8 wlistseltype;

		[Order(5uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 unused;

		[BitFieldSize(1u)]
		[Order(6uL)]
		public DataItem_UInt8 reserved;

		[Order(7uL)]
		public DataItem_UInt16 lct;

		public FlagsandLct(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class FTLbsData : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 ft;

		[Order(1uL)]
		public DataItem_UInt16 cbfcontinued;

		[Order(2uL)]
		public ObjFmla fmla;

		[Order(3uL)]
		public DataItem_UInt16 clines;

		[Order(4uL)]
		public DataItem_UInt16 isel;

		[Order(5uL)]
		public FlagsandLct flagsandlct;

		[Order(6uL)]
		public DataItem_UInt16 idedit;

		[Order(7uL)]
		public LBSDropData dropdata;

		[Order(8uL)]
		public List<XLUnicodeString> rglines;

		[Order(9uL)]
		public List<DataItem_UInt8> bsels;

		public FTLbsData(DataInByteArray Data, ushort cmoot, ulong EndOfContainingBiffRecord)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			ft = new DataItem_UInt16(Data);
			cbfcontinued = new DataItem_UInt16(Data);
			if (((int?)cbfcontinued.Value).HasValue)
			{
				ushort? value = cbfcontinued.Value;
				if (value.GetValueOrDefault() != 0 || !value.HasValue)
				{
					fmla = new ObjFmla(Data);
					clines = new DataItem_UInt16(Data);
					isel = new DataItem_UInt16(Data);
					flagsandlct = new FlagsandLct(Data);
					idedit = new DataItem_UInt16(Data);
					if (cmoot == 20)
					{
						dropdata = new LBSDropData(Data);
					}
					if (flagsandlct != null && flagsandlct.fvalidplex != null && flagsandlct.fvalidplex.Value == 1)
					{
						rglines = new List<XLUnicodeString>();
						if (clines != null && ((int?)clines.Value).HasValue)
						{
							ushort? value2 = clines.Value;
							if (value2.GetValueOrDefault() != 0 || !value2.HasValue)
							{
								for (int i = 0; i < clines.Value; i++)
								{
									XLUnicodeString item = new XLUnicodeString(Data);
									rglines.Add(item);
								}
							}
						}
					}
					if (flagsandlct.wlistseltype != null)
					{
						byte? value3 = flagsandlct.wlistseltype.Value;
						if ((value3.GetValueOrDefault() != 0 || !value3.HasValue) && clines != null && ((int?)clines.Value).HasValue)
						{
							ushort? value4 = clines.Value;
							if (value4.GetValueOrDefault() != 0 || !value4.HasValue)
							{
								bsels = new List<DataItem_UInt8>();
								for (int j = 0; j < clines.Value; j++)
								{
									DataItem_UInt8 item2 = new DataItem_UInt8(Data);
									bsels.Add(item2);
								}
							}
						}
					}
				}
			}
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class FtPioGrbit : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 ft;

		[Order(1uL)]
		public DataItem_UInt16 cb;

		[BitFieldSize(1u)]
		[BeginBitField(2u)]
		[Order(2uL)]
		public DataItem_UInt8 fCamera;

		[Order(3uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 unused1;

		[BitFieldSize(1u)]
		[Order(4uL)]
		public DataItem_UInt8 fPrstm;

		[Order(5uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fCtl;

		[Order(6uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fIcon;

		[BitFieldSize(1u)]
		[Order(7uL)]
		public DataItem_UInt8 fPrintCalc;

		[Order(8uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fDde;

		[Order(9uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fAutoPict;

		[BitFieldSize(6u)]
		[Order(10uL)]
		public DataItem_UInt8 unused2;

		[Order(11uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fAutoLoad;

		[BitFieldSize(1u)]
		[Order(12uL)]
		public DataItem_UInt8 fDefaultSize;

		public FtPioGrbit(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class StyleFlags : DataStructure
	{
		[BeginBitField(2u)]
		[Order(0uL)]
		[BitFieldSize(12u)]
		public DataItem_UInt16 ixfe;

		[BitFieldSize(3u)]
		[Order(1uL)]
		public DataItem_UInt16 unused;

		[BitFieldSize(1u)]
		[Order(2uL)]
		public DataItem_UInt16 fBuiltIn;

		public StyleFlags(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class BuiltInStyle : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt8 istyBuiltIn;

		[Order(1uL)]
		public DataItem_UInt8 iLevel;

		public BuiltInStyle(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class FontIndex : DataStructure
	{
		[Order(0uL)]
		public DataItem_Int16 ifnt;

		public FontIndex(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class DXFNum : DataStructure
	{
		[Order(0uL)]
		public DataItem_Int8 unused;

		[Order(1uL)]
		public DataItem_Int8 ifmt;

		[Order(2uL)]
		public DataItem_UInt16 cb;

		[Order(3uL)]
		public XLUnicodeString fmt;

		public DXFNum(DataInByteArray DataToRead, int fIfmtUser)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			if (fIfmtUser == 0)
			{
				unused = new DataItem_Int8(DataToRead);
				ifmt = new DataItem_Int8(DataToRead);
			}
			else
			{
				cb = new DataItem_UInt16(DataToRead);
				fmt = new XLUnicodeString(DataToRead);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class DXFFntD : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt8 cchFont;

		[Order(1uL)]
		public XLUnicodeStringNoCch stFontName;

		[Order(2uL)]
		public DataItem_UByteArray unused1;

		[Order(3uL)]
		public DataItem_UByteArray remaining;

		public DXFFntD(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			cchFont = new DataItem_UInt8(DataToRead);
			stFontName = new XLUnicodeStringNoCch(DataToRead);
			if (stFontName == null || stFontName.ToString() == string.Empty || 63 - (DataToRead.CurrentPosition - base.DataStructureOffset + 1) < 0)
			{
				unused1 = new DataItem_UByteArray(DataToRead, 63uL);
			}
			else if ((long)(63 - (DataToRead.CurrentPosition - base.DataStructureOffset + 1)) > 0L)
			{
				unused1 = new DataItem_UByteArray(DataToRead, 63 - (DataToRead.CurrentPosition - base.DataStructureOffset + 1));
			}
			remaining = new DataItem_UByteArray(DataToRead, 54uL);
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class DXFN : DataStructure
	{
		[BeginBitField(4u)]
		[BitFieldSize(25u)]
		[Order(0uL)]
		public DataItem_UInt32 unparsedFlags1;

		[Order(1uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 ibitAtrNum;

		[BitFieldSize(1u)]
		[Order(2uL)]
		public DataItem_UInt8 ibitAtrFnt;

		[Order(3uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 ibitAtrAlc;

		[Order(4uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 ibitAtrBdr;

		[BitFieldSize(1u)]
		[Order(5uL)]
		public DataItem_UInt8 ibitAtrPat;

		[Order(6uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 ibitAtrProt;

		[BitFieldSize(1u)]
		[Order(7uL)]
		public DataItem_UInt8 iReadingOrderNinch;

		[BeginBitField(2u)]
		[Order(8uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fIfmtUser;

		[BitFieldSize(15u)]
		[Order(9uL)]
		public DataItem_UInt16 unparsedFlags2;

		[Order(10uL)]
		[DoNotAutoProcess]
		public DXFNum dxfnum;

		[Order(11uL)]
		[DoNotAutoProcess]
		public DXFFntD dxffntd;

		[DoNotAutoProcess]
		[Order(12uL)]
		public DataItem_UInt64 dxfalc;

		[DoNotAutoProcess]
		[Order(13uL)]
		public DataItem_UInt64 dxfbdr;

		[Order(14uL)]
		[DoNotAutoProcess]
		public DataItem_UInt32 dxfpat;

		[Order(15uL)]
		[DoNotAutoProcess]
		public DataItem_UInt16 dxfprot;

		[DoNotAutoProcess]
		public ushort ShortFollowingBitFields;

		public DXFN(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}

		protected override void ParseData(DataInByteArray DataToRead)
		{
			base.ParseData(DataToRead);
			if (fIfmtUser != null && ((int?)fIfmtUser.Value).HasValue)
			{
				byte? value = fIfmtUser.Value;
				if (value == 1 && value.HasValue && DataToRead.HasDataLeftToRead)
				{
					ShortFollowingBitFields = DataToRead.PeekUInt16().Value;
				}
			}
			if (ibitAtrNum != null && ((int?)ibitAtrNum.Value).HasValue)
			{
				byte? value2 = ibitAtrNum.Value;
				if ((value2.GetValueOrDefault() != 0 || !value2.HasValue) && fIfmtUser != null && ((int?)fIfmtUser.Value).HasValue)
				{
					dxfnum = new DXFNum(DataToRead, fIfmtUser.Value.Value);
				}
			}
			if (ibitAtrFnt != null && ((int?)ibitAtrFnt.Value).HasValue)
			{
				byte? value3 = ibitAtrFnt.Value;
				if (value3.GetValueOrDefault() != 0 || !value3.HasValue)
				{
					dxffntd = new DXFFntD(DataToRead);
				}
			}
			if (ibitAtrAlc != null && ((int?)ibitAtrAlc.Value).HasValue)
			{
				byte? value4 = ibitAtrAlc.Value;
				if (value4.GetValueOrDefault() != 0 || !value4.HasValue)
				{
					dxfalc = new DataItem_UInt64(DataToRead);
				}
			}
			if (ibitAtrBdr != null && ((int?)ibitAtrBdr.Value).HasValue)
			{
				byte? value5 = ibitAtrBdr.Value;
				if (value5.GetValueOrDefault() != 0 || !value5.HasValue)
				{
					dxfbdr = new DataItem_UInt64(DataToRead);
				}
			}
			if (ibitAtrPat != null && ((int?)ibitAtrPat.Value).HasValue)
			{
				byte? value6 = ibitAtrPat.Value;
				if (value6.GetValueOrDefault() != 0 || !value6.HasValue)
				{
					dxfpat = new DataItem_UInt32(DataToRead);
				}
			}
			if (ibitAtrProt != null && ((int?)ibitAtrProt.Value).HasValue)
			{
				byte? value7 = ibitAtrProt.Value;
				if (value7.GetValueOrDefault() != 0 || !value7.HasValue)
				{
					dxfprot = new DataItem_UInt16(DataToRead);
				}
			}
		}
	}
	public class IFmt : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 ifmt;

		public IFmt(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class XFFlags : DataStructure
	{
		[BitFieldSize(1u)]
		[BeginBitField(2u)]
		[Order(0uL)]
		public DataItem_UInt16 fLocked;

		[Order(1uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fHidden;

		[BitFieldSize(1u)]
		[Order(2uL)]
		public DataItem_UInt16 fStyle;

		[Order(3uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 f123Prefix;

		[BitFieldSize(12u)]
		[Order(4uL)]
		public DataItem_UInt16 ixfParent;

		public XFFlags(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class CellParsedFormula : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 cce;

		[Order(1uL)]
		public DataItem_UByteArray rgce;

		[Order(2uL)]
		public DataItem_UByteArray rgcb;

		public CellParsedFormula(DataInByteArray DataToRead, ulong bytesToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			cce = new DataItem_UInt16(DataToRead);
			if (cce != null && ((int?)cce.Value).HasValue && cce.Value > 0)
			{
				rgce = new DataItem_UByteArray(DataToRead, cce.Value.Value);
				ulong? num = bytesToRead - cce.Value - 2;
				if (num.GetValueOrDefault() != 0 && num.HasValue)
				{
					rgcb = new DataItem_UByteArray(DataToRead, (bytesToRead - cce.Value - 2).Value);
				}
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class BOF : BIFFRecord
	{
		[Order(2uL)]
		public DataItem_UInt16 Version;

		[Order(3uL)]
		public DataItem_UInt16 dt;

		[Order(4uL)]
		public DataItem_UInt16 rupBuild;

		[Order(5uL)]
		public DataItem_UInt16 rupYear;

		[BitFieldSize(1u)]
		[Order(6uL)]
		[BeginBitField(4u)]
		public DataItem_UInt32 fWin;

		[BitFieldSize(1u)]
		[Order(7uL)]
		public DataItem_UInt32 fRisc;

		[BitFieldSize(1u)]
		[Order(8uL)]
		public DataItem_UInt32 fBeta;

		[Order(9uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt32 fWinAny;

		[Order(10uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt32 fMacAny;

		[Order(11uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt32 fBetaAny;

		[BitFieldSize(2u)]
		[Order(12uL)]
		public DataItem_UInt32 unused1;

		[BitFieldSize(1u)]
		[Order(13uL)]
		public DataItem_UInt32 fRiscAny;

		[Order(14uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt32 fOOM;

		[BitFieldSize(1u)]
		[Order(15uL)]
		public DataItem_UInt32 fGIJmp;

		[Order(16uL)]
		[BitFieldSize(2u)]
		public DataItem_UInt32 unused2;

		[Order(17uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt32 fFontLimit;

		[BitFieldSize(4u)]
		[Order(18uL)]
		public DataItem_UInt32 verXLHigh;

		[Order(19uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt32 unused3;

		[Order(20uL)]
		[BitFieldSize(13u)]
		public DataItem_UInt32 reserved1;

		[BitFieldSize(8u)]
		[BeginBitField(4u)]
		[Order(21uL)]
		public DataItem_UInt32 verLowestBiff;

		[Order(22uL)]
		[BitFieldSize(4u)]
		public DataItem_UInt32 verLastXLSaved;

		[Order(23uL)]
		[BitFieldSize(20u)]
		public DataItem_UInt32 reserved2;

		public BOF(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class EOF : BIFFRecord
	{
		public EOF(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class MSODrawing : BIFFRecord
	{
		[Order(2uL)]
		public OfficeArtDGContainer rgChildRec;

		[Order(3uL)]
		public OfficeArtSpContainer standAloneSpContainer;

		[Order(4uL)]
		public OfficeArtGlom OfficeArtFDGSL;

		public MSODrawing(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Type = new DataItem_UInt16(DataToRead);
			Length = new DataItem_UInt16(DataToRead);
			if (Length == null || !((int?)Length.Value).HasValue)
			{
				AddParsingNote(ParsingNoteType.Error, "Unable to read the length of an MSODrawing record!", DataToRead.CurrentPosition);
				base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
				return;
			}
			ulong currentPosition = DataToRead.CurrentPosition;
			OfficeArtRecordHeader officeArtRecordHeader = new OfficeArtRecordHeader(DataToRead);
			DataToRead.Seek(currentPosition);
			if (officeArtRecordHeader.recType == null || !((int?)officeArtRecordHeader.recType.Value).HasValue)
			{
				AddParsingNote(ParsingNoteType.Error, "Unable to read peek header of an MSO Drawing Group.  The record is most probably corrupt.!", DataToRead.CurrentPosition);
			}
			else if (officeArtRecordHeader.recType.Value == 61444 || officeArtRecordHeader.recType.Value == 61453)
			{
				standAloneSpContainer = new OfficeArtSpContainer(DataToRead, (base.DataStructureOffset + Length.Value + 4 - 1).Value);
			}
			else if (officeArtRecordHeader.recType.Value == 61721)
			{
				OfficeArtFDGSL = new OfficeArtGlom(DataToRead, (base.DataStructureOffset + Length.Value + 4).Value);
			}
			else
			{
				rgChildRec = new OfficeArtDGContainer(DataToRead, Length.Value.Value, (base.DataStructureOffset + Length.Value + 4 - 1).Value);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class MSODrawingGroup : BIFFRecord
	{
		[Order(2uL)]
		public OfficeArtDGGContainer rgChildRec;

		[Order(3uL)]
		public DataItem_UByteArray payload;

		public MSODrawingGroup(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Type = new DataItem_UInt16(DataToRead);
			Length = new DataItem_UInt16(DataToRead);
			if (Length == null || !((int?)Length.Value).HasValue)
			{
				AddParsingNote(ParsingNoteType.Error, "Unable to read the length of an MSODrawingGroup record!", DataToRead.CurrentPosition);
				base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
				return;
			}
			ushort value = DataToRead.PeekUInt16(2uL).Value;
			if (value != 61440)
			{
				payload = new DataItem_UByteArray(DataToRead, Length.Value.Value);
			}
			else
			{
				rgChildRec = new OfficeArtDGGContainer(DataToRead, Length.Value.Value, (base.DataStructureOffset + Length.Value + 4 - 1).Value);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class BuiltInFnGroupCount : BIFFRecord
	{
		[Order(2uL)]
		public DataItem_UInt16 count;

		public BuiltInFnGroupCount(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class Selection : BIFFRecord
	{
		[Order(2uL)]
		public DataItem_UInt8 pnn;

		[Order(3uL)]
		public DataItem_UInt16 rwAct;

		[Order(4uL)]
		public DataItem_UInt16 colAct;

		[Order(5uL)]
		public DataItem_Int16 irefAct;

		[Order(6uL)]
		public DataItem_UInt16 cref;

		[Order(7uL)]
		public DataItem_UByteArray rgref;

		public Selection(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Type = new DataItem_UInt16(DataToRead);
			Length = new DataItem_UInt16(DataToRead);
			pnn = new DataItem_UInt8(DataToRead);
			rwAct = new DataItem_UInt16(DataToRead);
			colAct = new DataItem_UInt16(DataToRead);
			irefAct = new DataItem_Int16(DataToRead);
			cref = new DataItem_UInt16(DataToRead);
			if (Length.Value >= 9)
			{
				rgref = new DataItem_UByteArray(DataToRead, (ulong)Length.Value.Value - 9uL);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class SST : BIFFRecord
	{
		[Order(2uL)]
		public DataItem_Int32 cstTotal;

		[Order(3uL)]
		public DataItem_Int32 cstUnique;

		[Order(4uL)]
		public List<XLUnicodeRichExtendedString> rgb;

		public SST(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Type = new DataItem_UInt16(DataToRead);
			Length = new DataItem_UInt16(DataToRead);
			if (Length != null && ((int?)Length.Value).HasValue)
			{
				cstTotal = new DataItem_Int32(DataToRead);
				cstUnique = new DataItem_Int32(DataToRead);
				if (cstUnique != null && cstUnique.Value.HasValue)
				{
					rgb = new List<XLUnicodeRichExtendedString>();
					int num = 0;
					while (DataToRead.HasDataLeftToRead && num < cstUnique.Value)
					{
						rgb.Add(new XLUnicodeRichExtendedString(DataToRead));
						num++;
					}
				}
			}
			else
			{
				AddParsingNote(ParsingNoteType.Warning, "Unable to read Length value from an SST record.", base.DataStructureOffset, DataToRead.CurrentPosition - base.DataStructureOffset);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class ColInfo : BIFFRecord
	{
		[Order(2uL)]
		public DataItem_UInt16 colFirst;

		[Order(3uL)]
		public DataItem_UInt16 colLast;

		[Order(4uL)]
		public DataItem_UInt16 coldx;

		[Order(5uL)]
		public DataItem_UInt16 ixfe;

		[Order(6uL)]
		public DataItem_UInt16 byteFlags;

		[Order(7uL)]
		public DataItem_UInt16 unused2;

		public ColInfo(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class Continue : BIFFRecord
	{
		[Order(2uL)]
		public DataItem_UByteArray ContinuedData;

		public Continue(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Type = new DataItem_UInt16(DataToRead);
			Length = new DataItem_UInt16(DataToRead);
			if (Length != null && ((int?)Length.Value).HasValue)
			{
				ContinuedData = new DataItem_UByteArray(DataToRead, Length.Value.Value);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class BoundSheet : BIFFRecord
	{
		[Order(2uL)]
		public DataItem_UInt32 lbPlyPos;

		[BitFieldSize(2u)]
		[BeginBitField(1u)]
		[Order(3uL)]
		public DataItem_UInt8 hsState;

		[Order(4uL)]
		[BitFieldSize(6u)]
		public DataItem_UInt8 unused;

		[Order(5uL)]
		public DataItem_UInt8 dt;

		[Order(6uL)]
		public ShortXLUnicodeString SheetName;

		public BoundSheet(DataInByteArray DataToRead)
			: base(DataToRead)
		{
			if (DataToRead.CurrentPosition != lbPlyPos.Offset + Length.Value.Value)
			{
				DataToRead.Seek(lbPlyPos.Offset + Length.Value.Value);
				base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
				AddParsingNote(ParsingNoteType.Warning, "A BoundSheet contained a different amount of data than its record length indicated would be coming - moving the current position back to where it should be.", base.DataStructureOffset, DataToRead.CurrentPosition - base.DataStructureOffset);
			}
		}

		public override string ToString()
		{
			if (SheetName != null)
			{
				if (SheetName.SheetName_ASCII != null)
				{
					return SheetName.SheetName_ASCII.Value;
				}
				if (SheetName.SheetName_Unicode != null)
				{
					return SheetName.SheetName_Unicode.Value;
				}
			}
			return base.ToString();
		}
	}
	public class LBLFlags : DataStructure
	{
		[BitFieldSize(1u)]
		[BeginBitField(2u)]
		[Order(0uL)]
		public DataItem_UInt16 fHidden;

		[Order(1uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fFunc;

		[BitFieldSize(1u)]
		[Order(2uL)]
		public DataItem_UInt16 fOB;

		[Order(3uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fProc;

		[Order(4uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fCalcExp;

		[BitFieldSize(1u)]
		[Order(5uL)]
		public DataItem_UInt16 fBuiltIn;

		[Order(6uL)]
		[BitFieldSize(6u)]
		public DataItem_UInt16 fGrp;

		[Order(7uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fReserved1;

		[BitFieldSize(1u)]
		[Order(8uL)]
		public DataItem_UInt16 fPublished;

		[BitFieldSize(1u)]
		[Order(9uL)]
		public DataItem_UInt16 fWorkBookParam;

		[BitFieldSize(1u)]
		[Order(10uL)]
		public DataItem_UInt16 fReserved2;

		public LBLFlags(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class LBL : BIFFRecord
	{
		[Order(2uL)]
		public LBLFlags Flags;

		[Order(3uL)]
		public DataItem_UInt8 chKey;

		[Order(4uL)]
		public DataItem_UInt8 cch;

		[Order(5uL)]
		public DataItem_UInt16 cce;

		[Order(6uL)]
		public DataItem_UInt16 reserved3;

		[Order(7uL)]
		public DataItem_UInt16 itab;

		[Order(8uL)]
		public DataItem_UInt8 reserved4;

		[Order(9uL)]
		public DataItem_UInt8 reserved5;

		[Order(10uL)]
		public DataItem_UInt8 reserved6;

		[Order(11uL)]
		public DataItem_UInt8 reserved7;

		[Order(12uL)]
		public DataItem_UInt8 A;

		[Order(13uL)]
		public DataItem_UInt8 NameIndex;

		[Order(14uL)]
		public DataItem_UnicodeString Name_Unicode;

		[Order(15uL)]
		public DataItem_ASCIIString Name_ASCII;

		[Order(16uL)]
		public DataItem_UByteArray rgch;

		[Order(17uL)]
		public DataItem_UByteArray rgce;

		[Order(18uL)]
		public DataItem_UByteArray ExtraBytes;

		public LBL(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Type = new DataItem_UInt16(DataToRead);
			Length = new DataItem_UInt16(DataToRead);
			Flags = new LBLFlags(DataToRead);
			chKey = new DataItem_UInt8(DataToRead);
			cch = new DataItem_UInt8(DataToRead);
			cce = new DataItem_UInt16(DataToRead);
			reserved3 = new DataItem_UInt16(DataToRead);
			itab = new DataItem_UInt16(DataToRead);
			reserved4 = new DataItem_UInt8(DataToRead);
			reserved5 = new DataItem_UInt8(DataToRead);
			reserved6 = new DataItem_UInt8(DataToRead);
			reserved7 = new DataItem_UInt8(DataToRead);
			if (Flags != null)
			{
				if (Flags.fBuiltIn != null)
				{
					ushort? value = Flags.fBuiltIn.Value;
					if (value.GetValueOrDefault() == 0 && value.HasValue)
					{
						A = new DataItem_UInt8(DataToRead);
						if (((A.Value.Value >> 7) & 1) == 1)
						{
							Name_Unicode = new DataItem_UnicodeString(DataToRead, cch.Value.Value);
						}
						else
						{
							Name_ASCII = new DataItem_ASCIIString(DataToRead, cch.Value.Value);
						}
						goto IL_019d;
					}
				}
				NameIndex = new DataItem_UInt8(DataToRead);
				if (cch.Value > 0)
				{
					Name_ASCII = new DataItem_ASCIIString(DataToRead, cch.Value.Value);
				}
			}
			goto IL_019d;
			IL_019d:
			rgce = new DataItem_UByteArray(DataToRead, cce.Value.Value);
			if (DataToRead.CurrentPosition - base.DataStructureOffset < (ulong)((long)Length.Value.Value + 4L))
			{
				ExtraBytes = new DataItem_UByteArray(DataToRead, (ulong)((long)Length.Value.Value + 4L + (long)base.DataStructureOffset) - DataToRead.CurrentPosition);
			}
			if (DataToRead.CurrentPosition != Flags.DataStructureOffset + Length.Value.Value)
			{
				DataToRead.Seek(Flags.DataStructureOffset + Length.Value.Value);
				AddParsingNote(ParsingNoteType.Warning, "An LBL contained a different amount of data than its record length indicated would be coming - moving the current position back to where it should be.", base.DataStructureOffset, DataToRead.CurrentPosition - base.DataStructureOffset);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class Label : BIFFRecord
	{
		[Order(2uL)]
		public Cell cell;

		[Order(3uL)]
		public XLUnicodeString st;

		public Label(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class BIFFRecord_General : BIFFRecord
	{
		[Order(2uL)]
		public DataItem_UByteArray Data;

		public BIFFRecord_General(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Type = new DataItem_UInt16(DataToRead);
			Length = new DataItem_UInt16(DataToRead);
			if (((int?)Length.Value).HasValue)
			{
				Data = new DataItem_UByteArray(DataToRead, Length.Value.Value);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class FilePass : BIFFRecord_General
	{
		public FilePass(DataInByteArray DataToRead)
			: base(DataToRead)
		{
			AddParsingNote(ParsingNoteType.Comment, "FilePass Structure encountered, unable to parse the rest of the file", DataToRead.CurrentPosition);
			throw new ParsingFailed("This file is encrypted");
		}
	}
	public class Hlink : BIFFRecord
	{
		[Order(2uL)]
		public Ref8u ref8;

		[Order(3uL)]
		public DataItem_GUID clsid;

		[Order(4uL)]
		public Hyperlink hyperlink;

		public Hlink(DataInByteArray DataToRead)
			: base(DataToRead)
		{
			if (DataToRead.CurrentPosition != ref8.DataStructureOffset + Length.Value.Value)
			{
				DataToRead.Seek(ref8.DataStructureOffset + Length.Value.Value);
				base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
				AddParsingNote(ParsingNoteType.Warning, "An Hlink record contained a different amount of data than its record length indicated would be coming - moving the current position back to where it should be.", base.DataStructureOffset, DataToRead.CurrentPosition - base.DataStructureOffset);
			}
		}
	}
	public class Obj : BIFFRecord
	{
		[Order(2uL)]
		public FTCmo cmo;

		[Order(3uL)]
		public FTData gmo;

		[Order(4uL)]
		public FTData pictFormat;

		[Order(5uL)]
		public FtPioGrbit pictFlags;

		[Order(6uL)]
		public FtCbls cbls;

		[Order(7uL)]
		public FTRbo rbo;

		[Order(8uL)]
		public FTSbs sbs;

		[Order(9uL)]
		public FtNts nts;

		[Order(10uL)]
		public FtMacro macro;

		[Order(11uL)]
		public FTPictFmla pictFmla;

		[Order(12uL)]
		public ObjLinkFmla linkFmla;

		[Order(13uL)]
		public FTCblsData checkBox;

		[Order(14uL)]
		public FTRboData radioButton;

		[Order(15uL)]
		public FTEboData edit;

		[Order(16uL)]
		public FTLbsData list;

		[Order(17uL)]
		public FTGboData gbo;

		[Order(18uL)]
		public DataItem_UInt32 reserved;

		public Obj(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Type = new DataItem_UInt16(DataToRead);
			Length = new DataItem_UInt16(DataToRead);
			cmo = new FTCmo(DataToRead);
			if (cmo != null && cmo.ot != null && ((int?)cmo.ot.Value).HasValue)
			{
				ushort? value = cmo.ot.Value;
				if (value.GetValueOrDefault() == 0 && value.HasValue)
				{
					gmo = new FTData(DataToRead);
				}
				if (cmo.ot.Value == 8)
				{
					pictFormat = new FTData(DataToRead);
					pictFlags = new FtPioGrbit(DataToRead);
				}
				if (cmo.ot.Value == 11 || cmo.ot.Value == 12)
				{
					cbls = new FtCbls(DataToRead);
				}
				if (cmo.ot.Value == 12)
				{
					rbo = new FTRbo(DataToRead);
				}
				if (cmo.ot.Value == 16 || cmo.ot.Value == 17 || cmo.ot.Value == 18 || cmo.ot.Value == 20)
				{
					sbs = new FTSbs(DataToRead);
				}
				if (cmo.ot.Value == 25)
				{
					nts = new FtNts(DataToRead);
				}
				ushort value2 = DataToRead.PeekUInt16().Value;
				if (value2 == 4)
				{
					macro = new FtMacro(DataToRead);
				}
				if (cmo.ot.Value == 8)
				{
					ushort value3 = DataToRead.PeekUInt16().Value;
					if (value3 == 9)
					{
						pictFmla = new FTPictFmla(pictFlags, DataToRead);
					}
				}
				if (cmo.ot.Value == 11 || cmo.ot.Value == 12 || cmo.ot.Value == 16 || cmo.ot.Value == 17 || cmo.ot.Value == 18 || cmo.ot.Value == 20)
				{
					ushort value4 = DataToRead.PeekUInt16().Value;
					if (((cmo.ot.Value == 11 || cmo.ot.Value == 12) && value4 == 20) || (cmo.ot.Value != 11 && cmo.ot.Value != 12 && value4 == 14))
					{
						linkFmla = new ObjLinkFmla(DataToRead);
					}
				}
				if (cmo.ot.Value == 11 || cmo.ot.Value == 12)
				{
					checkBox = new FTCblsData(DataToRead);
				}
				if (cmo.ot.Value == 12)
				{
					radioButton = new FTRboData(DataToRead);
				}
				if (cmo.ot.Value == 13)
				{
					edit = new FTEboData(DataToRead);
				}
				if (cmo.ot.Value == 18 || cmo.ot.Value == 20)
				{
					list = new FTLbsData(DataToRead, cmo.ot.Value.Value, base.DataStructureOffset + Length.Value.Value);
				}
				if (cmo.ot.Value == 19)
				{
					gbo = new FTGboData(DataToRead);
				}
				if (cmo.ot.Value != 18 && cmo.ot.Value != 20)
				{
					reserved = new DataItem_UInt32(DataToRead);
				}
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class Setup : BIFFRecord_General
	{
		public Setup(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class Note : BIFFRecord
	{
		[Order(2uL)]
		public DataItem_UInt16 row;

		[Order(3uL)]
		public DataItem_UInt16 col;

		[Order(4uL)]
		public DataItem_UByteArray remainingNoteData;

		[Order(5uL)]
		public DataItem_UByteArray revisionStreamNoteData;

		public Note(DataInByteArray DataToRead, bool isRevisionStream)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Type = new DataItem_UInt16(DataToRead);
			Length = new DataItem_UInt16(DataToRead);
			if (Length != null && ((int?)Length.Value).HasValue)
			{
				if (!isRevisionStream)
				{
					row = new DataItem_UInt16(DataToRead);
					col = new DataItem_UInt16(DataToRead);
					if (Length.Value >= 4)
					{
						remainingNoteData = new DataItem_UByteArray(DataToRead, (ulong)Length.Value.Value - 4uL);
					}
				}
				else
				{
					revisionStreamNoteData = new DataItem_UByteArray(DataToRead, Length.Value.Value);
				}
			}
			else
			{
				AddParsingNote(ParsingNoteType.Warning, "Unable to read Length value from a Note record.", base.DataStructureOffset, DataToRead.CurrentPosition - base.DataStructureOffset);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class Style : BIFFRecord
	{
		[Order(2uL)]
		public StyleFlags Flags;

		[Order(3uL)]
		public BuiltInStyle builtInData;

		[Order(4uL)]
		public XLUnicodeString user;

		public Style(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Type = new DataItem_UInt16(DataToRead);
			Length = new DataItem_UInt16(DataToRead);
			Flags = new StyleFlags(DataToRead);
			if (Flags != null && Flags.fBuiltIn != null)
			{
				ushort limit = (ushort)(Length.Value.GetValueOrDefault() - (DataToRead.CurrentPosition - base.DataStructureOffset - 4));
				ushort? value = Flags.fBuiltIn.Value;
				if (value.GetValueOrDefault() == 0 && value.HasValue)
				{
					user = new XLUnicodeString(DataToRead, limit);
					if (user != null && user.cch != null && ((int?)user.cch.Value).HasValue && (user.cch.Value < 1 || user.cch.Value > 255))
					{
						AddParsingNote(ParsingNoteType.Warning, "Found a user defined Style record with an invalid name.", base.DataStructureOffset);
					}
				}
				else
				{
					builtInData = new BuiltInStyle(DataToRead);
					if (builtInData != null && builtInData.istyBuiltIn != null && ((int?)builtInData.istyBuiltIn.Value).HasValue && builtInData.istyBuiltIn.Value > 9)
					{
						AddParsingNote(ParsingNoteType.Warning, "Found a Style record specifying an invalid built-in cell style type.", base.DataStructureOffset);
					}
				}
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class LhnGraph : BIFFRecord
	{
		[Order(2uL)]
		public DataItem_UInt8 lhngType;

		[Order(3uL)]
		public List<DataItem_UInt16> graphName;

		[Order(4uL)]
		public DataItem_UByteArray remainingData;

		public LhnGraph(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Type = new DataItem_UInt16(DataToRead);
			Length = new DataItem_UInt16(DataToRead);
			lhngType = new DataItem_UInt8(DataToRead);
			graphName = new List<DataItem_UInt16>(16);
			if (graphName != null)
			{
				for (int i = 0; i < 16; i++)
				{
					graphName.Add(new DataItem_UInt16(DataToRead));
				}
			}
			if (Length != null && ((int?)Length.Value).HasValue && Length.Value - 33 > 0)
			{
				remainingData = new DataItem_UByteArray(DataToRead, (ulong)Length.Value.Value - 33uL);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class Palette : BIFFRecord
	{
		[Order(2uL)]
		public DataItem_UInt16 ccv;

		[Order(3uL)]
		public List<LongRGB> rgColor;

		public Palette(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Type = new DataItem_UInt16(DataToRead);
			Length = new DataItem_UInt16(DataToRead);
			ccv = new DataItem_UInt16(DataToRead);
			if (ccv != null && ((int?)ccv.Value).HasValue)
			{
				ushort? value = ccv.Value;
				if (value.GetValueOrDefault() != 0 || !value.HasValue)
				{
					rgColor = new List<LongRGB>(ccv.Value.Value);
					for (int i = 0; i < ccv.Value; i++)
					{
						rgColor.Add(new LongRGB(DataToRead));
					}
				}
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class AutoFilter : BIFFRecord
	{
		[Order(2uL)]
		public DataItem_UInt16 iEntry;

		[Order(3uL)]
		public DataItem_UByteArray remaining;

		public int InfoCEntries { get; set; }

		public AutoFilter(DataInByteArray DataToRead)
		{
			InfoCEntries = -1;
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Type = new DataItem_UInt16(DataToRead);
			Length = new DataItem_UInt16(DataToRead);
			iEntry = new DataItem_UInt16(DataToRead);
			if (Length != null && ((int?)Length.Value).HasValue)
			{
				remaining = new DataItem_UByteArray(DataToRead, (ulong)(Length.Value - 2).Value);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class Fbi : BIFFRecord
	{
		[Order(2uL)]
		public DataItem_UInt16 dmixBasis;

		[Order(3uL)]
		public DataItem_UInt16 dmiyBasis;

		[Order(4uL)]
		public DataItem_UInt16 twpHeightBasis;

		[Order(5uL)]
		public DataItem_UInt16 scab;

		[Order(6uL)]
		public FontIndex ifnt;

		public Fbi(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class AutoFilterInfo : BIFFRecord
	{
		[Order(2uL)]
		public DataItem_UInt16 cEntries;

		public AutoFilterInfo(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class Qsi : BIFFRecord
	{
		[BeginBitField(2u)]
		[BitFieldSize(1u)]
		[Order(2uL)]
		public DataItem_UInt16 fTitles;

		[Order(3uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fRowNums;

		[BitFieldSize(1u)]
		[Order(4uL)]
		public DataItem_UInt16 fDisableRefresh;

		[Order(5uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fAsync;

		[BitFieldSize(1u)]
		[Order(6uL)]
		public DataItem_UInt16 fNewAsync;

		[BitFieldSize(1u)]
		[Order(7uL)]
		public DataItem_UInt16 fAutoRefresh;

		[BitFieldSize(1u)]
		[Order(8uL)]
		public DataItem_UInt16 fShrink;

		[BitFieldSize(1u)]
		[Order(9uL)]
		public DataItem_UInt16 fFill;

		[BitFieldSize(1u)]
		[Order(10uL)]
		public DataItem_UInt16 fAutoFormat;

		[BitFieldSize(1u)]
		[Order(11uL)]
		public DataItem_UInt16 fSaveData;

		[BitFieldSize(1u)]
		[Order(12uL)]
		public DataItem_UInt16 fDisableEdit;

		[Order(13uL)]
		[BitFieldSize(2u)]
		public DataItem_UInt16 unused1;

		[Order(14uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fOverwrite;

		[Order(15uL)]
		[BitFieldSize(2u)]
		public DataItem_UInt16 unused2;

		[Order(16uL)]
		public DataItem_UInt16 itblAutoFmt;

		[Order(17uL)]
		[BitFieldSize(1u)]
		[BeginBitField(2u)]
		public DataItem_UInt16 fibitAtrNum;

		[Order(18uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fibitAtrFnt;

		[BitFieldSize(1u)]
		[Order(19uL)]
		public DataItem_UInt16 fibitAtrAlc;

		[Order(20uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fibitAtrBdr;

		[BitFieldSize(1u)]
		[Order(21uL)]
		public DataItem_UInt16 fibitAtrPat;

		[Order(22uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fibitAtrProt;

		[Order(23uL)]
		[BitFieldSize(10u)]
		public DataItem_UInt16 unused3;

		[Order(24uL)]
		public DataItem_UInt32 reserved;

		[Order(25uL)]
		public XLUnicodeString rgchName;

		[Order(26uL)]
		public DataItem_UInt16 unused4;

		public Qsi(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class FrtFlags : DataStructure
	{
		[BeginBitField(2u)]
		[BitFieldSize(1u)]
		[Order(0uL)]
		public DataItem_UInt16 frtRef;

		[Order(1uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 frtAlert;

		[Order(2uL)]
		[BitFieldSize(14u)]
		public DataItem_UInt16 reserved;

		public FrtFlags(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class FrtRefHeaderU : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 rt;

		[Order(1uL)]
		public FrtFlags grbitFrt;

		[Order(2uL)]
		public Ref8u ref8;

		public FrtRefHeaderU(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class FrtHeaderOld : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 rt;

		[Order(1uL)]
		public FrtFlags grbitFrt;

		public FrtHeaderOld(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class Qsir : BIFFRecord
	{
		[Order(2uL)]
		public FrtRefHeaderU refHeader;

		[Order(3uL)]
		public DataItem_UInt16 cbQsirSaved;

		[Order(4uL)]
		public DataItem_UInt16 cbQsifSaved;

		[Order(5uL)]
		[BeginBitField(4u)]
		[BitFieldSize(1u)]
		public DataItem_UInt32 fPersist;

		[Order(6uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt32 fPersistSort;

		[Order(7uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt32 fPersistAutoFilter;

		[BitFieldSize(16u)]
		[Order(8uL)]
		public DataItem_UInt32 reserved1;

		[Order(9uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt32 reserved2;

		[BitFieldSize(1u)]
		[Order(10uL)]
		public DataItem_UInt32 fSorted;

		[BitFieldSize(1u)]
		[Order(11uL)]
		public DataItem_UInt32 fCaseSensSort;

		[Order(12uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt32 fHdrRowSort;

		[BitFieldSize(1u)]
		[Order(13uL)]
		public DataItem_UInt32 fIdWrapped;

		[BitFieldSize(1u)]
		[Order(14uL)]
		public DataItem_UInt32 reserved3;

		[Order(15uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt32 fTitlesOld;

		[BitFieldSize(5u)]
		[Order(16uL)]
		public DataItem_UInt32 wVerBeforeRefreshAlert;

		[Order(17uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt32 reserved4;

		[Order(18uL)]
		public DataItem_UInt32 iSortCustom;

		[Order(19uL)]
		public DataItem_UInt32 cQsif;

		[Order(20uL)]
		public DataItem_UInt32 cpstDeleted;

		[Order(21uL)]
		public DataItem_UInt32 idFieldNext;

		[Order(22uL)]
		public DataItem_UInt16 ccolExtraLeft;

		[Order(23uL)]
		public DataItem_UInt16 ccolExtraRight;

		[Order(24uL)]
		public DataItem_UInt32 idList;

		[Order(25uL)]
		[DoNotAutoProcess]
		public XLUnicodeString rgbTitle;

		public Qsir(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}

		protected override void ParseData(DataInByteArray DataToRead)
		{
			base.ParseData(DataToRead);
			uint? value = cpstDeleted.Value;
			if (value.GetValueOrDefault() != 0 || !value.HasValue)
			{
				rgbTitle = new XLUnicodeString(DataToRead, cpstDeleted.Value.Value);
			}
		}
	}
	public class Qsif : BIFFRecord
	{
		[Order(2uL)]
		public FrtHeaderOld refHeader;

		[Order(3uL)]
		[BitFieldSize(1u)]
		[BeginBitField(4u)]
		public DataItem_UInt32 fUserIns;

		[BitFieldSize(1u)]
		[Order(4uL)]
		public DataItem_UInt32 fFillDown;

		[BitFieldSize(1u)]
		[Order(5uL)]
		public DataItem_UInt32 fSortDes;

		[Order(6uL)]
		[BitFieldSize(8u)]
		public DataItem_UInt32 iSortKey;

		[BitFieldSize(1u)]
		[Order(7uL)]
		public DataItem_UInt32 fRowNums;

		[Order(8uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt32 reserved1;

		[BitFieldSize(1u)]
		[Order(9uL)]
		public DataItem_UInt32 fSorted;

		[BitFieldSize(2u)]
		[Order(10uL)]
		public DataItem_UInt32 reserved2;

		[BitFieldSize(1u)]
		[Order(11uL)]
		public DataItem_UInt32 fClipped;

		[BitFieldSize(15u)]
		[Order(12uL)]
		public DataItem_UInt32 reserved3;

		[Order(13uL)]
		public DataItem_UInt32 idField;

		[Order(14uL)]
		[DoNotAutoProcess]
		public DataItem_UInt32 idList;

		[Order(15uL)]
		public XLUnicodeString rgbTitle;

		public Qsif(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}

		protected override void ParseData(DataInByteArray DataToRead)
		{
			base.ParseData(DataToRead);
		}
	}
	public class QsiSXTag : BIFFRecord
	{
		[Order(2uL)]
		public DataItem_UInt32 frtHeaderOld;

		[Order(3uL)]
		public DataItem_UInt16 fSx;

		[Order(4uL)]
		[BitFieldSize(1u)]
		[BeginBitField(2u)]
		public DataItem_UInt16 fEnableRefresh;

		[Order(5uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fInvalid;

		[BitFieldSize(1u)]
		[Order(6uL)]
		public DataItem_UInt16 fTensorEx;

		[BitFieldSize(13u)]
		[Order(7uL)]
		public DataItem_UInt16 reserved1;

		[Order(8uL)]
		public DataItem_UInt32 dwQsiFuture;

		[Order(9uL)]
		public DataItem_UInt8 verSxLastUpdated;

		[Order(10uL)]
		public DataItem_UInt8 verSxUpdatableMin;

		[Order(11uL)]
		public DataItem_UInt8 obCchName;

		[Order(12uL)]
		public DataItem_UInt8 reserved2;

		[Order(13uL)]
		public XLUnicodeString stName;

		[Order(14uL)]
		public DataItem_UInt16 unused;

		public QsiSXTag(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class CF : BIFFRecord
	{
		[Order(2uL)]
		public DataItem_UInt8 ct;

		[Order(3uL)]
		public DataItem_UInt8 cp;

		[Order(4uL)]
		public DataItem_UInt16 cce1;

		[Order(5uL)]
		public DataItem_UInt16 cce2;

		[Order(6uL)]
		public DXFN rgbdxf;

		[Order(7uL)]
		public DataItem_UByteArray remainingBytes;

		public CF(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Type = new DataItem_UInt16(DataToRead);
			Length = new DataItem_UInt16(DataToRead);
			ct = new DataItem_UInt8(DataToRead);
			cp = new DataItem_UInt8(DataToRead);
			cce1 = new DataItem_UInt16(DataToRead);
			cce2 = new DataItem_UInt16(DataToRead);
			rgbdxf = new DXFN(DataToRead);
			if (cce1 != null && ((int?)cce1.Value).HasValue && cce2 != null && ((int?)cce2.Value).HasValue)
			{
				remainingBytes = new DataItem_UByteArray(DataToRead, (ulong)(cce1.Value + cce2.Value).Value);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class FrtWrapper : BIFFRecord_General
	{
		public FrtWrapper(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class DVal : BIFFRecord
	{
		[BitFieldSize(1u)]
		[Order(2uL)]
		[BeginBitField(2u)]
		public DataItem_UInt16 fWnClosed;

		[BitFieldSize(1u)]
		[Order(3uL)]
		public DataItem_UInt16 reserved1;

		[BitFieldSize(1u)]
		[Order(4uL)]
		public DataItem_UInt16 unused;

		[BitFieldSize(13u)]
		[Order(5uL)]
		public DataItem_UInt16 reserved2;

		[Order(6uL)]
		public DataItem_UInt32 xLeft;

		[Order(7uL)]
		public DataItem_UInt32 yTop;

		[Order(8uL)]
		public DataItem_Int32 idObj;

		[Order(9uL)]
		public DataItem_UInt32 idvMac;

		public DVal(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class Window1 : BIFFRecord
	{
		[Order(2uL)]
		public DataItem_Int16 xWn;

		[Order(3uL)]
		public DataItem_Int16 yWn;

		[Order(4uL)]
		public DataItem_Int16 dxWn;

		[Order(5uL)]
		public DataItem_Int16 dyWn;

		[Order(6uL)]
		public DataItem_UInt16 unparsedFlags;

		[Order(7uL)]
		public TabIndex itabCur;

		[Order(8uL)]
		public TabIndex itabFirst;

		[Order(9uL)]
		public DataItem_UInt16 ctabSek;

		[Order(10uL)]
		public DataItem_UInt16 wTabRatio;

		public Window1(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class XF : BIFFRecord
	{
		[Order(2uL)]
		public FontIndex ifnt;

		[Order(3uL)]
		public IFmt ifmt;

		[Order(4uL)]
		public XFFlags Flags;

		[Order(5uL)]
		public DataItem_ByteArray Data;

		public XF(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Type = new DataItem_UInt16(DataToRead);
			Length = new DataItem_UInt16(DataToRead);
			ifnt = new FontIndex(DataToRead);
			ifmt = new IFmt(DataToRead);
			Flags = new XFFlags(DataToRead);
			ushort num = (ushort)(Length.Value.GetValueOrDefault() - (DataToRead.CurrentPosition - base.DataStructureOffset - 4));
			Data = new DataItem_ByteArray(DataToRead, num);
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class Country : BIFFRecord_General
	{
		public Country(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class TxO : BIFFRecord_General
	{
		public TxO(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class Formula : BIFFRecord
	{
		[Order(2uL)]
		public DataItem_UByteArray BeginningOfRecord;

		[Order(3uL)]
		public CellParsedFormula formula;

		public Formula(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Type = new DataItem_UInt16(DataToRead);
			Length = new DataItem_UInt16(DataToRead);
			BeginningOfRecord = new DataItem_UByteArray(DataToRead, 20uL);
			if (Length != null && ((int?)Length.Value).HasValue && Length.Value > 20)
			{
				formula = new CellParsedFormula(DataToRead, (ulong)Length.Value.Value - 20uL);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class ExcelBinaryDocument : DataStructure
	{
		private OLESSDirectoryEntry ExcelDocumentStream;

		[Order(0uL)]
		public SubStream Globals;

		[Order(1uL)]
		public List<SubStream> Worksheets;

		public ExcelBinaryDocument(DataInByteArray Data, OLESSDataFile OLESSRoot, OLESSDirectoryEntry TheExcelDocumentStream)
		{
			Data.Seek(TheExcelDocumentStream.Data.Offset);
			base.DataStructureOffset = Data.CurrentPosition;
			base.DataStructureLength = TheExcelDocumentStream.Data.Length;
			ExcelDocumentStream = TheExcelDocumentStream;
			Globals = new SubStream(Data);
			Worksheets = new List<SubStream>();
			foreach (BIFFRecord bIFFRecord in Globals.BIFFRecords)
			{
				if (bIFFRecord is BoundSheet)
				{
					BoundSheet boundSheet = bIFFRecord as BoundSheet;
					if (boundSheet.lbPlyPos != null)
					{
						Data.Seek(ExcelDocumentStream.Data.Offset + boundSheet.lbPlyPos.Value.Value);
						Worksheets.Add(new SubStream(Data));
					}
				}
			}
		}

		public ulong? GetExcelDocumentStreamOffset()
		{
			if (ExcelDocumentStream == null || ExcelDocumentStream.Data == null)
			{
				return null;
			}
			return ExcelDocumentStream.Data.Offset;
		}
	}
	public class ExcelBIFF8BinaryFormat : DataFormat
	{
		public OLESSDataFile OLESSRoot;

		public List<ExcelBinaryDocument> ExcelBinaryDocuments;

		protected override void ParseData(DataInByteArray Data)
		{
			OLESSRoot = new OLESSDataFile(Data);
			ExcelBinaryDocuments = new List<ExcelBinaryDocument>();
			if (OLESSRoot.DirectoryEntries != null)
			{
				foreach (OLESSDirectoryEntry directoryEntry in OLESSRoot.DirectoryEntries)
				{
					if (string.Compare(directoryEntry.EleName.Value.Replace("\0", ""), "WORKBOOK", ignoreCase: true) == 0)
					{
						ExcelBinaryDocuments.Add(new ExcelBinaryDocument(Data, OLESSRoot, directoryEntry));
					}
				}
			}
			if (ExcelBinaryDocuments.Count == 0)
			{
				AddParsingNote(ParsingNoteType.ParserNotApplicable, "'Workbook' stream was not found.");
			}
		}

		public static BIFFRecord FigureOutWhatTypeOfRecordThisIs(DataInByteArray Data)
		{
			BIFFRecord bIFFRecord = null;
			ushort? num = Data.PeekUInt16();
			if (!((int?)num).HasValue)
			{
				return null;
			}
			return (BIFFRecordType)num.Value switch
			{
				BIFFRecordType.BOF => new BOF(Data), 
				BIFFRecordType.EOF => new EOF(Data), 
				BIFFRecordType.Obj => new Obj(Data), 
				BIFFRecordType.MsoDrawing => new MSODrawing(ExtendThroughTrailingContinueRecords(Data)), 
				BIFFRecordType.MsoDrawingGroup => new MSODrawingGroup(ExtendThroughTrailingContinueRecords(Data)), 
				BIFFRecordType.BoundSheet8 => new BoundSheet(Data), 
				BIFFRecordType.Lbl => new LBL(Data), 
				BIFFRecordType.SST => new SST(ExtendThroughTrailingContinueRecords(Data)), 
				BIFFRecordType.Continue => new Continue(Data), 
				BIFFRecordType.BuiltInFnGroupCount => new BuiltInFnGroupCount(Data), 
				BIFFRecordType.Selection => new Selection(Data), 
				BIFFRecordType.Label => new Label(Data), 
				BIFFRecordType.HLink => new Hlink(Data), 
				BIFFRecordType.ColInfo => new ColInfo(Data), 
				BIFFRecordType.Setup => new Setup(Data), 
				BIFFRecordType.Note => new Note(Data, isRevisionStream: false), 
				BIFFRecordType.Style => new Style(Data), 
				BIFFRecordType.LhnGraph => new LhnGraph(Data), 
				BIFFRecordType.Palette => new Palette(Data), 
				BIFFRecordType.AutoFilterInfo => new AutoFilterInfo(Data), 
				BIFFRecordType.AutoFilter => new AutoFilter(Data), 
				BIFFRecordType.Fbi => new Fbi(Data), 
				BIFFRecordType.Qsi => new Qsi(Data), 
				BIFFRecordType.Qsir => new Qsir(Data), 
				BIFFRecordType.Qsif => new Qsif(Data), 
				BIFFRecordType.QsiSXTag => new QsiSXTag(Data), 
				BIFFRecordType.CF => new CF(Data), 
				BIFFRecordType.FrtWrapper => new FrtWrapper(Data), 
				BIFFRecordType.DVal => new DVal(Data), 
				BIFFRecordType.FilePass => new FilePass(Data), 
				BIFFRecordType.Window1 => new Window1(Data), 
				BIFFRecordType.XF => new XF(Data), 
				BIFFRecordType.Country => new Country(Data), 
				BIFFRecordType.TxO => new TxO(Data), 
				BIFFRecordType.Formula => new Formula(Data), 
				_ => new BIFFRecord_General(Data), 
			};
		}

		public static DataInByteArray ExtendThroughTrailingContinueRecords(DataInByteArray DataToRead)
		{
			if (DataToRead.PeekUInt16(4uL + (ulong)DataToRead.PeekUInt16(2uL).Value).Value != 60)
			{
				return DataToRead;
			}
			ulong currentPosition = DataToRead.CurrentPosition;
			ulong num = 0uL;
			List<int> list = new List<int>();
			List<OffsetMapping> list2 = new List<OffsetMapping>();
			int value = DataToRead.PeekUInt16(2uL).Value;
			list.Add(value + 4);
			num = (ulong)value + 4uL;
			ushort? num2 = null;
			ulong num3 = 0uL;
			do
			{
				int value2 = DataToRead.PeekUInt16((ulong)((long)num + (long)((list.Count - 1) * 4) + 2)).Value;
				list.Add(value2);
				num3 += 4;
				list2.Add(new OffsetMapping(num, num3));
				num += (ulong)value2;
				num2 = DataToRead.PeekUInt16(num + (ulong)((list.Count - 1) * 4));
			}
			while (((int?)num2).HasValue && num2 == 60);
			byte[] array = new byte[num];
			int num4 = 0;
			for (int i = 0; i < list.Count; i++)
			{
				if (i != 0)
				{
					DataToRead.Seek(DataToRead.CurrentPosition + 4);
				}
				Buffer.BlockCopy(DataToRead.Data, (int)DataToRead.CurrentPosition, array, num4, list[i]);
				DataToRead.Seek(DataToRead.CurrentPosition + (ulong)list[i]);
				num4 += list[i];
			}
			DataInByteArray dataInByteArray = new DataInByteArray(array, currentPosition);
			dataInByteArray.OffsetMappings = list2;
			return dataInByteArray;
		}

		public static void SplitBOFVersion(ushort readVersion, out ushort MajorVersion, out ushort MinorVersion)
		{
			MinorVersion = (ushort)(readVersion & 0xFF);
			MajorVersion = (ushort)(readVersion & 0xFF00);
		}
	}
}
namespace GUT.DataFormats.WordBinaryFormat
{
	public class WordBinaryFormat : DataFormat
	{
		public OLESSDataFile OLESSRoot;

		public List<WordBinaryDocument> WordBinaryDocuments;

		protected override void ParseData(DataInByteArray Data)
		{
			OLESSRoot = new OLESSDataFile(Data);
			WordBinaryDocuments = new List<WordBinaryDocument>();
			if (OLESSRoot.DirectoryEntries == null)
			{
				return;
			}
			foreach (OLESSDirectoryEntry directoryEntry in OLESSRoot.DirectoryEntries)
			{
				if (string.Compare(directoryEntry.EleName.Value.Replace("\0", ""), "WORDDOCUMENT", ignoreCase: true) == 0)
				{
					WordBinaryDocuments.Add(new WordBinaryDocument(Data, OLESSRoot, directoryEntry));
				}
			}
		}
	}
	[CanDetect("CVE-2006-4534")]
	[CanDetect("CVE-2007-0515")]
	public class WordBinaryFormatDetectionLogic : WordBinaryFormat
	{
		[CanDetect("CVE-2007-0870")]
		public void DetectCVE_2007_0870()
		{
			if (WordBinaryDocuments == null)
			{
				return;
			}
			foreach (WordBinaryDocument wordBinaryDocument in WordBinaryDocuments)
			{
				if (wordBinaryDocument != null)
				{
					if (wordBinaryDocument.Real_Undo != null && wordBinaryDocument.Real_Undo.Value > wordBinaryDocument.WordDocumentStream.Data.Length - 250)
					{
						AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Found a fcDocUndoWord9 a value that was too big (it was " + wordBinaryDocument.WordFIB.FIBTable97.fcDocUndoWord9.Value + ")", "CVE-2007-0870", wordBinaryDocument.Real_Undo.Offset, wordBinaryDocument.Real_Undo.Length);
					}
					if (wordBinaryDocument.fcMin_Undo != null && wordBinaryDocument.Real_Undo != null && wordBinaryDocument.fcMin_Undo.Value < wordBinaryDocument.Real_Undo.Value)
					{
						AddParsingNote(ParsingNoteType.DefinitelyMalicious, "fcMin_Undo is smaller than Real_Undo", "CVE-2007-0870", wordBinaryDocument.fcMin_Undo.Offset, wordBinaryDocument.fcMin_Undo.Length);
					}
					if (wordBinaryDocument.fcMin_Undo != null && wordBinaryDocument.fcMin_Undo.Value > wordBinaryDocument.WordDocumentStream.Data.Length)
					{
						AddParsingNote(ParsingNoteType.DefinitelyMalicious, "fcMin_Undo is larger than the WordDocumentStream length", "CVE-2007-0870", wordBinaryDocument.fcMin_Undo.Offset, wordBinaryDocument.fcMin_Undo.Length);
					}
				}
			}
		}

		[CanDetect("CVE-2006-4534")]
		public void DetectCVE_2006_4534()
		{
			if (WordBinaryDocuments == null)
			{
				return;
			}
			foreach (WordBinaryDocument wordBinaryDocument in WordBinaryDocuments)
			{
				if (wordBinaryDocument == null)
				{
					continue;
				}
				bool flag = false;
				if (wordBinaryDocument.WordFIB != null && wordBinaryDocument.WordFIB.FIBTable97 != null && wordBinaryDocument.WordFIB.FIBBase.wIdent != null && ((int?)wordBinaryDocument.WordFIB.FIBBase.wIdent.Value).HasValue)
				{
					switch (wordBinaryDocument.WordFIB.FIBBase.wIdent.Value)
					{
					case 32920:
					case 32921:
					case 42647:
					case 42648:
					case 42649:
					case 42650:
					case 42651:
						AddParsingNote(ParsingNoteType.Warning, "Magic trigger: " + wordBinaryDocument.WordFIB.FIBBase.wIdent.Value, "CVE-2006-4534", wordBinaryDocument.WordFIB.FIBBase.wIdent.Offset, wordBinaryDocument.WordFIB.FIBBase.wIdent.Length);
						flag = true;
						break;
					}
				}
				if (wordBinaryDocument.WordFIB != null && wordBinaryDocument.WordFIB.FibRgCswNew != null && wordBinaryDocument.WordFIB.FibRgCswNew.nFibNew != null && ((int?)wordBinaryDocument.WordFIB.FibRgCswNew.nFibNew.Value).HasValue && wordBinaryDocument.WordFIB.FIBBase != null && wordBinaryDocument.WordFIB.FIBBase.nFib != null && ((int?)wordBinaryDocument.WordFIB.FIBBase.nFib.Value).HasValue)
				{
					ushort? value = wordBinaryDocument.WordFIB.FibRgCswNew.nFibNew.Value;
					if (value.GetValueOrDefault() == 0 && value.HasValue && wordBinaryDocument.WordFIB.FIBBase.nFib.Value < 205)
					{
						AddParsingNote(ParsingNoteType.Warning, "TheWordBinaryDocument.WordFIB.FIBBase.nFib trigger: " + wordBinaryDocument.WordFIB.FIBBase.nFib.Value, "CVE-2006-4534", wordBinaryDocument.WordFIB.FIBBase.nFib.Offset, wordBinaryDocument.WordFIB.FIBBase.nFib.Length);
						flag = true;
					}
					else if (wordBinaryDocument.WordFIB.FibRgCswNew.nFibNew.Value < 205)
					{
						AddParsingNote(ParsingNoteType.Warning, "TheWordBinaryDocument.WordFIB.FibRgCswNew.nFibNew trigger: " + wordBinaryDocument.WordFIB.FibRgCswNew.nFibNew.Value, "CVE-2006-4534", wordBinaryDocument.WordFIB.FibRgCswNew.nFibNew.Offset, wordBinaryDocument.WordFIB.FibRgCswNew.nFibNew.Length);
						flag = true;
					}
				}
				if (!flag || (wordBinaryDocument.WordFIB.FIBTable97 != null && wordBinaryDocument.WordFIB.FIBTable97.lcbDop != null && wordBinaryDocument.WordFIB.FIBTable97.lcbDop.Value.HasValue && wordBinaryDocument.WordFIB.FIBTable97.lcbDop.Value <= 92) || wordBinaryDocument.WordFIB.FIBTable97 == null || wordBinaryDocument.WordFIB.FIBTable97.fcDop == null)
				{
					continue;
				}
				ulong num = wordBinaryDocument.WordFIB.FIBTable97.fcDop.Value.Value;
				if (wordBinaryDocument.GetActiveTableStream() != null)
				{
					if (BitConverter.ToUInt16(wordBinaryDocument.GetActiveTableStream().Data.Value, (int)num + 92) > 100)
					{
						ulong offset = wordBinaryDocument.GetActiveTableStream().Data.Offset + num + 92;
						AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Offset " + offset + " triggers the vuln!", "CVE-2006-4534", offset, 2uL);
					}
					if (BitConverter.ToUInt16(wordBinaryDocument.GetActiveTableStream().Data.Value, (int)num + 94) > 50)
					{
						ulong offset2 = wordBinaryDocument.GetActiveTableStream().Data.Offset + num + 94;
						AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Offset " + offset2 + " triggers the vuln!", "CVE-2006-4534", offset2, 2uL);
					}
				}
			}
		}

		[CanDetect("CVE-2006-6456")]
		public void DetectCVE_2006_6456()
		{
			if (WordBinaryDocuments == null)
			{
				return;
			}
			foreach (WordBinaryDocument wordBinaryDocument in WordBinaryDocuments)
			{
				if (wordBinaryDocument == null || wordBinaryDocument.Sepxs == null)
				{
					continue;
				}
				foreach (SEPX sepx in wordBinaryDocument.Sepxs)
				{
					if (sepx == null || sepx.grpprl == null)
					{
						continue;
					}
					foreach (PRL item in sepx.grpprl)
					{
						if (item != null && item.sprm != null && item.sprm.sgc != null && item.sprm.sgc.Value == 7)
						{
							AddParsingNote(ParsingNoteType.PossiblyMalicious, "sprm.sgc == 7, This is probably malicious, although there is a small chance of a false positive", "CVE-2006-6456", item.sprm.sgc.Offset, item.sprm.sgc.Length);
						}
					}
				}
			}
			foreach (WordBinaryDocument wordBinaryDocument2 in WordBinaryDocuments)
			{
				if (wordBinaryDocument2 == null || wordBinaryDocument2.stChpxFKPs == null)
				{
					continue;
				}
				foreach (CHPXFKP stChpxFKP in wordBinaryDocument2.stChpxFKPs)
				{
					if (stChpxFKP == null || stChpxFKP.Chpxs == null)
					{
						continue;
					}
					foreach (CHPX chpx in stChpxFKP.Chpxs)
					{
						if (chpx == null || chpx.grpprl == null)
						{
							continue;
						}
						foreach (PRL item2 in chpx.grpprl)
						{
							if (item2 != null && item2.sprm != null && item2.sprm.sgc != null && item2.sprm.sgc.Value == 7)
							{
								AddParsingNote(ParsingNoteType.PossiblyMalicious, "sprm.sgc == 7, This is probably malicious, although there is a small chance of a false positive", "CVE-2006-6456", item2.sprm.sgc.Offset, item2.sprm.sgc.Length);
							}
						}
					}
				}
			}
			foreach (WordBinaryDocument wordBinaryDocument3 in WordBinaryDocuments)
			{
				if (wordBinaryDocument3 == null || wordBinaryDocument3.stPapxFKPs == null)
				{
					continue;
				}
				foreach (PAPXFKP stPapxFKP in wordBinaryDocument3.stPapxFKPs)
				{
					if (stPapxFKP == null || stPapxFKP.PapxInFkps == null)
					{
						continue;
					}
					foreach (PAPXINFKP papxInFkp in stPapxFKP.PapxInFkps)
					{
						if (papxInFkp == null || papxInFkp.grpprlInPapx == null || papxInFkp.grpprlInPapx.grpprl == null)
						{
							continue;
						}
						foreach (PRL item3 in papxInFkp.grpprlInPapx.grpprl)
						{
							if (item3 != null && item3.sprm != null && item3.sprm.sgc != null && item3.sprm.sgc.Value == 7)
							{
								AddParsingNote(ParsingNoteType.PossiblyMalicious, "sprm.sgc == 7, This is probably malicious, although there is a small chance of a false positive", "CVE-2006-6456", item3.sprm.sgc.Offset, item3.sprm.sgc.Length);
							}
						}
					}
				}
			}
		}

		[CanDetect("CVE-2006-5994")]
		public void DetectCVE_2006_5994()
		{
			if (WordBinaryDocuments == null)
			{
				return;
			}
			foreach (WordBinaryDocument wordBinaryDocument in WordBinaryDocuments)
			{
				if (wordBinaryDocument == null || wordBinaryDocument.Clx == null || wordBinaryDocument.Clx.Pcdt == null || wordBinaryDocument.Clx.Pcdt.PlcPcd == null)
				{
					continue;
				}
				if (wordBinaryDocument.Clx.Pcdt.PlcPcd.aCP != null && wordBinaryDocument.Clx.Pcdt.PlcPcd.aCP.Count >= 1)
				{
					uint? value = wordBinaryDocument.Clx.Pcdt.PlcPcd.aCP[0].Value;
					if (value.GetValueOrDefault() != 0 || !value.HasValue)
					{
						AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Found corruption in a PLCPCD (CP[0] was not 0)", "CVE-2006-5994", wordBinaryDocument.Clx.Pcdt.PlcPcd.aCP[0].Offset, wordBinaryDocument.Clx.Pcdt.PlcPcd.aCP[0].Length);
					}
				}
				if (wordBinaryDocument.Clx.Pcdt.PlcPcd.aCP == null)
				{
					continue;
				}
				for (int i = 1; i < wordBinaryDocument.Clx.Pcdt.PlcPcd.aCP.Count; i++)
				{
					if (wordBinaryDocument.Clx.Pcdt.PlcPcd.aCP[i - 1].Value > wordBinaryDocument.Clx.Pcdt.PlcPcd.aCP[i].Value)
					{
						AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Found corruption in a PLCPCD (not in ascending order)", "CVE-2006-5994", wordBinaryDocument.Clx.Pcdt.PlcPcd.aCP[i].Offset, wordBinaryDocument.Clx.Pcdt.PlcPcd.aCP[i].Length);
					}
				}
			}
		}

		[CanDetect("CVE-2007-0515")]
		public void DetectCVE_2007_0515()
		{
			if (WordBinaryDocuments == null)
			{
				return;
			}
			foreach (WordBinaryDocument wordBinaryDocument in WordBinaryDocuments)
			{
				if (wordBinaryDocument == null)
				{
					continue;
				}
				if (wordBinaryDocument.WordFIB != null && wordBinaryDocument.WordFIB.FIBTable97 != null && wordBinaryDocument.WordFIB.FIBBase.wIdent != null && ((int?)wordBinaryDocument.WordFIB.FIBBase.wIdent.Value).HasValue)
				{
					switch (wordBinaryDocument.WordFIB.FIBBase.wIdent.Value)
					{
					case 32920:
					case 32921:
					case 42647:
					case 42648:
					case 42649:
					case 42650:
					case 42651:
						continue;
					}
				}
				if (wordBinaryDocument.WordFIB != null && wordBinaryDocument.WordFIB.FIBBase != null && wordBinaryDocument.WordFIB.FIBBase.fExtChar != null && ((int?)wordBinaryDocument.WordFIB.FIBBase.fExtChar.Value).HasValue)
				{
					byte? value = wordBinaryDocument.WordFIB.FIBBase.fExtChar.Value;
					if (value.GetValueOrDefault() == 0 && value.HasValue)
					{
						continue;
					}
				}
				if (wordBinaryDocument.GetActiveTableStream() != null && wordBinaryDocument.WordFIB.FIBTable97 != null && wordBinaryDocument.WordFIB.FIBTable97.lcbGrpXstAtnOwners != null && wordBinaryDocument.WordFIB.FIBTable97.lcbGrpXstAtnOwners.Value.HasValue && wordBinaryDocument.WordFIB.FIBTable97.fcGrpXstAtnOwners != null && wordBinaryDocument.WordFIB.FIBTable97.fcGrpXstAtnOwners.Value.HasValue && wordBinaryDocument.WordFIB.FIBTable97.lcbGrpXstAtnOwners.Value > wordBinaryDocument.GetActiveTableStream().Data.Length - wordBinaryDocument.WordFIB.FIBTable97.fcGrpXstAtnOwners.Value)
				{
					AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Structure length out of bounds", "CVE-2007-0515", wordBinaryDocument.WordFIB.FIBTable97.lcbGrpXstAtnOwners.Offset, wordBinaryDocument.WordFIB.FIBTable97.lcbGrpXstAtnOwners.Length);
				}
				if (wordBinaryDocument.GrpXstAtnOwners == null || wordBinaryDocument.GrpXstAtnOwners.XSTs == null)
				{
					continue;
				}
				foreach (XST xST in wordBinaryDocument.GrpXstAtnOwners.XSTs)
				{
					if (xST != null && xST.cch != null && ((int?)xST.cch.Value).HasValue && xST.cch.Value > 255)
					{
						AddParsingNote(ParsingNoteType.DefinitelyMalicious, "XST length is > 255 (it is " + xST.cch.Value, "CVE-2007-0515", xST.cch.Offset, xST.cch.Length);
					}
				}
			}
		}

		[CanDetect("CVE-2006-2492")]
		public void DetectCVE_2006_2492()
		{
			if (WordBinaryDocuments == null)
			{
				return;
			}
			foreach (WordBinaryDocument wordBinaryDocument in WordBinaryDocuments)
			{
				if (wordBinaryDocument == null || wordBinaryDocument.SmartTagSectionInTableStream == null)
				{
					continue;
				}
				foreach (SmartTag smartTag in wordBinaryDocument.SmartTagSectionInTableStream.SmartTags)
				{
					if (smartTag.Pointer != null)
					{
						uint? value = smartTag.Pointer.Value;
						if ((value.GetValueOrDefault() != 0 || !value.HasValue) && !FIBStructureIsValidForCVE_2006_2492(wordBinaryDocument))
						{
							AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Found a SmartTag with a non-zero pointer (it was " + smartTag.Pointer.Value + ") and the FIB values were invalid", "CVE-2006-2492", smartTag.DataStructureOffset, smartTag.DataStructureLength);
						}
					}
				}
			}
		}

		private bool FIBStructureIsValidForCVE_2006_2492(WordBinaryDocument WordDocument)
		{
			if (WordDocument.WordFIB.FIBTable2002 == null || WordDocument.WordFIB.FIBTable2002.fcPlcfBkfFactoid == null || WordDocument.WordFIB.FIBTable2002.lcbPlcfBkfFactoid == null)
			{
				return true;
			}
			if (WordDocument.WordFIB.FIBTable2002.lcbPlcfBkfFactoid.Value < 4)
			{
				AddParsingNote(ParsingNoteType.PossiblyMalicious, "lcbPlcfBkfFactoid is smaller than it should be", "CVE-2006-2492", WordDocument.WordFIB.FIBTable2002.lcbPlcfBkfFactoid.Offset, WordDocument.WordFIB.FIBTable2002.lcbPlcfBkfFactoid.Length);
				return false;
			}
			if ((ulong)((long)WordDocument.WordFIB.FIBTable2002.fcPlcfBkfFactoid.Value.Value + (long)WordDocument.WordFIB.FIBTable2002.lcbPlcfBkfFactoid.Value.Value) > WordDocument.GetActiveTableStream().Data.Length)
			{
				AddParsingNote(ParsingNoteType.PossiblyMalicious, "PlcfBkfFactoid is out of range", "CVE-2006-2492", WordDocument.WordFIB.FIBTable2002.fcPlcfBkfFactoid.Offset, WordDocument.WordFIB.FIBTable2002.fcPlcfBkfFactoid.Length + WordDocument.WordFIB.FIBTable2002.lcbPlcfBkfFactoid.Length);
				return false;
			}
			ulong num = (ulong)((long)WordDocument.WordFIB.FIBTable2002.lcbPlcfBkfFactoid.Value.Value - 4L) / 10uL;
			if (num > 32752)
			{
				AddParsingNote(ParsingNoteType.PossiblyMalicious, "lcbPlcfBkfFactoid indicates there are more items than are reasonable (" + num + ")", "CVE-2006-2492", WordDocument.WordFIB.FIBTable2002.lcbPlcfBkfFactoid.Offset, WordDocument.WordFIB.FIBTable2002.lcbPlcfBkfFactoid.Length);
				return false;
			}
			return true;
		}

		[CanDetect("CVE-2008-4841")]
		public void DetectCVE_2008_4841()
		{
			if (WordBinaryDocuments == null)
			{
				return;
			}
			foreach (WordBinaryDocument wordBinaryDocument in WordBinaryDocuments)
			{
				if (wordBinaryDocument.PLFLSTInTableStream != null && wordBinaryDocument.PLFLSTInTableStream.TrailingLVLs != null)
				{
					foreach (LVL trailingLVL in wordBinaryDocument.PLFLSTInTableStream.TrailingLVLs)
					{
						if (trailingLVL.xst != null && trailingLVL.xst.cch != null && trailingLVL.xst.cch.Value > 256)
						{
							AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Found an xst.cch that claims a length larger than 256 (claims " + trailingLVL.xst.cch.Value + ")", "CVE-2008-4841", trailingLVL.xst.cch.Offset, 2uL);
						}
					}
				}
				if (wordBinaryDocument.PLFLFOInTableStream == null || wordBinaryDocument.PLFLFOInTableStream.rgLfoData == null)
				{
					continue;
				}
				foreach (LFODATA rgLfoDatum in wordBinaryDocument.PLFLFOInTableStream.rgLfoData)
				{
					if (rgLfoDatum.cp.Value == (uint?)uint.MaxValue || rgLfoDatum.rgLfoLvl == null)
					{
						continue;
					}
					foreach (LFOLVL item in rgLfoDatum.rgLfoLvl)
					{
						if (item.lvl != null && item.lvl.xst != null && item.lvl.xst.cch != null && item.lvl.xst.cch.Value > 256)
						{
							AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Found an xst.cch that claims a length larger than 256 (claims " + item.lvl.xst.cch.Value + ")", "CVE-2008-4841", item.lvl.xst.cch.Offset, 2uL);
						}
					}
				}
			}
		}
	}
}
namespace GUT.DataFormats.ExcelBIFF8BinaryFormat
{
	public class ExcelBIFF8BinaryFormatDetectionLogic : ExcelBIFF8BinaryFormat
	{
		[CanDetect("CVE-2007-0671")]
		public void DetectCVE_2007_0671()
		{
			if (ExcelBinaryDocuments == null)
			{
				return;
			}
			foreach (ExcelBinaryDocument excelBinaryDocument in ExcelBinaryDocuments)
			{
				if (excelBinaryDocument == null)
				{
					continue;
				}
				if (excelBinaryDocument.Globals != null)
				{
					CheckBIFFRecordsForCVE_2007_0671(excelBinaryDocument.Globals.BIFFRecords);
				}
				if (excelBinaryDocument.Worksheets == null)
				{
					continue;
				}
				foreach (SubStream worksheet in excelBinaryDocument.Worksheets)
				{
					CheckBIFFRecordsForCVE_2007_0671(worksheet.BIFFRecords);
				}
			}
		}

		private void CheckBIFFRecordsForCVE_2007_0671(List<BIFFRecord> BIFFRecords)
		{
			if (BIFFRecords == null)
			{
				return;
			}
			for (int i = 0; i < BIFFRecords.Count; i++)
			{
				if (BIFFRecords[i] != null && BIFFRecords[i] is MSODrawingGroup)
				{
					MSODrawingGroup mSODrawingGroup = (MSODrawingGroup)BIFFRecords[i];
					if (mSODrawingGroup.rgChildRec != null)
					{
						if (mSODrawingGroup.rgChildRec.drawingPrimaryOptions != null && mSODrawingGroup.rgChildRec.drawingPrimaryOptions.fopt != null)
						{
							DectectVulnCVE_2007_0671(mSODrawingGroup.rgChildRec.drawingPrimaryOptions.fopt, mSODrawingGroup);
						}
						if (mSODrawingGroup.rgChildRec.drawingTertiaryOptions != null && mSODrawingGroup.rgChildRec.drawingTertiaryOptions.fopt != null)
						{
							DectectVulnCVE_2007_0671(mSODrawingGroup.rgChildRec.drawingTertiaryOptions.fopt, mSODrawingGroup);
						}
					}
				}
				else
				{
					if (BIFFRecords[i] == null || !(BIFFRecords[i] is MSODrawing))
					{
						continue;
					}
					MSODrawing mSODrawing = (MSODrawing)BIFFRecords[i];
					if (mSODrawing.rgChildRec != null && mSODrawing.rgChildRec.groupShape != null)
					{
						List<OfficeArtSpgrContainerFileBlock> list = new List<OfficeArtSpgrContainerFileBlock>(mSODrawing.rgChildRec.groupShape.rgfb.Count);
						list.AddRange(mSODrawing.rgChildRec.groupShape.rgfb);
						int num = 0;
						do
						{
							OfficeArtSpgrContainerFileBlock officeArtSpgrContainerFileBlock = list[num];
							if (officeArtSpgrContainerFileBlock.spgrContainer != null)
							{
								list.AddRange(officeArtSpgrContainerFileBlock.spgrContainer.rgfb);
							}
							else if (officeArtSpgrContainerFileBlock.spContainer != null && officeArtSpgrContainerFileBlock.spContainer.shapePrimaryOptions != null && officeArtSpgrContainerFileBlock.spContainer.shapePrimaryOptions.fopt != null)
							{
								DectectVulnCVE_2007_0671(officeArtSpgrContainerFileBlock.spContainer.shapePrimaryOptions.fopt, mSODrawing);
							}
							num++;
						}
						while (list.Count > num);
					}
					if (mSODrawing.rgChildRec != null && mSODrawing.rgChildRec.shape != null && mSODrawing.rgChildRec.shape.shapePrimaryOptions != null && mSODrawing.rgChildRec.shape.shapePrimaryOptions.fopt != null)
					{
						DectectVulnCVE_2007_0671(mSODrawing.rgChildRec.shape.shapePrimaryOptions.fopt, mSODrawing);
					}
					if (mSODrawing.standAloneSpContainer != null && mSODrawing.standAloneSpContainer.shapePrimaryOptions != null && mSODrawing.standAloneSpContainer.shapePrimaryOptions.fopt != null)
					{
						DectectVulnCVE_2007_0671(mSODrawing.standAloneSpContainer.shapePrimaryOptions.fopt, mSODrawing);
					}
				}
			}
		}

		private void DectectVulnCVE_2007_0671(OfficeArtRGFOPTE rgfopte, BIFFRecord m)
		{
			int num = 0;
			for (int i = 0; i < rgfopte.complexDataSizes.Count; i++)
			{
				if (num + rgfopte.complexDataSizes[i] < num)
				{
					if (!rgfopte.whichRgfopteTooLong.HasValue || rgfopte.whichRgfopteTooLong > i)
					{
						AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Found a malicious record.  This file may attempt to exploit CVE_2007_0671!!", "CVE-2007-0671", m.DataStructureOffset, m.DataStructureLength);
					}
					else
					{
						AddParsingNote(ParsingNoteType.Warning, "Found a potentially malicious record, invalid property table beyond BIFF boundary.  This file could be attempting to exploit CVE_2007_0671!!", "CVE-2007-0671", m.DataStructureOffset, m.DataStructureLength);
					}
					break;
				}
				if ((rgfopte.complexData == null && num > 0) || (rgfopte.complexData != null && (ulong)num > rgfopte.complexData.Length))
				{
					AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Found a potentially malicious record, we did not have room to read all of the complex data from a property record.  This file may attempt to exploit CVE_2007_0671!!", "CVE-2007-0671", m.DataStructureOffset, m.DataStructureLength);
					break;
				}
				num += rgfopte.complexDataSizes[i];
			}
		}

		[CanDetect("CVE-2009-0238")]
		public void DetectCVE_2009_0238()
		{
			if (ExcelBinaryDocuments == null)
			{
				return;
			}
			foreach (ExcelBinaryDocument excelBinaryDocument in ExcelBinaryDocuments)
			{
				if (excelBinaryDocument == null)
				{
					continue;
				}
				if (excelBinaryDocument.Globals != null)
				{
					CheckBIFFRecordsForCVE_2009_0238(excelBinaryDocument.Globals.BIFFRecords);
				}
				if (excelBinaryDocument.Worksheets == null)
				{
					continue;
				}
				foreach (SubStream worksheet in excelBinaryDocument.Worksheets)
				{
					CheckBIFFRecordsForCVE_2009_0238(worksheet.BIFFRecords);
				}
			}
		}

		private void CheckBIFFRecordsForCVE_2009_0238(List<BIFFRecord> BIFFRecords)
		{
			if (BIFFRecords == null)
			{
				return;
			}
			foreach (BIFFRecord BIFFRecord in BIFFRecords)
			{
				if (BIFFRecord == null || !(BIFFRecord is SST))
				{
					continue;
				}
				SST sST = BIFFRecord as SST;
				if (sST.rgb == null)
				{
					continue;
				}
				foreach (XLUnicodeRichExtendedString item in sST.rgb)
				{
					if (item != null && item.ExtRst != null && item.ExtRst.cb != null && item.ExtRst.cb.Value % 2 == 1)
					{
						AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Found an SST record with an odd value for one of its ExtRst.cb members (it was " + item.ExtRst.cb.Value + ")", "CVE-2009-0238", item.ExtRst.cb.Offset, item.ExtRst.cb.Length);
					}
				}
			}
		}

		[CanDetect("CVE-2008-0081")]
		public void DetectCVE_2008_0081()
		{
			if (ExcelBinaryDocuments == null)
			{
				return;
			}
			foreach (ExcelBinaryDocument excelBinaryDocument in ExcelBinaryDocuments)
			{
				if (excelBinaryDocument == null)
				{
					continue;
				}
				if (excelBinaryDocument.Globals != null)
				{
					CheckBIFFRecordsForCVE_2008_0081(excelBinaryDocument.Globals.BIFFRecords);
				}
				if (excelBinaryDocument.Worksheets == null)
				{
					continue;
				}
				foreach (SubStream worksheet in excelBinaryDocument.Worksheets)
				{
					CheckBIFFRecordsForCVE_2008_0081(worksheet.BIFFRecords);
				}
			}
		}

		private void CheckBIFFRecordsForCVE_2008_0081(List<BIFFRecord> BIFFRecords)
		{
			if (BIFFRecords == null)
			{
				return;
			}
			bool flag = false;
			foreach (BIFFRecord BIFFRecord in BIFFRecords)
			{
				if (BIFFRecord != null && BIFFRecord is BoundSheet)
				{
					flag = true;
				}
				if (BIFFRecord != null)
				{
					ushort? value = BIFFRecord.Type.Value;
					if (value == 223 && value.HasValue && !flag)
					{
						AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Found a UDDESC record prior to a BoundSheet record", "CVE-2008-0081", BIFFRecord.DataStructureOffset, BIFFRecord.DataStructureLength);
					}
				}
			}
		}
	}
}
namespace GUT.DataFormats.PowerPoint97_2003BinaryFormat
{
	public class PowerPoint97_2003BinaryFormat : DataFormat
	{
		public OLESSDataFile OLESSRoot;

		public List<PowerPointBinaryDocument> PowerPointBinaryDocuments;

		public CurrentUserAtom TheCurrentUserAtom;

		public uint BytesToParse;

		public ulong CurrentUserStreamOffset;

		public ulong PowerPointDocumentStreamOffset;

		public ulong PowerPointDocumentStreamLength;

		protected override void ParseData(DataInByteArray Data)
		{
			OLESSRoot = new OLESSDataFile(Data);
			BytesToParse = 0u;
			CurrentUserStreamOffset = 0uL;
			PowerPointDocumentStreamOffset = 0uL;
			PowerPointBinaryDocuments = new List<PowerPointBinaryDocument>();
			if (OLESSRoot.DirectoryEntries != null)
			{
				foreach (OLESSDirectoryEntry directoryEntry in OLESSRoot.DirectoryEntries)
				{
					if (directoryEntry.EleName.Value != null)
					{
						if (string.Compare(directoryEntry.EleName.Value.Replace("\0", ""), 0, "CURRENT USER", 0, 12, ignoreCase: true) == 0)
						{
							CurrentUserStreamOffset = directoryEntry.Data.Offset;
						}
						if (string.Compare(directoryEntry.EleName.Value.Replace("\0", ""), 0, "ENCRYPTEDSUMMARY", 0, 16, ignoreCase: true) == 0)
						{
							AddParsingNote(ParsingNoteType.Warning, "An 'EncryptedSummary' stream was found - this file may be password protected / encrypted!", directoryEntry.Data.Offset + 2, 2uL);
						}
						if (string.Compare(directoryEntry.EleName.Value.Replace("\0", ""), 0, "POWERPOINT DOCUMENT", 0, 19, ignoreCase: true) == 0)
						{
							PowerPointDocumentStreamOffset = directoryEntry.Data.Offset;
							PowerPointDocumentStreamLength = directoryEntry.Data.Length;
						}
						if (CurrentUserStreamOffset != 0 && PowerPointDocumentStreamOffset != 0)
						{
							PowerPointBinaryDocuments.Add(new PowerPointBinaryDocument(Data, CurrentUserStreamOffset, PowerPointDocumentStreamOffset, PowerPointDocumentStreamLength));
							CurrentUserStreamOffset = 0uL;
							PowerPointDocumentStreamOffset = 0uL;
							PowerPointDocumentStreamLength = 0uL;
						}
					}
				}
			}
			if (PowerPointBinaryDocuments.Count == 0)
			{
				AddParsingNote(ParsingNoteType.ParserNotApplicable, "A valid 'PowerPoint Document' stream in the PowerPoint 97-2003 format was not found.");
			}
		}

		public static Record FigureOutWhatTypeOfRecordThisIs(DataInByteArray Data)
		{
			ushort? num = Data.PeekUInt16();
			ushort? num2 = Data.PeekUInt16(2uL);
			if (!((int?)num).HasValue || !((int?)num2).HasValue)
			{
				return null;
			}
			ushort value = num.Value;
			ushort value2 = num2.Value;
			Record record = null;
			switch (value2)
			{
			case 1000:
				return new Container(Data);
			case 1006:
				return new Container(Data);
			case 1016:
				return new Container(Data);
			case 1008:
				return new Container(Data);
			case 61443:
				return new Container(Data);
			case 1036:
				return new Container(Data);
			case 1035:
				return new Container(Data);
			case 1038:
			case 1039:
			case 1052:
			case 1053:
			case 1054:
			case 1055:
			case 1056:
			case 1058:
			case 1059:
			case 1060:
			case 1061:
			case 1062:
			case 1063:
				return new Atom(Data);
			case 1001:
				return new DocumentAtom(Data);
			case 1002:
				return new Atom(Data);
			case 4085:
				return new UserEditAtom(Data);
			case 6002:
				return new PersistDirectoryAtom(Data);
			case 4018:
				return new TextMasterStyle10Atom(Data);
			case 1064:
				return new RoundTripCustomTableStyles12(Data);
			case 1030:
				return new DocRoutingSlip(Data);
			case 3011:
				return new OEPlaceHolderAtom(Data);
			case 3999:
				return new TextHeaderAtom(Data);
			case 61451:
				return new MSOPropertyTable(Data);
			case 61729:
				return new MSOPropertyTable(Data);
			case 61730:
				return new MSOPropertyTable(Data);
			case 61450:
				return new MSOShapeAtom(Data);
			case 1040:
				return new NamedShows(Data);
			case 61453:
				return new ClientTextBox(Data);
			case 1011:
				return new SlidePersistAtom(Data);
			case 61447:
				return new MSOFBTBSE(Data);
			case 5003:
				return new Container(Data);
			case 4056:
			case 4087:
			case 4088:
			case 4089:
			case 4090:
			case 4117:
				return new MetaCharAtom(Data);
			case 61440:
			case 61442:
			case 61444:
				return new DrawingContainer(Data);
			default:
				if ((value & 0xF) == 15)
				{
					return new Container(Data);
				}
				return new Atom(Data);
			}
		}
	}
	public class PowerPoint97_2003BinaryFormatDetectionLogic : PowerPoint97_2003BinaryFormat
	{
		[CanDetect("CVE-2007-0671")]
		public void DetectCVE_2007_0671()
		{
			if (PowerPointBinaryDocuments == null)
			{
				return;
			}
			foreach (PowerPointBinaryDocument powerPointBinaryDocument in PowerPointBinaryDocuments)
			{
				foreach (DrawingContainer item in powerPointBinaryDocument.FindRecordsOfType<DrawingContainer>(Recursive: true))
				{
					if (item == null)
					{
						continue;
					}
					foreach (MSOPropertyTable item2 in item.FindRecordsOfType<MSOPropertyTable>(Recursive: true))
					{
						if (item2 != null && item2.Header != null && item2.Header.Length != null && item2.Header.Length.Value.HasValue && (ulong)((long)item2.DataStructureOffset + (long)(item2.Header.Length.Value ?? 0) + 8) > item.DataStructureOffset + item.DataStructureLength)
						{
							AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Potentially exploitable Property Table detected in a Drawing Group container, record length exceeds drawing group container length", "CVE-2007-0671", item2.DataStructureOffset, item2.DataStructureLength);
						}
					}
				}
			}
		}

		[CanDetect("CVE-2006-3434")]
		public void DetectCVE_2006_3434()
		{
			if (PowerPointBinaryDocuments == null)
			{
				return;
			}
			foreach (PowerPointBinaryDocument powerPointBinaryDocument in PowerPointBinaryDocuments)
			{
				foreach (DrawingContainer item in powerPointBinaryDocument.FindRecordsOfType<DrawingContainer>(Recursive: true))
				{
					foreach (MSOPropertyTable item2 in item.FindRecordsOfType<MSOPropertyTable>(Recursive: true))
					{
						if (item2 == null)
						{
							continue;
						}
						foreach (FOPTEComplex item3 in item2.FopteComplexArray)
						{
							if (item2.FopteArray != null && item3 != null && item3.Owner != null && item3.Owner.op != null && item3.Owner.op.Value.HasValue && (ulong?)((long)item3.DataStructureOffset - ((long)item2.DataStructureOffset - (long)item2.FopteArray.Count * 6L) + item3.Owner.op.Value) > (ulong?)4294967295uL)
							{
								AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Potentially exploitable Property Table detected in a Drawing Group container, complex data size causes integer overflow", "CVE-2006-3434", item2.DataStructureOffset, item2.DataStructureLength);
							}
							if (item2.FopteArray != null && item3 != null && item3.Owner != null && item3.Owner.op != null && item3.Owner.op.Value.HasValue && item2.Header != null && item2.Header.Length != null && item2.Header.Length.Value.HasValue && item3.DataStructureOffset + item3.Owner.op.Value > (ulong?)((long)item2.DataStructureOffset + (long)item2.Header.Length.Value.Value + 8))
							{
								AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Potentially exploitable Property Table detected in a Drawing Group container, complex data exceeds record length", "CVE-2006-3434", item2.DataStructureOffset, item2.DataStructureLength);
							}
						}
					}
				}
			}
		}

		[CanDetect("CVE-2009-0556")]
		public void DetectCVE_2009_0556()
		{
			bool flag = false;
			bool flag2 = false;
			if (PowerPointBinaryDocuments == null)
			{
				return;
			}
			List<Record> list = new List<Record>();
			foreach (PowerPointBinaryDocument powerPointBinaryDocument in PowerPointBinaryDocuments)
			{
				if (powerPointBinaryDocument != null && powerPointBinaryDocument.Children != null)
				{
					list.AddRange(powerPointBinaryDocument.Children);
				}
			}
			for (int i = 0; i < list.Count; i++)
			{
				if (!(list[i] is Container) || !(list[i] is Container { Children: not null } container))
				{
					continue;
				}
				list.AddRange(container.Children);
				if (list[i].Header.Type.Value != 61453)
				{
					continue;
				}
				foreach (Record child in container.Children)
				{
					if (child.Header.Type.Value == 3999)
					{
						flag = true;
						if (flag && flag2)
						{
							AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Found a malicious PST_OutlineTextRefAtom atom in a PST_ClientTextBox container at offset: " + child.Header.Type.Offset, "CVE-2009-0556", child.Header.Type.Offset, child.Header.Type.Length);
							flag = false;
							flag2 = false;
						}
					}
					if (child.Header.Type.Value == 3998)
					{
						flag2 = true;
						if (flag && flag2)
						{
							AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Found a malicious PST_OutlineTextRefAtom atom in a PST_ClientTextBox container at offset: " + child.Header.Type.Offset, "CVE-2009-0556", child.Header.Type.Offset, child.Header.Type.Length);
							flag = false;
							flag2 = false;
						}
					}
				}
				flag = false;
				flag2 = false;
			}
		}

		[CanDetect("CVE-2006-0022")]
		public void DetectCVE_2006_0022()
		{
			if (PowerPointBinaryDocuments == null)
			{
				return;
			}
			foreach (PowerPointBinaryDocument powerPointBinaryDocument in PowerPointBinaryDocuments)
			{
				foreach (TextHeaderAtom item in powerPointBinaryDocument.FindRecordsOfType<TextHeaderAtom>(Recursive: true))
				{
					if (item != null && item.textType != null && item.textType.Value.HasValue && item.textType.Value > 8)
					{
						AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Malformed TextHeader record found (textType > 8)", "CVE-2006-0022", item.DataStructureOffset, item.DataStructureLength);
					}
				}
			}
		}

		[CanDetect("CVE-2006-4694")]
		public void DetectCVE_2006_4694()
		{
			if (PowerPointBinaryDocuments == null)
			{
				return;
			}
			foreach (PowerPointBinaryDocument powerPointBinaryDocument in PowerPointBinaryDocuments)
			{
				foreach (NamedShows item in powerPointBinaryDocument.FindRecordsOfType<NamedShows>(Recursive: true))
				{
					if (item == null)
					{
						continue;
					}
					foreach (Record child in item.Children)
					{
						if (child != null && child.Header != null && child.Header.Type != null && ((int?)child.Header.Type.Value).HasValue && child.Header.Type.Value != 1041 && child.Header.Type.Value != 1042 && child.Header.Type.Value != 4026)
						{
							AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Malformed NamedShows container found (contains atoms other than 1041, 1042, or 4026)", "CVE-2006-4694", child.DataStructureOffset, child.DataStructureLength);
						}
					}
				}
			}
		}

		[CanDetect("CVE-2006-3590")]
		public void DetectCVE_2006_3590()
		{
			if (PowerPointBinaryDocuments == null)
			{
				return;
			}
			foreach (PowerPointBinaryDocument powerPointBinaryDocument in PowerPointBinaryDocuments)
			{
				foreach (DrawingContainer item in powerPointBinaryDocument.FindRecordsOfType<DrawingContainer>(Recursive: true))
				{
					if (item.FindRecordsOfType<ClientTextBox>(Recursive: true).Count != 0)
					{
						continue;
					}
					foreach (MSOPropertyTable item2 in item.FindRecordsOfType<MSOPropertyTable>(Recursive: true))
					{
						foreach (Fopte item3 in item2.FopteArray)
						{
							if (item3 != null && item3.pid != null && ((int?)item3.pid.Value).HasValue && item3.op != null && item3.op.Value.HasValue && item3.pid.Value == 128)
							{
								uint? value = item3.op.Value;
								if (value.GetValueOrDefault() != 0 || !value.HasValue)
								{
									AddParsingNote(ParsingNoteType.DefinitelyMalicious, "ShapeContainer contains a text property table but is missing a ClientTextBox container", "CVE-2006-3590", item.DataStructureOffset, item.DataStructureLength);
								}
							}
						}
					}
				}
			}
		}

		[CanDetect("CVE-2006-0009")]
		public void DetectCVE_2006_0009()
		{
			if (PowerPointBinaryDocuments == null)
			{
				return;
			}
			foreach (PowerPointBinaryDocument powerPointBinaryDocument in PowerPointBinaryDocuments)
			{
				foreach (DocRoutingSlip item in powerPointBinaryDocument.FindRecordsOfType<DocRoutingSlip>(Recursive: true))
				{
					if (item.originatorString != null && item.originatorString.stringType != null && item.originatorString.stringType.Value != 1)
					{
						AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Malformed DocRoutingSlip Record: originatorString.stringType != 1", "CVE-2006-0009", item.originatorString.DataStructureOffset, item.originatorString.DataStructureLength);
					}
					if (item.originatorString != null && item.originatorString.stringData != null && item.originatorString.stringData.Value != null)
					{
						int length = item.originatorString.stringData.Value.Length;
						int? num = item.originatorString.stringLength.Value - 1;
						if (length >= num.GetValueOrDefault() && num.HasValue && item.originatorString.stringData.Value[item.originatorString.stringLength.Value.Value - 1] != 0)
						{
							AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Malformed DocRoutingSlip Record: originatorString wasn't null terminated where it should have been", "CVE-2006-0009", item.originatorString.DataStructureOffset, item.originatorString.DataStructureLength);
						}
					}
					if (item.rgRecipientRoutingSlipStrings != null)
					{
						foreach (DocRoutingSlipString rgRecipientRoutingSlipString in item.rgRecipientRoutingSlipStrings)
						{
							if (rgRecipientRoutingSlipString == null)
							{
								continue;
							}
							if (rgRecipientRoutingSlipString.stringType != null && rgRecipientRoutingSlipString.stringType.Value != 2)
							{
								AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Malformed DocRoutingSlip Record: a rgRecipientRoutingSlipStrings member's stringType != 2", "CVE-2006-0009", rgRecipientRoutingSlipString.DataStructureOffset, rgRecipientRoutingSlipString.DataStructureLength);
							}
							if (rgRecipientRoutingSlipString.stringData != null && rgRecipientRoutingSlipString.stringData.Value != null)
							{
								int length2 = rgRecipientRoutingSlipString.stringData.Value.Length;
								int? num2 = rgRecipientRoutingSlipString.stringLength.Value - 1;
								if (length2 >= num2.GetValueOrDefault() && num2.HasValue && rgRecipientRoutingSlipString.stringData.Value[rgRecipientRoutingSlipString.stringLength.Value.Value - 1] != 0)
								{
									AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Malformed DocRoutingSlip Record: rgRecipientRoutingSlipStrings member wasn't null terminated where it should have been", "CVE-2006-0009", rgRecipientRoutingSlipString.DataStructureOffset, rgRecipientRoutingSlipString.DataStructureLength);
								}
							}
						}
					}
					if (item.subjectString != null && item.subjectString.stringType != null && item.subjectString.stringType.Value != 3)
					{
						AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Malformed DocRoutingSlip Record: subjectString.stringType != 3", "CVE-2006-0009", item.DataStructureOffset, item.DataStructureLength);
					}
					if (item.subjectString != null && item.subjectString.stringData != null && item.subjectString.stringData.Value != null)
					{
						int length3 = item.subjectString.stringData.Value.Length;
						ushort? value = item.subjectString.stringLength.Value;
						if (length3 >= value.GetValueOrDefault() && value.HasValue && item.subjectString.stringData.Value[item.subjectString.stringLength.Value.Value] != 0)
						{
							AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Malformed DocRoutingSlip Record: subjectString wasn't null terminated where it should have been", "CVE-2006-0009", item.subjectString.DataStructureOffset, item.subjectString.DataStructureLength);
						}
					}
					if (item.messageString != null && item.messageString.stringType != null && item.messageString.stringType.Value != 4)
					{
						AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Malformed DocRoutingSlip Record: messageString.stringType != 4", "CVE-2006-0009", item.DataStructureOffset, item.DataStructureLength);
					}
					if (item.messageString != null && item.messageString.stringData != null && item.messageString.stringData.Value != null)
					{
						int length4 = item.messageString.stringData.Value.Length;
						ushort? value2 = item.messageString.stringLength.Value;
						if (length4 >= value2.GetValueOrDefault() && value2.HasValue && item.messageString.stringData.Value[item.messageString.stringLength.Value.Value] != 0)
						{
							AddParsingNote(ParsingNoteType.DefinitelyMalicious, "Malformed DocRoutingSlip Record: messageString wasn't null terminated where it should have been", "CVE-2006-0009", item.messageString.DataStructureOffset, item.messageString.DataStructureLength);
						}
					}
				}
			}
		}
	}
}
namespace GUT.DataFormats.OLESS
{
	public class CLSID : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt32 dw1;

		[Order(1uL)]
		public DataItem_UInt16 w1;

		[Order(2uL)]
		public DataItem_UInt16 w2;

		[ConstantLength(8uL)]
		[Order(3uL)]
		public DataItem_ByteArray aby;

		public CLSID(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class SECTOR : DataStructure
	{
		private enum FAT_Constants : ulong
		{
			FAT_FREESECT = 4294967295uL,
			FAT_ENDOFCHAIN = 4294967294uL,
			FAT_FATSECT = 4294967293uL,
			FAT_DIFSECT = 4294967292uL,
			FAT_TERMINAL = 4294967292uL,
			FAT_NOSTREAM = 4294967295uL
		}

		[Order(0uL)]
		public DataItem_UInt32 SectorNumber;

		public SECTOR(DataInByteArray Data)
			: base(Data)
		{
		}

		public override string ToString()
		{
			if (SectorNumber == null || !SectorNumber.Value.HasValue)
			{
				return base.ToString();
			}
			string text = ((FAT_Constants)SectorNumber.Value.Value).ToString();
			if (text == null || text == "")
			{
				return base.ToString();
			}
			return text;
		}
	}
	public class OLESSHEADER : DataStructure
	{
		[Order(0uL)]
		[ConstantLength(8uL)]
		public DataItem_UByteArray Sig;

		[Order(1uL)]
		public CLSID clsidNull;

		[Order(2uL)]
		public DataItem_UInt16 VerMinor;

		[Order(3uL)]
		public DataItem_UInt16 VerDll;

		[Order(4uL)]
		public DataItem_UInt16 ByteOrder;

		[Order(5uL)]
		public DataItem_UInt16 SectorShift;

		[Order(6uL)]
		public DataItem_UInt16 MiniSecShift;

		[Order(7uL)]
		public DataItem_UInt16 Reserved;

		[Order(8uL)]
		public DataItem_UInt32 Reserved2;

		[Order(9uL)]
		public DataItem_UInt32 NumDirSects;

		[Order(10uL)]
		public DataItem_UInt32 NumFatSects;

		[Order(11uL)]
		public SECTOR DirSect1;

		[Order(12uL)]
		public DataItem_UInt32 TransactSig;

		[Order(13uL)]
		public DataItem_UInt32 MiniStrMax;

		[Order(14uL)]
		public SECTOR MiniFatSect1;

		[Order(15uL)]
		public DataItem_UInt32 NumMiniFatSects;

		[Order(16uL)]
		public SECTOR DifatSect1;

		[Order(17uL)]
		public DataItem_UInt32 NumDifatSects;

		[ConstantLength(109uL)]
		[Order(18uL)]
		public List<SECTOR> DiFat;

		public OLESSHEADER(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class OLESSDirectoryEntry : DataStructure
	{
		[ConstantLength(32uL)]
		[Order(0uL)]
		public DataItem_UnicodeString EleName;

		[Order(1uL)]
		public DataItem_UInt16 CbEleName;

		[Order(2uL)]
		public DataItem_UInt8 Type;

		[Order(3uL)]
		public DataItem_UInt8 TbyFlags;

		[Order(4uL)]
		public DataItem_UInt32 sidLeft;

		[Order(5uL)]
		public DataItem_UInt32 sidRight;

		[Order(6uL)]
		public DataItem_UInt32 sidChild;

		[Order(7uL)]
		public CLSID clsidThis;

		[Order(8uL)]
		public DataItem_UInt32 UserFlags;

		[Order(9uL)]
		public DataItem_UInt64 CreateTime;

		[Order(10uL)]
		public DataItem_UInt64 ModifyTime;

		[Order(11uL)]
		public DataItem_UInt32 StartSect;

		[Order(12uL)]
		public DataItem_UInt32 SizeLow;

		[Order(13uL)]
		public DataItem_UInt32 SizeHigh;

		[DoNotAutoProcess]
		public DataItem_UByteArray Data;

		public string FullPath;

		public ulong DirectoryIndex;

		public OLESSDirectoryEntry(DataInByteArray Data)
			: base(Data)
		{
		}

		public override string ToString()
		{
			if (EleName != null && EleName.Value != null && FullPath != null)
			{
				return FullPath.Replace("\0", "") + "\\" + EleName.Value.Replace("\0", "");
			}
			if (EleName != null && EleName.Value != null)
			{
				return EleName.Value.Replace("\0", "");
			}
			return "";
		}
	}
	public class ChildLink
	{
		public string Path;

		public int SIDOfChild;

		public ChildLink(string path, int sidOfChild)
		{
			Path = path;
			SIDOfChild = sidOfChild;
		}
	}
	public class OLESSDataFile : DataStructure
	{
		private const ulong FAT_FREESECT = 4294967295uL;

		private const ulong FAT_ENDOFCHAIN = 4294967294uL;

		private const ulong FAT_FATSECT = 4294967293uL;

		private const ulong FAT_DIFSECT = 4294967292uL;

		private const ulong FAT_TERMINAL = 4294967292uL;

		private const ulong FAT_NOSTREAM = 4294967295uL;

		public OLESSHEADER OLESSHeader;

		public List<SECTOR> DiFATSectors;

		public List<SECTOR> DiFAT;

		public List<SECTOR> FAT;

		public List<SECTOR> MiniFAT;

		public List<OLESSDirectoryEntry> DirectoryEntries;

		public OLESSDataFile(DataInByteArray Data)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			if (Data.Length > 8)
			{
				OLESSHeader = new OLESSHEADER(Data);
			}
			if (OLESSHeader == null || OLESSHeader.Sig == null || OLESSHeader.Sig.Value[0] != 208 || OLESSHeader.Sig.Value[1] != 207 || OLESSHeader.Sig.Value[2] != 17 || OLESSHeader.Sig.Value[3] != 224 || OLESSHeader.Sig.Value[4] != 161 || OLESSHeader.Sig.Value[5] != 177 || OLESSHeader.Sig.Value[6] != 26 || OLESSHeader.Sig.Value[7] != 225)
			{
				throw new ParserNotApplicable("The expected OLESS signature 0xD0CF11E (DOCFILE) was not found.");
			}
			ulong num = (ulong)(1 << (int?)OLESSHeader.SectorShift.Value).Value;
			ulong num2 = num / 4;
			if (num < 128)
			{
				AddParsingNote(ParsingNoteType.Error, "The sector size is too small - this OLESS file must be corrupt", OLESSHeader.SectorShift.Offset, OLESSHeader.SectorShift.Length);
			}
			ulong num3 = (ulong)(1 << (int?)OLESSHeader.MiniSecShift.Value).Value;
			if (num3 < 16 || num3 > OLESSHeader.MiniStrMax.Value)
			{
				AddParsingNote(ParsingNoteType.Error, "The mini fat sector size is out of bounds - this OLESS file must be corrupt", OLESSHeader.MiniSecShift.Offset, OLESSHeader.MiniSecShift.Length);
			}
			if ((ulong?)OLESSHeader.DifatSect1.SectorNumber.Value < (ulong?)4294967292uL)
			{
				Data.Seek(GetSectorOffset(OLESSHeader.DifatSect1.SectorNumber.Value.Value));
				DiFAT = new List<SECTOR>();
				DiFATSectors = new List<SECTOR>();
				uint num4 = OLESSHeader.DiFat[108].SectorNumber.Value ?? 0;
				for (uint num5 = 0u; num5 < (OLESSHeader.NumDifatSects.Value ?? 0); num5++)
				{
					for (ulong num6 = 0uL; num6 < num2 - 1; num6++)
					{
						SECTOR sECTOR = new SECTOR(Data);
						DiFAT.Add(sECTOR);
						num4++;
						if (sECTOR.SectorNumber.Value.HasValue)
						{
							uint? value = sECTOR.SectorNumber.Value;
							if (value.GetValueOrDefault() <= 4294967290u && value.HasValue && sECTOR.SectorNumber.Value != num4)
							{
								AddParsingNote(ParsingNoteType.Error, "This OLESS file has not been de-fragmented - this file may fail to parse as expected.");
							}
						}
					}
					SECTOR sECTOR2 = new SECTOR(Data);
					DiFATSectors.Add(sECTOR2);
					if (sECTOR2.SectorNumber.Value.HasValue)
					{
						uint? value2 = sECTOR2.SectorNumber.Value;
						if (value2.GetValueOrDefault() <= 4294967290u && value2.HasValue && sECTOR2.SectorNumber.Value != (OLESSHeader.DifatSect1.SectorNumber.Value ?? 0) + num5 + 1)
						{
							AddParsingNote(ParsingNoteType.Error, "This OLESS file has not been de-fragmented - this file may fail to parse as expected.");
						}
					}
				}
				_ = OLESSHeader.NumDifatSects.Value.Value;
			}
			if (!Data.Seek(GetSectorOffset(OLESSHeader.DiFat[0].SectorNumber.Value.Value)))
			{
				AddParsingNote(ParsingNoteType.Error, "Failed to seek to the first FAT sector in the file!  This file may need to be defragmented!");
				return;
			}
			FAT = new List<SECTOR>();
			ulong num7 = 0uL;
			num7 = ((num2 * OLESSHeader.NumFatSects.Value.Value <= (Data.Length - Data.CurrentPosition) / num * num2) ? (num2 * OLESSHeader.NumFatSects.Value.Value) : ((Data.Length - Data.CurrentPosition) / num * num2));
			for (ulong num8 = 0uL; num8 < num7; num8++)
			{
				if (!Data.HasDataLeftToRead)
				{
					break;
				}
				SECTOR sECTOR3 = new SECTOR(Data);
				FAT.Add(sECTOR3);
				if (sECTOR3.SectorNumber.Value.HasValue)
				{
					uint? value3 = sECTOR3.SectorNumber.Value;
					if (value3.GetValueOrDefault() <= 4294967290u && value3.HasValue && sECTOR3.SectorNumber.Value != FAT.Count)
					{
						AddParsingNote(ParsingNoteType.Error, "This OLESS file has not been de-fragmented - this file may fail to parse as expected.");
					}
				}
			}
			if ((ulong?)OLESSHeader.MiniFatSect1.SectorNumber.Value < (ulong?)4294967292uL)
			{
				Data.Seek(GetSectorOffset(OLESSHeader.MiniFatSect1.SectorNumber.Value.Value));
				ulong num9 = 0uL;
				num9 = ((num2 * OLESSHeader.NumMiniFatSects.Value.Value <= (Data.Length - Data.CurrentPosition) / num * num2) ? (num2 * OLESSHeader.NumMiniFatSects.Value.Value) : ((Data.Length - Data.CurrentPosition) / num * num2));
				MiniFAT = new List<SECTOR>();
				for (ulong num10 = 0uL; num10 < num9; num10++)
				{
					if (!Data.HasDataLeftToRead)
					{
						break;
					}
					MiniFAT.Add(new SECTOR(Data));
				}
			}
			ulong num11 = OLESSHeader.DirSect1.SectorNumber.Value.Value;
			ulong num12 = num / 128;
			uint num13 = 0u;
			ulong num14 = 0uL;
			Data.Seek(GetSectorOffset((uint)num11));
			DirectoryEntries = new List<OLESSDirectoryEntry>();
			while (true)
			{
				for (ulong num15 = 0uL; num15 < num12; num15++)
				{
					bool flag = false;
					OLESSDirectoryEntry oLESSDirectoryEntry = new OLESSDirectoryEntry(Data);
					if (!Data.HasDataLeftToRead)
					{
						AddParsingNote(ParsingNoteType.Error, "Hit EOF while parsing OLESS directory entries!  This file probably needs to be defragmented.", oLESSDirectoryEntry.DataStructureOffset, oLESSDirectoryEntry.DataStructureLength);
					}
					oLESSDirectoryEntry.DirectoryIndex = num14;
					oLESSDirectoryEntry.FullPath = "\\Orphaned";
					int num16;
					if (oLESSDirectoryEntry.CbEleName.Value.Value == 0)
					{
						uint? value4 = oLESSDirectoryEntry.sidLeft.Value;
						if (value4 == (uint?)uint.MaxValue && value4.HasValue && oLESSDirectoryEntry.sidRight.Value == (uint?)uint.MaxValue)
						{
							num16 = ((oLESSDirectoryEntry.sidChild.Value == (uint?)uint.MaxValue) ? 1 : 0);
							goto IL_07ea;
						}
					}
					num16 = 0;
					goto IL_07ea;
					IL_07ea:
					flag = (byte)num16 != 0;
					if (num14 == 0 || flag)
					{
						oLESSDirectoryEntry.FullPath = "";
					}
					DirectoryEntries.Add(oLESSDirectoryEntry);
					num14++;
					uint? value5 = oLESSDirectoryEntry.SizeLow.Value;
					if (value5.GetValueOrDefault() != 0 || !value5.HasValue)
					{
						if (oLESSDirectoryEntry.Type.Value == 5)
						{
							if (num13 != 0)
							{
								AddParsingNote(ParsingNoteType.Error, "An OLESS directory entry appears to be corrupt", oLESSDirectoryEntry.Type.Offset, oLESSDirectoryEntry.Type.Length);
							}
							num13 = oLESSDirectoryEntry.StartSect.Value.Value;
							_ = oLESSDirectoryEntry.SizeLow.Value.Value;
						}
						else if (oLESSDirectoryEntry.Type.Value == 2)
						{
							ulong currentPosition = Data.CurrentPosition;
							if (oLESSDirectoryEntry.SizeLow.Value < OLESSHeader.MiniStrMax.Value)
							{
								Data.Seek(GetMiniSectorOffset(oLESSDirectoryEntry.StartSect.Value.Value, num13));
							}
							else
							{
								Data.Seek(GetSectorOffset(oLESSDirectoryEntry.StartSect.Value.Value));
							}
							oLESSDirectoryEntry.Data = new DataItem_UByteArray(Data, oLESSDirectoryEntry.SizeLow.Value.Value);
							Data.Seek(currentPosition);
						}
					}
				}
				if (num11 >= (ulong)FAT.Count)
				{
					AddParsingNote(ParsingNoteType.Error, "This OLESS file has not been de-fragmented - parsing will fail");
					return;
				}
				if ((ulong?)FAT[(int)num11].SectorNumber.Value >= (ulong?)4294967292uL)
				{
					break;
				}
				num11 = FAT[(int)num11].SectorNumber.Value.Value;
			}
			if (DirectoryEntries.Count > 0 && DirectoryEntries[0].sidChild.Value != (uint?)uint.MaxValue)
			{
				Queue<ChildLink> queue = new Queue<ChildLink>();
				queue.Enqueue(new ChildLink("\\Root Entry", (int)DirectoryEntries[0].sidChild.Value.Value));
				while (queue.Count > 0)
				{
					ChildLink childLink = queue.Dequeue();
					if (childLink.SIDOfChild < DirectoryEntries.Count)
					{
						Queue<int> queue2 = new Queue<int>();
						queue2.Enqueue(childLink.SIDOfChild);
						while (queue2.Count > 0)
						{
							int num17 = queue2.Dequeue();
							if (num17 > 0 && num17 < DirectoryEntries.Count)
							{
								if (!(DirectoryEntries[num17].FullPath != "\\Orphaned"))
								{
									DirectoryEntries[num17].FullPath = childLink.Path;
									if (DirectoryEntries[num17].sidLeft.Value != (uint?)uint.MaxValue)
									{
										queue2.Enqueue((int)DirectoryEntries[num17].sidLeft.Value.Value);
									}
									if (DirectoryEntries[num17].sidRight.Value != (uint?)uint.MaxValue)
									{
										queue2.Enqueue((int)DirectoryEntries[num17].sidRight.Value.Value);
									}
									if (DirectoryEntries[num17].sidChild.Value != (uint?)uint.MaxValue)
									{
										queue.Enqueue(new ChildLink(childLink.Path + "\\" + DirectoryEntries[num17].EleName.Value, (int)DirectoryEntries[num17].sidChild.Value.Value));
									}
								}
							}
							else
							{
								AddParsingNote(ParsingNoteType.Error, "A directory entry claimed a child SID which didn't exist");
							}
						}
					}
					else
					{
						AddParsingNote(ParsingNoteType.Error, "A directory entry claimed a child SID which didn't exist");
					}
				}
			}
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}

		private ulong GetSectorOffset(uint Sector)
		{
			if (OLESSHeader == null)
			{
				throw new ParsingFailed("The OLESSHeader was referenced before being parsed.");
			}
			return (Sector + 1 << (int?)OLESSHeader.SectorShift.Value).Value;
		}

		private ulong GetMiniSectorOffset(uint Sector, uint MiniSect)
		{
			if (OLESSHeader == null)
			{
				throw new ParsingFailed("The OLESSHeader was referenced before being parsed.");
			}
			return (GetSectorOffset(MiniSect) + (Sector << (int?)OLESSHeader.MiniSecShift.Value)).Value;
		}
	}
	public class OLESSFormat : DataFormat
	{
		[Order(0uL)]
		public OLESSDataFile OLESSRoot;

		protected override void ParseData(DataInByteArray Data)
		{
			OLESSRoot = new OLESSDataFile(Data);
		}
	}
}
namespace GUT.DataFormats.MSODrawing
{
	public class OfficeArtRecordHeader : DataStructure
	{
		[BeginBitField(2u)]
		[Order(0uL)]
		[BitFieldSize(4u)]
		public DataItem_UInt8 recVer;

		[BitFieldSize(12u)]
		[Order(1uL)]
		public DataItem_UInt16 recInstance;

		[Order(2uL)]
		public DataItem_UInt16 recType;

		[Order(3uL)]
		public DataItem_UInt32 recLen;

		public OfficeArtRecordHeader(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class OfficeArtFdgg : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt32 spidMax;

		[Order(1uL)]
		public DataItem_UInt32 cidcl;

		[Order(2uL)]
		public DataItem_UInt32 cspSaved;

		[Order(3uL)]
		public DataItem_UInt32 cdgSaved;

		public OfficeArtFdgg(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class OfficeArtIdcl : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt32 dgid;

		[Order(1uL)]
		public DataItem_UInt32 cspidCur;

		public OfficeArtIdcl(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class OfficeArtFdg : DataStructure
	{
		[Order(0uL)]
		public OfficeArtRecordHeader rh;

		[Order(1uL)]
		public DataItem_UInt32 csp;

		[Order(2uL)]
		public DataItem_UInt32 spidCur;

		public OfficeArtFdg(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class OfficeArtFdggBlock : DataStructure
	{
		[Order(0uL)]
		public OfficeArtRecordHeader rh;

		[Order(1uL)]
		public OfficeArtFdgg head;

		[Order(2uL)]
		public List<OfficeArtIdcl> rgidcl;

		public OfficeArtFdggBlock(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			rh = new OfficeArtRecordHeader(DataToRead);
			head = new OfficeArtFdgg(DataToRead);
			if (head != null && head.cidcl != null && head.cidcl.Value.HasValue)
			{
				uint? value = head.cidcl.Value;
				if (value.GetValueOrDefault() != 0 && value.HasValue)
				{
					rgidcl = new List<OfficeArtIdcl>((int)head.cidcl.Value.Value);
					for (uint num = 1u; num < head.cidcl.Value; num++)
					{
						rgidcl.Add(new OfficeArtIdcl(DataToRead));
					}
				}
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class OfficeArtGlom : DataStructure
	{
		[Order(0uL)]
		public OfficeArtRecordHeader rh;

		[Order(1uL)]
		public DataItem_UByteArray glom;

		public OfficeArtGlom(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			rh = new OfficeArtRecordHeader(DataToRead);
			if (rh != null && rh.recLen != null && rh.recLen.Value.HasValue)
			{
				uint? value = rh.recLen.Value;
				if (value.GetValueOrDefault() != 0 && value.HasValue)
				{
					glom = new DataItem_UByteArray(DataToRead, rh.recLen.Value.Value);
				}
			}
			else
			{
				AddParsingNote(ParsingNoteType.Error, "Unable to read OfficeArtGlom!", DataToRead.CurrentPosition);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}

		public OfficeArtGlom(DataInByteArray DataToRead, ulong numBytes)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			if (numBytes != 0)
			{
				glom = new DataItem_UByteArray(DataToRead, numBytes);
			}
			else
			{
				AddParsingNote(ParsingNoteType.Error, "Unable to read OfficeArtGlom!", DataToRead.CurrentPosition);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class OfficeArtFOPTEOPID : DataStructure
	{
		[BeginBitField(2u)]
		[Order(0uL)]
		[BitFieldSize(14u)]
		public DataItem_UInt16 opid;

		[Order(1uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fBid;

		[Order(2uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fComplex;

		public OfficeArtFOPTEOPID(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class OfficeArtFOPTE : DataStructure
	{
		[Order(0uL)]
		public OfficeArtFOPTEOPID opid;

		[Order(1uL)]
		public DataItem_Int32 op;

		public OfficeArtFOPTE(DataInByteArray DataToRead, ref List<int> complexDataSizes)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			opid = new OfficeArtFOPTEOPID(DataToRead);
			op = new DataItem_Int32(DataToRead);
			if (op != null && op.Value.HasValue && opid != null && opid.fComplex != null && (opid.fComplex.Value == 1 || opid.opid.Value == 1026 || opid.opid.Value == 1027 || opid.opid.Value == 1028 || opid.opid.Value == 1029 || opid.opid.Value == 1030 || opid.opid.Value == 1031 || opid.opid.Value == 1032 || opid.opid.Value == 1033 || opid.opid.Value == 1034 || opid.opid.Value == 1035 || opid.opid.Value == 1036 || opid.opid.Value == 1037 || opid.opid.Value == 1038 || opid.opid.Value == 1284 || opid.opid.Value == 1288 || opid.opid.Value == 1349 || opid.opid.Value == 1350 || opid.opid.Value == 1359 || opid.opid.Value == 1372 || opid.opid.Value == 1376 || opid.opid.Value == 1413 || opid.opid.Value == 1414 || opid.opid.Value == 1423 || opid.opid.Value == 1436 || opid.opid.Value == 1440 || opid.opid.Value == 1477 || opid.opid.Value == 1478 || opid.opid.Value == 1487 || opid.opid.Value == 1500 || opid.opid.Value == 1504 || opid.opid.Value == 1541 || opid.opid.Value == 1542 || opid.opid.Value == 1551 || opid.opid.Value == 1564 || opid.opid.Value == 1568 || opid.opid.Value == 1605 || opid.opid.Value == 1606 || opid.opid.Value == 1615 || opid.opid.Value == 1628 || opid.opid.Value == 1632 || opid.opid.Value == 1664 || opid.opid.Value == 1666 || opid.opid.Value == 1728 || opid.opid.Value == 1792 || opid.opid.Value == 192 || opid.opid.Value == 193 || opid.opid.Value == 197 || opid.opid.Value == 198 || opid.opid.Value == 261 || opid.opid.Value == 271 || opid.opid.Value == 272 || opid.opid.Value == 274 || opid.opid.Value == 280 || opid.opid.Value == 286 || opid.opid.Value == 325 || opid.opid.Value == 326 || opid.opid.Value == 337 || opid.opid.Value == 338 || opid.opid.Value == 341 || opid.opid.Value == 342 || opid.opid.Value == 343 || opid.opid.Value == 345 || opid.opid.Value == 390 || opid.opid.Value == 391 || opid.opid.Value == 407 || opid.opid.Value == 417 || opid.opid.Value == 421 || opid.opid.Value == 453 || opid.opid.Value == 454 || opid.opid.Value == 463 || opid.opid.Value == 476 || opid.opid.Value == 480 || opid.opid.Value == 533 || opid.opid.Value == 537 || opid.opid.Value == 652 || opid.opid.Value == 896 || opid.opid.Value == 897 || opid.opid.Value == 898 || opid.opid.Value == 899 || opid.opid.Value == 909 || opid.opid.Value == 910 || opid.opid.Value == 919 || opid.opid.Value == 921 || opid.opid.Value == 922 || opid.opid.Value == 928 || opid.opid.Value == 930 || opid.opid.Value == 932 || opid.opid.Value == 933 || opid.opid.Value == 934 || opid.opid.Value == 936))
			{
				if (opid.opid.Value == 341)
				{
					int? value = op.Value;
					if (value.GetValueOrDefault() == 0 && value.HasValue)
					{
						complexDataSizes.Add(6);
						goto IL_1161;
					}
				}
				complexDataSizes.Add(op.Value.Value);
				goto IL_1161;
			}
			complexDataSizes.Add(0);
			goto IL_1181;
			IL_1161:
			opid.fComplex.Value = 1;
			goto IL_1181;
			IL_1181:
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class OfficeArtRGFOPTE : DataStructure
	{
		[Order(0uL)]
		public List<OfficeArtFOPTE> rgfopte;

		[Order(1uL)]
		public DataItem_UByteArray complexData;

		[DoNotAutoProcess]
		public List<int> complexDataSizes;

		[DoNotAutoProcess]
		public int? whichRgfopteTooLong = null;

		public OfficeArtRGFOPTE(DataInByteArray DataToRead, ushort numRecords, uint tableLen, ulong endOfMSO)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			rgfopte = new List<OfficeArtFOPTE>();
			complexDataSizes = new List<int>();
			if (rgfopte != null && complexDataSizes != null)
			{
				for (int i = 0; i < numRecords; i++)
				{
					OfficeArtFOPTE officeArtFOPTE = new OfficeArtFOPTE(DataToRead, ref complexDataSizes);
					if (!whichRgfopteTooLong.HasValue && DataToRead.CurrentPosition > endOfMSO)
					{
						whichRgfopteTooLong = i;
					}
					if (officeArtFOPTE == null)
					{
						break;
					}
					rgfopte.Add(officeArtFOPTE);
				}
				if (tableLen - (DataToRead.CurrentPosition - base.DataStructureOffset) != 0)
				{
					complexData = new DataItem_UByteArray(DataToRead, tableLen - (DataToRead.CurrentPosition - base.DataStructureOffset));
				}
			}
			else
			{
				AddParsingNote(ParsingNoteType.Error, "Unable to read OfficeArtRGFOPTE!", DataToRead.CurrentPosition);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class OfficeArtFOPT : DataStructure
	{
		[Order(0uL)]
		public OfficeArtRecordHeader rh;

		[Order(1uL)]
		public OfficeArtRGFOPTE fopt;

		public OfficeArtFOPT(DataInByteArray DataToRead, ulong endOfMSO)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			rh = new OfficeArtRecordHeader(DataToRead);
			if (rh != null && rh.recInstance != null && ((int?)rh.recInstance.Value).HasValue && rh.recLen != null && rh.recLen.Value.HasValue)
			{
				fopt = new OfficeArtRGFOPTE(DataToRead, rh.recInstance.Value.Value, rh.recLen.Value.Value, endOfMSO);
			}
			else
			{
				AddParsingNote(ParsingNoteType.Error, "Unable to read OfficeArtFOPT!", DataToRead.CurrentPosition);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class OfficeArtFSP : DataStructure
	{
		[Order(0uL)]
		public OfficeArtRecordHeader rh;

		[Order(1uL)]
		public DataItem_Int32 spid;

		[BeginBitField(2u)]
		[BitFieldSize(1u)]
		[Order(2uL)]
		public DataItem_UInt8 fGroup;

		[Order(3uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fChlid;

		[BitFieldSize(1u)]
		[Order(4uL)]
		public DataItem_UInt8 fPatriarch;

		[Order(5uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fDeleted;

		[Order(6uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fOleShape;

		[Order(7uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fHaveMaster;

		[BitFieldSize(1u)]
		[Order(8uL)]
		public DataItem_UInt8 fFlipH;

		[BitFieldSize(1u)]
		[Order(9uL)]
		public DataItem_UInt8 fFlipV;

		[Order(10uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fConnector;

		[BitFieldSize(1u)]
		[Order(11uL)]
		public DataItem_UInt8 fHaveAnchor;

		[Order(12uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fBackground;

		[Order(13uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fHaveSpt;

		[Order(14uL)]
		[BitFieldSize(4u)]
		public DataItem_UInt8 unusedBits;

		[Order(15uL)]
		public DataItem_UInt16 unused1;

		public OfficeArtFSP(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class OfficeArtSpContainer : DataStructure
	{
		[Order(0uL)]
		public OfficeArtRecordHeader rh;

		[Order(1uL)]
		public DataItem_UByteArray shapeGroupGlom;

		[Order(2uL)]
		public OfficeArtFSP shapeProp;

		[Order(3uL)]
		public DataItem_UByteArray deletedShapeGlom;

		[Order(4uL)]
		public OfficeArtFOPT shapePrimaryOptions;

		[Order(5uL)]
		public OfficeArtFOPT shapeSecondaryOptions;

		[Order(6uL)]
		public OfficeArtFOPT shapeTertiaryOptions;

		[Order(7uL)]
		public OfficeArtGlom childAnchor;

		[Order(8uL)]
		public OfficeArtGlom clientAnchor;

		[Order(9uL)]
		public OfficeArtGlom clientData;

		[Order(10uL)]
		public OfficeArtGlom clientTextbox;

		public OfficeArtSpContainer(DataInByteArray DataToRead, ulong endOfMSO)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			rh = new OfficeArtRecordHeader(DataToRead);
			_ = DataToRead.CurrentPosition;
			if (rh != null && rh.recLen != null && rh.recLen.Value.HasValue)
			{
				uint? value = rh.recLen.Value;
				if (value.GetValueOrDefault() != 0 && value.HasValue)
				{
					bool flag = false;
					do
					{
						switch (BitConverter.ToUInt16(DataToRead.PeekUByteArray(4uL), 2))
						{
						case 61449:
							shapeGroupGlom = new DataItem_UByteArray(DataToRead, 24uL);
							break;
						case 61450:
							shapeProp = new OfficeArtFSP(DataToRead);
							break;
						case 61725:
							deletedShapeGlom = new DataItem_UByteArray(DataToRead, 12uL);
							break;
						case 61451:
							shapePrimaryOptions = new OfficeArtFOPT(DataToRead, endOfMSO);
							break;
						case 61729:
							shapeSecondaryOptions = new OfficeArtFOPT(DataToRead, endOfMSO);
							break;
						case 61730:
							shapeTertiaryOptions = new OfficeArtFOPT(DataToRead, endOfMSO);
							break;
						case 61455:
							childAnchor = new OfficeArtGlom(DataToRead);
							break;
						case 61456:
							clientAnchor = new OfficeArtGlom(DataToRead);
							break;
						case 61457:
							clientData = new OfficeArtGlom(DataToRead);
							break;
						case 61453:
							clientTextbox = new OfficeArtGlom(DataToRead);
							break;
						default:
							flag = true;
							break;
						}
					}
					while (DataToRead.HasDataLeftToRead && !flag);
					goto IL_01db;
				}
			}
			if (rh == null || (rh.recLen == null && !rh.recLen.Value.HasValue))
			{
				AddParsingNote(ParsingNoteType.Error, "Unable to read OfficeArtSpContainer!", DataToRead.CurrentPosition);
			}
			goto IL_01db;
			IL_01db:
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class OfficeArtSpgrContainerFileBlock : DataStructure
	{
		[Order(0uL)]
		public OfficeArtSpContainer spContainer;

		[Order(1uL)]
		public OfficeArtSpgrContainer spgrContainer;

		public OfficeArtSpgrContainerFileBlock(DataInByteArray DataToRead, int containerLength, ulong endOfMSO)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			ushort num = BitConverter.ToUInt16(DataToRead.PeekUByteArray(4uL), 2);
			ushort num2 = num;
			if (num2 == 61443)
			{
				spgrContainer = new OfficeArtSpgrContainer(DataToRead, containerLength, endOfMSO);
			}
			else
			{
				spContainer = new OfficeArtSpContainer(DataToRead, endOfMSO);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class OfficeArtSpgrContainer : DataStructure
	{
		[Order(0uL)]
		public OfficeArtRecordHeader rh;

		[Order(1uL)]
		public List<OfficeArtSpgrContainerFileBlock> rgfb;

		public OfficeArtSpgrContainer(DataInByteArray DataToRead, int containerLength, ulong endOfMSO)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			rh = new OfficeArtRecordHeader(DataToRead);
			if (rh != null && rh.recLen != null && rh.recLen.Value.HasValue)
			{
				uint? value = rh.recLen.Value;
				if (value.GetValueOrDefault() != 0 && value.HasValue)
				{
					if (rgfb == null)
					{
						rgfb = new List<OfficeArtSpgrContainerFileBlock>();
					}
					int num = ((containerLength - 8 < rh.recLen.Value) ? (containerLength - 8) : ((int)rh.recLen.Value.Value));
					do
					{
						ulong currentPosition = DataToRead.CurrentPosition;
						OfficeArtSpgrContainerFileBlock officeArtSpgrContainerFileBlock = new OfficeArtSpgrContainerFileBlock(DataToRead, num, endOfMSO);
						if (officeArtSpgrContainerFileBlock == null)
						{
							break;
						}
						rgfb.Add(officeArtSpgrContainerFileBlock);
						num -= (int)(DataToRead.CurrentPosition - currentPosition);
					}
					while (num > 0);
				}
			}
			else
			{
				AddParsingNote(ParsingNoteType.Error, "Unable to read OfficeArtSpgrContainer!", DataToRead.CurrentPosition);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class OfficeArtBStoreContainerFileBlock : DataStructure
	{
		[Order(0uL)]
		public OfficeArtGlom glom;

		public OfficeArtBStoreContainerFileBlock(DataInByteArray DataToRead)
		{
			glom = new OfficeArtGlom(DataToRead);
		}
	}
	public class OfficeArtBStoreContainer : DataStructure
	{
		[Order(0uL)]
		public OfficeArtRecordHeader rh;

		[Order(1uL)]
		public List<OfficeArtBStoreContainerFileBlock> rgfb;

		public OfficeArtBStoreContainer(DataInByteArray DataToRead)
		{
			rh = new OfficeArtRecordHeader(DataToRead);
			if (rh != null && rh.recInstance != null && ((int?)rh.recInstance.Value).HasValue && rh.recInstance.Value > 0)
			{
				rgfb = new List<OfficeArtBStoreContainerFileBlock>(rh.recInstance.Value.Value);
				for (int i = 0; i < rh.recInstance.Value; i++)
				{
					rgfb.Add(new OfficeArtBStoreContainerFileBlock(DataToRead));
				}
			}
		}
	}
	public class OfficeArtDGContainer : DataStructure
	{
		[Order(0uL)]
		public OfficeArtRecordHeader rh;

		[Order(1uL)]
		public OfficeArtFdg drawingData;

		[Order(2uL)]
		public OfficeArtGlom regroupItems;

		[Order(3uL)]
		public OfficeArtSpgrContainer groupShape;

		[Order(4uL)]
		public OfficeArtSpContainer shape;

		[Order(5uL)]
		public OfficeArtGlom solvers;

		[Order(6uL)]
		public OfficeArtGlom OfficeArtFDGSL;

		[DoNotAutoProcess]
		public DataItem_UByteArray errorBytes;

		public OfficeArtDGContainer(DataInByteArray DataToRead, ushort containerLength, ulong endOfMSO)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			rh = new OfficeArtRecordHeader(DataToRead);
			if (rh != null && rh.recVer != null && ((int?)rh.recVer.Value).HasValue && rh.recType != null && ((int?)rh.recType.Value).HasValue)
			{
				byte? value = rh.recVer.Value;
				if (value != 15 || !value.HasValue || rh.recType.Value != 61442)
				{
					errorBytes = new DataItem_UByteArray(DataToRead, (ulong)containerLength - 8uL);
				}
				else
				{
					while (DataToRead.CurrentPosition < base.DataStructureOffset + containerLength)
					{
						ushort num = BitConverter.ToUInt16(DataToRead.PeekUByteArray(4uL), 2);
						bool flag = false;
						switch (num)
						{
						case 61448:
							drawingData = new OfficeArtFdg(DataToRead);
							break;
						case 61720:
							regroupItems = new OfficeArtGlom(DataToRead);
							break;
						case 61443:
							groupShape = new OfficeArtSpgrContainer(DataToRead, (int)(base.DataStructureOffset + containerLength - DataToRead.CurrentPosition), endOfMSO);
							break;
						case 61444:
							shape = new OfficeArtSpContainer(DataToRead, endOfMSO);
							break;
						case 61445:
							solvers = new OfficeArtGlom(DataToRead);
							break;
						case 61721:
							OfficeArtFDGSL = new OfficeArtGlom(DataToRead, endOfMSO - DataToRead.CurrentPosition + 1);
							break;
						default:
							flag = true;
							errorBytes = new DataItem_UByteArray(DataToRead, base.DataStructureOffset + containerLength - DataToRead.CurrentPosition);
							break;
						}
						if (flag)
						{
							AddParsingNote(ParsingNoteType.Error, "Unable to fully read OfficeArtDGContainer, incorrect top-level subtype found!", DataToRead.CurrentPosition);
							break;
						}
					}
				}
			}
			else
			{
				AddParsingNote(ParsingNoteType.Error, "Unable to read OfficeArtRecordHeader in OfficeArtDGContainer!", DataToRead.CurrentPosition);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class OfficeArtDGGContainer : DataStructure
	{
		[Order(0uL)]
		public OfficeArtRecordHeader rh;

		[Order(1uL)]
		public OfficeArtFdggBlock drawingGroup;

		[Order(2uL)]
		public OfficeArtBStoreContainer blipStore;

		[Order(3uL)]
		public OfficeArtFOPT drawingPrimaryOptions;

		[Order(4uL)]
		public OfficeArtFOPT drawingTertiaryOptions;

		[Order(5uL)]
		public OfficeArtGlom colorMRU;

		[Order(6uL)]
		public OfficeArtGlom splitColors;

		[Order(7uL)]
		public DataItem_UByteArray remainingData;

		public OfficeArtDGGContainer(DataInByteArray DataToRead, ulong containerLength, ulong endOfMSO)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			rh = new OfficeArtRecordHeader(DataToRead);
			if (rh != null && rh.recVer != null && ((int?)rh.recVer.Value).HasValue && rh.recType != null && ((int?)rh.recType.Value).HasValue)
			{
				if (rh.recType.Value == 61440)
				{
					bool flag = false;
					do
					{
						switch (BitConverter.ToUInt16(DataToRead.PeekUByteArray(4uL), 2))
						{
						case 61446:
							drawingGroup = new OfficeArtFdggBlock(DataToRead);
							break;
						case 61447:
							blipStore = new OfficeArtBStoreContainer(DataToRead);
							break;
						case 61451:
							drawingPrimaryOptions = new OfficeArtFOPT(DataToRead, endOfMSO);
							break;
						case 61730:
							drawingTertiaryOptions = new OfficeArtFOPT(DataToRead, endOfMSO);
							break;
						case 61722:
							colorMRU = new OfficeArtGlom(DataToRead);
							break;
						case 61726:
							splitColors = new OfficeArtGlom(DataToRead);
							break;
						default:
							flag = true;
							break;
						}
					}
					while (!flag && DataToRead.HasDataLeftToRead);
					if (rh.recLen != null && rh.recLen.Value.HasValue)
					{
						ulong num = DataToRead.CurrentPosition - base.DataStructureOffset;
						uint? value = rh.recLen.Value;
						if (num < value.GetValueOrDefault() && value.HasValue && containerLength - (DataToRead.CurrentPosition - base.DataStructureOffset) != 0)
						{
							remainingData = new DataItem_UByteArray(DataToRead, containerLength);
						}
					}
				}
				else
				{
					DataToRead.Seek(DataToRead.CurrentPosition - 8);
					remainingData = new DataItem_UByteArray(DataToRead, containerLength);
				}
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
}
namespace GUT.DataFormats.WordBinaryFormat
{
	public enum WordSPRMCodes
	{
		sprmPIstd = 17920,
		sprmPIstdPermute = 50689,
		sprmPIncLvl = 9730,
		sprmPJc80 = 9219,
		sprmPFSideBySide = 9220,
		sprmPFKeep = 9221,
		sprmPFKeepFollow = 9222,
		sprmPFPageBreakBefore = 9223,
		sprmPBrcl = 9224,
		sprmPBrcp = 9225,
		sprmPIlvl = 9738,
		sprmPIlfo = 17931,
		sprmPFNoLineNumb = 9228,
		sprmPChgTabsPapx = 50701,
		sprmPDxaRight80 = 33806,
		sprmPDxaLeft80 = 33807,
		sprmPNest80 = 17936,
		sprmPDxaLeft180 = 33809,
		sprmPDyaLine = 25618,
		sprmPDyaBefore = 42003,
		sprmPDyaAfter = 42004,
		sprmPChgTabs = 50709,
		sprmPFInTable = 9238,
		sprmPFTtp = 9239,
		sprmPDxaAbs = 33816,
		sprmPDyaAbs = 33817,
		sprmPDxaWidth = 33818,
		sprmPPc = 9755,
		sprmPBrcTop10 = 17948,
		sprmPBrcLeft10 = 17949,
		sprmPBrcBottom10 = 17950,
		sprmPBrcRight10 = 17951,
		sprmPBrcBetween10 = 17952,
		sprmPBrcBar10 = 17953,
		sprmPDxaFromText10 = 17954,
		sprmPWr = 9251,
		sprmPBrcTop70 = 17444,
		sprmPBrcTop80 = 25636,
		sprmPBrcLeft70 = 17445,
		sprmPBrcLeft80 = 25637,
		sprmPBrcBottom70 = 17446,
		sprmPBrcBottom80 = 25638,
		sprmPBrcRight70 = 17447,
		sprmPBrcRight80 = 25639,
		sprmPBrcBetween70 = 17448,
		sprmPBrcBetween80 = 25640,
		sprmPBrcBar70 = 17961,
		sprmPBrcBar80 = 26153,
		sprmPFNoAutoHyph = 9258,
		sprmPWHeightAbs = 17451,
		sprmPWHeightAbsOld = 33835,
		sprmPDcs = 17452,
		sprmPShd80 = 17453,
		sprmPDyaFromText = 33838,
		sprmPDxaFromText = 33839,
		sprmPFLocked = 9264,
		sprmPFWidowControl = 9265,
		sprmPRulerDontUse = 50738,
		sprmPFKinsoku = 9267,
		sprmPFWordWrap = 9268,
		sprmPFOverflowPunct = 9269,
		sprmPFTopLinePunct = 9270,
		sprmPFAutoSpaceDE = 9271,
		sprmPFAutoSpaceDN = 9272,
		sprmPWAlignFont = 17465,
		sprmPFrameTextFlow = 17466,
		sprmPISnapBaseLine = 9275,
		sprmPSpare10 = 50748,
		sprmPSpare11 = 50749,
		sprmPAnld80 = 50750,
		sprmPPropRMark90 = 50751,
		sprmPOutLvl = 9792,
		sprmPFBiDi = 9281,
		sprmPSpare12 = 9282,
		sprmPFNumRMIns = 9283,
		sprmPCrLf = 9284,
		sprmPNumRM = 50757,
		sprmPHugePapx = 26182,
		sprmPFUsePgsuSettings = 9287,
		sprmPFAdjustRight = 9288,
		sprmPItap = 26185,
		sprmPDtap = 26186,
		sprmPFInnerTableCell = 9291,
		sprmPFInnerTtp = 9292,
		sprmPShd = 50765,
		sprmPBrcTop = 50766,
		sprmPBrcLeft = 50767,
		sprmPBrcBottom = 50768,
		sprmPBrcRight = 50769,
		sprmPBrcBetween = 50770,
		sprmPBrcBar = 50771,
		sprmPAnldCv = 26196,
		sprmPDxcRight = 17493,
		sprmPDxcLeft = 17494,
		sprmPDxcLeft1 = 17495,
		sprmPDylBefore = 17496,
		sprmPDylAfter = 17497,
		sprmPFOpenTch = 9306,
		sprmPFDyaBeforeAuto = 9307,
		sprmPFDyaAfterAuto = 9308,
		sprmPDxaRight = 33885,
		sprmPDxaLeft = 33886,
		sprmPNest = 18015,
		sprmPDxaLeft1 = 33888,
		sprmPJc = 9313,
		sprmPFNoAllowOverlap = 9314,
		sprmPRuler = 50787,
		sprmPWall = 9828,
		sprmPIpgp = 25701,
		sprmPCnf = 50790,
		sprmPRsid = 25703,
		sprmPIstdList = 17512,
		sprmPIstdListPermute = 50793,
		sprmPDyaBeforeNotCp0 = 42090,
		sprmPTableProps = 25707,
		sprmPTIstdInfo = 50796,
		sprmPFContextualSpacing = 9325,
		sprmPRpf = 9326,
		sprmPPropRMark = 50799,
		sprmCFRMarkDel = 2048,
		sprmCFRMark = 2049,
		sprmCFFldVanish = 2050,
		sprmCPicLocation = 27139,
		sprmCIbstRMark = 18436,
		sprmCDttmRMark = 26629,
		sprmCFData = 2054,
		sprmCIdslRMark = 18439,
		sprmCChs = 59912,
		sprmCSymbol = 27145,
		sprmCFOle2 = 2058,
		sprmCIdCharType = 18443,
		sprmCHighlight = 10764,
		sprmCConflict = 59917,
		sprmCObjpLocation = 26638,
		sprmCConflictDesc = 51727,
		sprmCFFtcAsciSym = 10768,
		sprmCFWebHidden = 2065,
		sprmCHsp = 27154,
		sprmCNewIbstRM = 51731,
		sprmCFRMMove = 10260,
		sprmCRsidProp = 26645,
		sprmCRsidText = 26646,
		sprmCRsidRMDel = 26647,
		sprmCFSpecVanish = 2072,
		sprmCIstd = 18992,
		sprmCIstdPermute = 51761,
		sprmCDefault = 10802,
		sprmCPlain = 10803,
		sprmCKcd = 10804,
		sprmCFBold = 2101,
		sprmCFItalic = 2102,
		sprmCFStrike = 2103,
		sprmCFOutline = 2104,
		sprmCFShadow = 2105,
		sprmCFSmallCaps = 2106,
		sprmCFCaps = 2107,
		sprmCFVanish = 2108,
		sprmCFtcDefault = 19005,
		sprmCKul = 10814,
		sprmCSizePos = 59967,
		sprmCDxaSpace = 34880,
		sprmCLid = 19009,
		sprmCLidOld = 18497,
		sprmCIco = 10818,
		sprmCHps = 19011,
		sprmCHpsInc = 10820,
		sprmCHpsPos = 18501,
		sprmCHpsPosAdj = 10822,
		sprmCMajority = 51783,
		sprmCIss = 10824,
		sprmCHpsNew50 = 51785,
		sprmCHpsInc1 = 51786,
		sprmCHpsKern = 18507,
		sprmCMajority50 = 51788,
		sprmCHpsMul = 19021,
		sprmCHresi = 18510,
		sprmCRgFtc0 = 19023,
		sprmCRgFtc1 = 19024,
		sprmCRgFtc2 = 19025,
		sprmCCharScale = 18514,
		sprmCFDStrike = 10835,
		sprmCFImprint = 2132,
		sprmCFSpec = 2133,
		sprmCFObj = 2134,
		sprmCPropRMark90 = 51799,
		sprmCFEmboss = 2136,
		sprmCSfxText = 10329,
		sprmCFBiDi = 2138,
		sprmCFDiacColorOld = 2139,
		sprmCFBoldBi = 2140,
		sprmCFItalicBi = 2141,
		sprmCFtcBi = 19038,
		sprmCLidBi = 18527,
		sprmCIcoBi = 19040,
		sprmCHpsBi = 19041,
		sprmCDispFldRMark = 51810,
		sprmCIbstRMarkDel = 18531,
		sprmCDttmRMarkDel = 26724,
		sprmCBrc80 = 26725,
		sprmCShd80 = 18534,
		sprmCIdslRMarkDel = 18535,
		sprmCFUsePgsuSettings = 2152,
		sprmCMcpDefault = 51817,
		sprmCMcp = 51818,
		sprmCCpg = 18539,
		sprmCUsr = 10348,
		sprmCRgLid0_80 = 18541,
		sprmCRgLid1_80 = 18542,
		sprmCIdctHint = 10351,
		sprmCCv = 26736,
		sprmCShd = 51825,
		sprmCBrc = 51826,
		sprmCRgLid0 = 18547,
		sprmCRgLid1 = 18548,
		sprmCFNoProof = 2165,
		sprmCFitText = 51830,
		sprmCCvUl = 26743,
		sprmCFELayout = 51832,
		sprmCLbcCRJ = 10361,
		sprmCFLangApplied = 10874,
		sprmCUnknownHtml = 26747,
		sprmCCvPermute = 51836,
		sprmCFBoldPresent = 10365,
		sprmCFItalicPresent = 10366,
		sprmCTransNoProof0 = 10367,
		sprmCTransNoProof1 = 10368,
		sprmCHpsPresent = 18561,
		sprmCFComplexScripts = 2178,
		sprmCWall = 10883,
		sprmCPbi = 51844,
		sprmCCnf = 51845,
		sprmCNeedFontFixup = 10886,
		sprmCFNoBr = 10887,
		sprmCPbiIBullet = 26759,
		sprmCPbiGrf = 18568,
		sprmCPropRMark = 51849,
		sprmCFSdtVanish = 10896,
		sprmPicBrcl = 11776,
		sprmPicScale = 52737,
		sprmPicBrcTop70 = 19458,
		sprmPicBrcTop80 = 27650,
		sprmPicBrcLeft70 = 19459,
		sprmPicBrcLeft80 = 27651,
		sprmPicBrcBottom70 = 19460,
		sprmPicBrcBottom80 = 27652,
		sprmPicBrcRight70 = 19461,
		sprmPicBrcRight80 = 27653,
		sprmPicSpare4 = 52742,
		sprmCFOle2WasHere = 52743,
		sprmPicBrcTop = 52744,
		sprmPicBrcLeft = 52745,
		sprmPicBrcBottom = 52746,
		sprmPicBrcRight = 52747,
		sprmScnsPgn = 12288,
		sprmSiHeadingPgn = 12289,
		sprmSOlstAnm80 = 53762,
		sprmSDxaColWidth = 61955,
		sprmSDxaColSpacing = 61956,
		sprmSFEvenlySpaced = 12293,
		sprmSFProtected = 12294,
		sprmSDmBinFirst = 20487,
		sprmSDmBinOther = 20488,
		sprmSBkc = 12297,
		sprmSFTitlePage = 12298,
		sprmSCcolumns = 20491,
		sprmSDxaColumns = 36876,
		sprmSFAutoPgn = 12301,
		sprmSNfcPgn = 12302,
		sprmSDyaPgn = 45071,
		sprmSDxaPgn = 45072,
		sprmSFPgnRestart = 12305,
		sprmSFEndnote = 12306,
		sprmSLnc = 12307,
		sprmSGrpfIhdt = 12308,
		sprmSNLnnMod = 20501,
		sprmSDxaLnn = 36886,
		sprmSDyaHdrTop = 45079,
		sprmSDyaHdrBottom = 45080,
		sprmSLBetween = 12313,
		sprmSVjc = 12314,
		sprmSLnnMin = 20507,
		sprmSPgnStart = 20508,
		sprmSBOrientation = 12317,
		sprmSBCustomize = 12318,
		sprmSXaPage = 45087,
		sprmSYaPage = 45088,
		sprmSDxaLeft = 45089,
		sprmSDxaRight = 45090,
		sprmSDyaTop = 36899,
		sprmSDyaBottom = 36900,
		sprmSDzaGutter = 45093,
		sprmSDmPaperReq = 20518,
		sprmSPropRMark90 = 53799,
		sprmSFBiDi = 12840,
		sprmSFFacingCol = 12841,
		sprmSFRTLGutter = 12842,
		sprmSBrcTop80 = 28715,
		sprmSBrcLeft80 = 28716,
		sprmSBrcBottom80 = 28717,
		sprmSBrcRight80 = 28718,
		sprmSPgbProp = 21039,
		sprmSDxtCharSpace = 28720,
		sprmSDyaLinePitch = 36913,
		sprmSClm = 20530,
		sprmSTextFlow = 20531,
		sprmSBrcTop = 53812,
		sprmSBrcLeft = 53813,
		sprmSBrcBottom = 53814,
		sprmSBrcRight = 53815,
		sprmSOlstCv = 53816,
		sprmSWall = 12857,
		sprmSRsid = 28730,
		sprmSFpc = 12347,
		sprmSRncFtn = 12348,
		sprmSEpc = 12349,
		sprmSRncEdn = 12350,
		sprmSNFtn = 20543,
		sprmSNfcFtnRef = 20544,
		sprmSNEdn = 20545,
		sprmSNfcEdnRef = 20546,
		sprmSPropRMark = 53827,
		sprmTJc90 = 21504,
		sprmTDxaLeft = 38401,
		sprmTDxaLeftOld = 22017,
		sprmTDxaGapHalf = 38402,
		sprmTDxaGapHalfOld = 22018,
		sprmTFCantSplit90 = 13315,
		sprmTTableHeader = 13316,
		sprmTTableBorders80 = 54789,
		sprmTDefTable10 = 54790,
		sprmTDyaRowHeight = 37895,
		sprmTDefTable = 54792,
		sprmTDefTableShd80 = 54793,
		sprmTTlp = 29706,
		sprmTFBiDi = 22027,
		sprmTDefTableShd3rd = 54796,
		sprmTPc = 13837,
		sprmTDxaAbs = 37902,
		sprmTDyaAbs = 37903,
		sprmTDxaFromText = 37904,
		sprmTDyaFromText = 37905,
		sprmTDefTableShd = 54802,
		sprmTTableBorders = 54803,
		sprmTTableWidth = 62996,
		sprmTFAutofit = 13845,
		sprmTDefTableShd2nd = 54806,
		sprmTWidthBefore = 62999,
		sprmTWidthAfter = 63000,
		sprmTFKeepFollow = 13849,
		sprmTBrcTopCv = 54810,
		sprmTBrcLeftCv = 54811,
		sprmTBrcBottomCv = 54812,
		sprmTBrcRightCv = 54813,
		sprmTDxaFromTextRight = 37918,
		sprmTDyaFromTextBottom = 37919,
		sprmTSetBrc80 = 54816,
		sprmTInsert = 30241,
		sprmTDelete = 22050,
		sprmTDxaCol = 30243,
		sprmTMerge = 22052,
		sprmTSplit = 22053,
		sprmTSetBrc10 = 54822,
		sprmTSetShd80 = 30247,
		sprmTSetShdOdd80 = 30248,
		sprmTTextFlow = 30249,
		sprmTDiagLine80 = 54826,
		sprmTVertMerge = 54827,
		sprmTVertAlign = 54828,
		sprmTSetShd = 54829,
		sprmTSetShdOdd = 54830,
		sprmTSetBrc = 54831,
		sprmTDiagLine = 54832,
		sprmTCellSpacing = 54833,
		sprmTCellPadding = 54834,
		sprmTCellSpacingDefault = 54835,
		sprmTCellPaddingDefault = 54836,
		sprmTCellWidth = 54837,
		sprmTFitText = 63030,
		sprmTCellSpacingOuter = 54839,
		sprmTCellPaddingOuter = 54840,
		sprmTFCellNoWrap = 54841,
		sprmTIstd = 22074,
		sprmTSetShdRaw = 54843,
		sprmTSetShdOddRaw = 54844,
		sprmTIstdPermute = 54845,
		sprmTCellPaddingStyle = 54846,
		sprmTSetShdTable = 54880,
		sprmTWidthIndent = 63073,
		sprmTCellBrcType = 54882,
		sprmTFNeverBeenAutofit = 13923,
		sprmTFBiDi90 = 22116,
		sprmTFNoAllowOverlap = 13413,
		sprmTFCantSplit = 13414,
		sprmTPropRMark = 54887,
		sprmTWall = 13928,
		sprmTIpgp = 29801,
		sprmTCnf = 54890,
		sprmTSetShdTableDef = 54891,
		sprmTDiagLine2nd = 54892,
		sprmTDiagLine3rd = 54893,
		sprmTDiagLine4th = 54894,
		sprmTDiagLine5th = 54895,
		sprmTDefTableShdRaw = 54896,
		sprmTDefTableShdRaw2nd = 54897,
		sprmTDefTableShdRaw3rd = 54898,
		sprmTSetShdRowFirst = 54899,
		sprmTSetShdRowLast = 54900,
		sprmTSetShdColFirst = 54901,
		sprmTSetShdColLast = 54902,
		sprmTSetShdBand1 = 54903,
		sprmTSetShdBand2 = 54904,
		sprmTRsid = 29817,
		sprmTCellWidthStyle = 62586,
		sprmTCellPaddingStyleBad = 54907,
		sprmTCellVertAlignStyle = 13436,
		sprmTCellNoWrapStyle = 13437,
		sprmTCellFitTextStyle = 13438,
		sprmTCellBrcTopStyle = 54399,
		sprmTCellBrcBottomStyle = 54912,
		sprmTCellBrcLeftStyle = 54913,
		sprmTCellBrcRightStyle = 54914,
		sprmTCellBrcInsideHStyle = 54915,
		sprmTCellBrcInsideVStyle = 54916,
		sprmTCellBrcTL2BRStyle = 54917,
		sprmTCellBrcTR2BLStyle = 54918,
		sprmTCellShdStyle = 54919,
		sprmTCHorzBands = 13448,
		sprmTCVertBands = 13449,
		sprmTJc = 21642,
		sprmTTableBrcTop = 54923,
		sprmTTableBrcLeft = 54924,
		sprmTTableBrcBottom = 54925,
		sprmTTableBrcRight = 54926,
		sprmTTableBrcInsideH = 54927,
		sprmTTableBrcInsideV = 54928
	}
	public class FIBBASE : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 wIdent;

		[Order(1uL)]
		public DataItem_UInt16 nFib;

		[Order(2uL)]
		public DataItem_UInt16 unused;

		[Order(3uL)]
		public DataItem_UInt16 lid;

		[Order(4uL)]
		public DataItem_UInt16 pnNext;

		[BeginBitField(2u)]
		[Order(5uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fDot;

		[BitFieldSize(1u)]
		[Order(6uL)]
		public DataItem_UInt8 fGlsy;

		[BitFieldSize(1u)]
		[Order(7uL)]
		public DataItem_UInt8 fComplex;

		[Order(8uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fHasPic;

		[Order(9uL)]
		[BitFieldSize(4u)]
		public DataItem_UInt8 cQuickSaves;

		[BitFieldSize(1u)]
		[Order(10uL)]
		public DataItem_UInt8 fEncrypted;

		[BitFieldSize(1u)]
		[Order(11uL)]
		public DataItem_UInt8 fWhichTblStm;

		[Order(12uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fReadOnlyRecommended;

		[BitFieldSize(1u)]
		[Order(13uL)]
		public DataItem_UInt8 fWriteReservation;

		[Order(14uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fExtChar;

		[BitFieldSize(1u)]
		[Order(15uL)]
		public DataItem_UInt8 fLoadOverride;

		[BitFieldSize(1u)]
		[Order(16uL)]
		public DataItem_UInt8 fFarEast;

		[Order(17uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fObfuscated;

		[Order(18uL)]
		public DataItem_UInt16 nFibBack;

		[Order(19uL)]
		public DataItem_UInt32 lKey;

		[Order(20uL)]
		public DataItem_Int8 envr;

		[Order(21uL)]
		public DataItem_Int8 flags2;

		[Order(22uL)]
		public DataItem_UInt16 reserved3;

		[Order(23uL)]
		public DataItem_UInt16 reserved4;

		[Order(24uL)]
		public DataItem_UInt32 reserved5;

		[Order(25uL)]
		public DataItem_UInt32 reserved6;

		public FIBBASE(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class FIBRGCSWNEW : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 nFibNew;

		[Order(1uL)]
		public DataItem_ByteArray fibRgCswNewData2000;

		[Order(2uL)]
		public DataItem_ByteArray fibRgCswNewData2007;

		public FIBRGCSWNEW(DataInByteArray Data)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			nFibNew = new DataItem_UInt16(Data);
			if (nFibNew.Value == 217 || nFibNew.Value == 257 || nFibNew.Value == 268)
			{
				fibRgCswNewData2000 = new DataItem_ByteArray(Data, 2uL);
			}
			else if (nFibNew.Value == 274)
			{
				fibRgCswNewData2007 = new DataItem_ByteArray(Data, 7uL);
			}
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class FIBRGFCLCB97 : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt32 fcStshfOrig;

		[Order(1uL)]
		public DataItem_UInt32 lcbStshfOrig;

		[Order(2uL)]
		public DataItem_UInt32 fcStshf;

		[Order(3uL)]
		public DataItem_UInt32 lcbStshf;

		[Order(4uL)]
		public DataItem_UInt32 fcPlcffndRef;

		[Order(5uL)]
		public DataItem_UInt32 lcbPlcffndRef;

		[Order(6uL)]
		public DataItem_UInt32 fcPlcffndTxt;

		[Order(7uL)]
		public DataItem_UInt32 lcbPlcffndTxt;

		[Order(8uL)]
		public DataItem_UInt32 fcPlcfandRef;

		[Order(9uL)]
		public DataItem_UInt32 lcbPlcfandRef;

		[Order(10uL)]
		public DataItem_UInt32 fcPlcfandTxt;

		[Order(11uL)]
		public DataItem_UInt32 lcbPlcfandTxt;

		[Order(12uL)]
		public DataItem_UInt32 fcPlcfSed;

		[Order(13uL)]
		public DataItem_UInt32 lcbPlcfSed;

		[Order(14uL)]
		public DataItem_UInt32 fcPlcPad;

		[Order(15uL)]
		public DataItem_UInt32 lcbPlcPad;

		[Order(16uL)]
		public DataItem_UInt32 fcPlcfPhe;

		[Order(17uL)]
		public DataItem_UInt32 lcbPlcfPhe;

		[Order(18uL)]
		public DataItem_UInt32 fcSttbfGlsy;

		[Order(19uL)]
		public DataItem_UInt32 lcbSttbfGlsy;

		[Order(20uL)]
		public DataItem_UInt32 fcPlcfGlsy;

		[Order(21uL)]
		public DataItem_UInt32 lcbPlcfGlsy;

		[Order(22uL)]
		public DataItem_UInt32 fcPlcfHdd;

		[Order(23uL)]
		public DataItem_UInt32 lcbPlcfHdd;

		[Order(24uL)]
		public DataItem_UInt32 fcPlcfBteChpx;

		[Order(25uL)]
		public DataItem_UInt32 lcbPlcfBteChpx;

		[Order(26uL)]
		public DataItem_UInt32 fcPlcfBtePapx;

		[Order(27uL)]
		public DataItem_UInt32 lcbPlcfBtePapx;

		[Order(28uL)]
		public DataItem_UInt32 fcPlcfSea;

		[Order(29uL)]
		public DataItem_UInt32 lcbPlcfSea;

		[Order(30uL)]
		public DataItem_UInt32 fcSttbfFfn;

		[Order(31uL)]
		public DataItem_UInt32 lcbSttbfFfn;

		[Order(32uL)]
		public DataItem_UInt32 fcPlcfFldMom;

		[Order(33uL)]
		public DataItem_UInt32 lcbPlcfFldMom;

		[Order(34uL)]
		public DataItem_UInt32 fcPlcfFldHdr;

		[Order(35uL)]
		public DataItem_UInt32 lcbPlcfFldHdr;

		[Order(36uL)]
		public DataItem_UInt32 fcPlcfFldFtn;

		[Order(37uL)]
		public DataItem_UInt32 lcbPlcfFldFtn;

		[Order(38uL)]
		public DataItem_UInt32 fcPlcfFldAtn;

		[Order(39uL)]
		public DataItem_UInt32 lcbPlcfFldAtn;

		[Order(40uL)]
		public DataItem_UInt32 fcPlcfFldMcr;

		[Order(41uL)]
		public DataItem_UInt32 lcbPlcfFldMcr;

		[Order(42uL)]
		public DataItem_UInt32 fcSttbfBkmk;

		[Order(43uL)]
		public DataItem_UInt32 lcbSttbfBkmk;

		[Order(44uL)]
		public DataItem_UInt32 fcPlcfBkf;

		[Order(45uL)]
		public DataItem_UInt32 lcbPlcfBkf;

		[Order(46uL)]
		public DataItem_UInt32 fcPlcfBkl;

		[Order(47uL)]
		public DataItem_UInt32 lcbPlcfBkl;

		[Order(48uL)]
		public DataItem_UInt32 fcCmds;

		[Order(49uL)]
		public DataItem_UInt32 lcbCmds;

		[Order(50uL)]
		public DataItem_UInt32 fcUnused1;

		[Order(51uL)]
		public DataItem_UInt32 lcbUnused1;

		[Order(52uL)]
		public DataItem_UInt32 fcSttbfMcr;

		[Order(53uL)]
		public DataItem_UInt32 lcbSttbfMcr;

		[Order(54uL)]
		public DataItem_UInt32 fcPrDrvr;

		[Order(55uL)]
		public DataItem_UInt32 lcbPrDrvr;

		[Order(56uL)]
		public DataItem_UInt32 fcPrEnvPort;

		[Order(57uL)]
		public DataItem_UInt32 lcbPrEnvPort;

		[Order(58uL)]
		public DataItem_UInt32 fcPrEnvLand;

		[Order(59uL)]
		public DataItem_UInt32 lcbPrEnvLand;

		[Order(60uL)]
		public DataItem_UInt32 fcWss;

		[Order(61uL)]
		public DataItem_UInt32 lcbWss;

		[Order(62uL)]
		public DataItem_UInt32 fcDop;

		[Order(63uL)]
		public DataItem_UInt32 lcbDop;

		[Order(64uL)]
		public DataItem_UInt32 fcSttbfAssoc;

		[Order(65uL)]
		public DataItem_UInt32 lcbSttbfAssoc;

		[Order(66uL)]
		public DataItem_UInt32 fcClx;

		[Order(67uL)]
		public DataItem_UInt32 lcbClx;

		[Order(68uL)]
		public DataItem_UInt32 fcPlcfPgdFtn;

		[Order(69uL)]
		public DataItem_UInt32 lcbPlcfPgdFtn;

		[Order(70uL)]
		public DataItem_UInt32 fcAutosaveSource;

		[Order(71uL)]
		public DataItem_UInt32 lcbAutosaveSource;

		[Order(72uL)]
		public DataItem_UInt32 fcGrpXstAtnOwners;

		[Order(73uL)]
		public DataItem_UInt32 lcbGrpXstAtnOwners;

		[Order(74uL)]
		public DataItem_UInt32 fcSttbfAtnBkmk;

		[Order(75uL)]
		public DataItem_UInt32 lcbSttbfAtnBkmk;

		[Order(76uL)]
		public DataItem_UInt32 fcUnused2;

		[Order(77uL)]
		public DataItem_UInt32 lcbUnused2;

		[Order(78uL)]
		public DataItem_UInt32 fcUnused3;

		[Order(79uL)]
		public DataItem_UInt32 lcbUnused3;

		[Order(80uL)]
		public DataItem_UInt32 fcPlcSpaMom;

		[Order(81uL)]
		public DataItem_UInt32 lcbPlcSpaMom;

		[Order(82uL)]
		public DataItem_UInt32 fcPlcSpaHdr;

		[Order(83uL)]
		public DataItem_UInt32 lcbPlcSpaHdr;

		[Order(84uL)]
		public DataItem_UInt32 fcPlcfAtnBkf;

		[Order(85uL)]
		public DataItem_UInt32 lcbPlcfAtnBkf;

		[Order(86uL)]
		public DataItem_UInt32 fcPlcfAtnBkl;

		[Order(87uL)]
		public DataItem_UInt32 lcbPlcfAtnBkl;

		[Order(88uL)]
		public DataItem_UInt32 fcPms;

		[Order(89uL)]
		public DataItem_UInt32 lcbPms;

		[Order(90uL)]
		public DataItem_UInt32 fcFormFldSttbs;

		[Order(91uL)]
		public DataItem_UInt32 lcbFormFldSttbs;

		[Order(92uL)]
		public DataItem_UInt32 fcPlcfendRef;

		[Order(93uL)]
		public DataItem_UInt32 lcbPlcfendRef;

		[Order(94uL)]
		public DataItem_UInt32 fcPlcfendTxt;

		[Order(95uL)]
		public DataItem_UInt32 lcbPlcfendTxt;

		[Order(96uL)]
		public DataItem_UInt32 fcPlcfFldEdn;

		[Order(97uL)]
		public DataItem_UInt32 lcbPlcfFldEdn;

		[Order(98uL)]
		public DataItem_UInt32 fcUnused4;

		[Order(99uL)]
		public DataItem_UInt32 lcbUnused4;

		[Order(100uL)]
		public DataItem_UInt32 fcDggInfo;

		[Order(101uL)]
		public DataItem_UInt32 lcbDggInfo;

		[Order(102uL)]
		public DataItem_UInt32 fcSttbfRMark;

		[Order(103uL)]
		public DataItem_UInt32 lcbSttbfRMark;

		[Order(104uL)]
		public DataItem_UInt32 fcSttbfCaption;

		[Order(105uL)]
		public DataItem_UInt32 lcbSttbfCaption;

		[Order(106uL)]
		public DataItem_UInt32 fcSttbfAutoCaption;

		[Order(107uL)]
		public DataItem_UInt32 lcbSttbfAutoCaption;

		[Order(108uL)]
		public DataItem_UInt32 fcPlcfWkb;

		[Order(109uL)]
		public DataItem_UInt32 lcbPlcfWkb;

		[Order(110uL)]
		public DataItem_UInt32 fcPlcfSpl;

		[Order(111uL)]
		public DataItem_UInt32 lcbPlcfSpl;

		[Order(112uL)]
		public DataItem_UInt32 fcPlcftxbxTxt;

		[Order(113uL)]
		public DataItem_UInt32 lcbPlcftxbxTxt;

		[Order(114uL)]
		public DataItem_UInt32 fcPlcfFldTxbx;

		[Order(115uL)]
		public DataItem_UInt32 lcbPlcfFldTxbx;

		[Order(116uL)]
		public DataItem_UInt32 fcPlcfHdrtxbxTxt;

		[Order(117uL)]
		public DataItem_UInt32 lcbPlcfHdrtxbxTxt;

		[Order(118uL)]
		public DataItem_UInt32 fcPlcffldHdrTxbx;

		[Order(119uL)]
		public DataItem_UInt32 lcbPlcffldHdrTxbx;

		[Order(120uL)]
		public DataItem_UInt32 fcStwUser;

		[Order(121uL)]
		public DataItem_UInt32 lcbStwUser;

		[Order(122uL)]
		public DataItem_UInt32 fcSttbTtmbd;

		[Order(123uL)]
		public DataItem_UInt32 lcbSttbTtmbd;

		[Order(124uL)]
		public DataItem_UInt32 fcCookieData;

		[Order(125uL)]
		public DataItem_UInt32 lcbCookieData;

		[Order(126uL)]
		public DataItem_UInt32 fcPgdMotherOldOld;

		[Order(127uL)]
		public DataItem_UInt32 lcbPgdMotherOldOld;

		[Order(128uL)]
		public DataItem_UInt32 fcBkdMotherOldOld;

		[Order(129uL)]
		public DataItem_UInt32 lcbBkdMotherOldOld;

		[Order(130uL)]
		public DataItem_UInt32 fcPgdFtnOldOld;

		[Order(131uL)]
		public DataItem_UInt32 lcbPgdFtnOldOld;

		[Order(132uL)]
		public DataItem_UInt32 fcBkdFtnOldOld;

		[Order(133uL)]
		public DataItem_UInt32 lcbBkdFtnOldOld;

		[Order(134uL)]
		public DataItem_UInt32 fcPgdEdnOldOld;

		[Order(135uL)]
		public DataItem_UInt32 lcbPgdEdnOldOld;

		[Order(136uL)]
		public DataItem_UInt32 fcBkdEdnOldOld;

		[Order(137uL)]
		public DataItem_UInt32 lcbBkdEdnOldOld;

		[Order(138uL)]
		public DataItem_UInt32 fcSttbfIntlFld;

		[Order(139uL)]
		public DataItem_UInt32 lcbSttbfIntlFld;

		[Order(140uL)]
		public DataItem_UInt32 fcRouteSlip;

		[Order(141uL)]
		public DataItem_UInt32 lcbRouteSlip;

		[Order(142uL)]
		public DataItem_UInt32 fcSttbSavedBy;

		[Order(143uL)]
		public DataItem_UInt32 lcbSttbSavedBy;

		[Order(144uL)]
		public DataItem_UInt32 fcSttbFnm;

		[Order(145uL)]
		public DataItem_UInt32 lcbSttbFnm;

		[Order(146uL)]
		public DataItem_UInt32 fcPlfLst;

		[Order(147uL)]
		public DataItem_UInt32 lcbPlfLst;

		[Order(148uL)]
		public DataItem_UInt32 fcPlfLfo;

		[Order(149uL)]
		public DataItem_UInt32 lcbPlfLfo;

		[Order(150uL)]
		public DataItem_UInt32 fcPlcfTxbxBkd;

		[Order(151uL)]
		public DataItem_UInt32 lcbPlcfTxbxBkd;

		[Order(152uL)]
		public DataItem_UInt32 fcPlcfTxbxHdrBkd;

		[Order(153uL)]
		public DataItem_UInt32 lcbPlcfTxbxHdrBkd;

		[Order(154uL)]
		public DataItem_UInt32 fcDocUndoWord9;

		[Order(155uL)]
		public DataItem_UInt32 lcbDocUndoWord9;

		[Order(156uL)]
		public DataItem_UInt32 fcRgbUse;

		[Order(157uL)]
		public DataItem_UInt32 lcbRgbUse;

		[Order(158uL)]
		public DataItem_UInt32 fcUsp;

		[Order(159uL)]
		public DataItem_UInt32 lcbUsp;

		[Order(160uL)]
		public DataItem_UInt32 fcUskf;

		[Order(161uL)]
		public DataItem_UInt32 lcbUskf;

		[Order(162uL)]
		public DataItem_UInt32 fcPlcupcRgbUse;

		[Order(163uL)]
		public DataItem_UInt32 lcbPlcupcRgbUse;

		[Order(164uL)]
		public DataItem_UInt32 fcPlcupcUsp;

		[Order(165uL)]
		public DataItem_UInt32 lcbPlcupcUsp;

		[Order(166uL)]
		public DataItem_UInt32 fcSttbGlsyStyle;

		[Order(167uL)]
		public DataItem_UInt32 lcbSttbGlsyStyle;

		[Order(168uL)]
		public DataItem_UInt32 fcPlgosl;

		[Order(169uL)]
		public DataItem_UInt32 lcbPlgosl;

		[Order(170uL)]
		public DataItem_UInt32 fcPlcocx;

		[Order(171uL)]
		public DataItem_UInt32 lcbPlcocx;

		[Order(172uL)]
		public DataItem_UInt32 fcPlcfBteLvc;

		[Order(173uL)]
		public DataItem_UInt32 lcbPlcfBteLvc;

		[Order(174uL)]
		public DataItem_UInt32 dwLowDateTime;

		[Order(175uL)]
		public DataItem_UInt32 dwHighDateTime;

		[Order(176uL)]
		public DataItem_UInt32 fcPlcfLvcPre10;

		[Order(177uL)]
		public DataItem_UInt32 lcbPlcfLvcPre10;

		[Order(178uL)]
		public DataItem_UInt32 fcPlcfAsumy;

		[Order(179uL)]
		public DataItem_UInt32 lcbPlcfAsumy;

		[Order(180uL)]
		public DataItem_UInt32 fcPlcfGram;

		[Order(181uL)]
		public DataItem_UInt32 lcbPlcfGram;

		[Order(182uL)]
		public DataItem_UInt32 fcSttbListNames;

		[Order(183uL)]
		public DataItem_UInt32 lcbSttbListNames;

		[Order(184uL)]
		public DataItem_UInt32 fcSttbfUssr;

		[Order(185uL)]
		public DataItem_UInt32 lcbSttbfUssr;

		public FIBRGFCLCB97(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class FIBRGFCLCB2000 : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt32 fcPlcfTch;

		[Order(1uL)]
		public DataItem_UInt32 lcbPlcfTch;

		[Order(2uL)]
		public DataItem_UInt32 fcRmdThreading;

		[Order(3uL)]
		public DataItem_UInt32 lcbRmdThreading;

		[Order(4uL)]
		public DataItem_UInt32 fcMid;

		[Order(5uL)]
		public DataItem_UInt32 lcbMid;

		[Order(6uL)]
		public DataItem_UInt32 fcSttbRgtplc;

		[Order(7uL)]
		public DataItem_UInt32 lcbSttbRgtplc;

		[Order(8uL)]
		public DataItem_UInt32 fcMsoEnvelope;

		[Order(9uL)]
		public DataItem_UInt32 lcbMsoEnvelope;

		[Order(10uL)]
		public DataItem_UInt32 fcPlcfLad;

		[Order(11uL)]
		public DataItem_UInt32 lcbPlcfLad;

		[Order(12uL)]
		public DataItem_UInt32 fcRgDofr;

		[Order(13uL)]
		public DataItem_UInt32 lcbRgDofr;

		[Order(14uL)]
		public DataItem_UInt32 fcPlcosl;

		[Order(15uL)]
		public DataItem_UInt32 lcbPlcosl;

		[Order(16uL)]
		public DataItem_UInt32 fcPlcfCookieOld;

		[Order(17uL)]
		public DataItem_UInt32 lcbPlcfCookieOld;

		[Order(18uL)]
		public DataItem_UInt32 fcPgdMotherOld;

		[Order(19uL)]
		public DataItem_UInt32 lcbPgdMotherOld;

		[Order(20uL)]
		public DataItem_UInt32 fcBkdMotherOld;

		[Order(21uL)]
		public DataItem_UInt32 lcbBkdMotherOld;

		[Order(22uL)]
		public DataItem_UInt32 fcPgdFtnOld;

		[Order(23uL)]
		public DataItem_UInt32 lcbPgdFtnOld;

		[Order(24uL)]
		public DataItem_UInt32 fcBkdFtnOld;

		[Order(25uL)]
		public DataItem_UInt32 lcbBkdFtnOld;

		[Order(26uL)]
		public DataItem_UInt32 fcPgdEdnOld;

		[Order(27uL)]
		public DataItem_UInt32 lcbPgdEdnOld;

		[Order(28uL)]
		public DataItem_UInt32 fcBkdEdnOld;

		[Order(29uL)]
		public DataItem_UInt32 lcbBkdEdnOld;

		public FIBRGFCLCB2000(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class FIBRGFCLCB2002 : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt32 fcUnused1;

		[Order(1uL)]
		public DataItem_UInt32 lcbUnused1;

		[Order(2uL)]
		public DataItem_UInt32 fcPlcfPgp;

		[Order(3uL)]
		public DataItem_UInt32 lcbPlcfPgp;

		[Order(4uL)]
		public DataItem_UInt32 fcPlcfuim;

		[Order(5uL)]
		public DataItem_UInt32 lcbPlcfuim;

		[Order(6uL)]
		public DataItem_UInt32 fcPlfguidUim;

		[Order(7uL)]
		public DataItem_UInt32 lcbPlfguidUim;

		[Order(8uL)]
		public DataItem_UInt32 fcAtrdExtra;

		[Order(9uL)]
		public DataItem_UInt32 lcbAtrdExtra;

		[Order(10uL)]
		public DataItem_UInt32 fcPlrsid;

		[Order(11uL)]
		public DataItem_UInt32 lcbPlrsid;

		[Order(12uL)]
		public DataItem_UInt32 fcSttbfBkmkFactoid;

		[Order(13uL)]
		public DataItem_UInt32 lcbSttbfBkmkFactoid;

		[Order(14uL)]
		public DataItem_UInt32 fcPlcfBkfFactoid;

		[Order(15uL)]
		public DataItem_UInt32 lcbPlcfBkfFactoid;

		[Order(16uL)]
		public DataItem_UInt32 fcPlcfcookie;

		[Order(17uL)]
		public DataItem_UInt32 lcbPlcfcookie;

		[Order(18uL)]
		public DataItem_UInt32 fcPlcfBklFactoid;

		[Order(19uL)]
		public DataItem_UInt32 lcbPlcfBklFactoid;

		[Order(20uL)]
		public DataItem_UInt32 fcFactoidData;

		[Order(21uL)]
		public DataItem_UInt32 lcbFactoidData;

		[Order(22uL)]
		public DataItem_UInt32 fcDocUndo;

		[Order(23uL)]
		public DataItem_UInt32 lcbDocUndo;

		[Order(24uL)]
		public DataItem_UInt32 fcSttbfBkmkFcc;

		[Order(25uL)]
		public DataItem_UInt32 lcbSttbfBkmkFcc;

		[Order(26uL)]
		public DataItem_UInt32 fcPlcfBkfFcc;

		[Order(27uL)]
		public DataItem_UInt32 lcbPlcfBkfFcc;

		[Order(28uL)]
		public DataItem_UInt32 fcPlcfBklFcc;

		[Order(29uL)]
		public DataItem_UInt32 lcbPlcfBklFcc;

		[Order(30uL)]
		public DataItem_UInt32 fcSttbfbkmkBPRepairs;

		[Order(31uL)]
		public DataItem_UInt32 lcbSttbfbkmkBPRepairs;

		[Order(32uL)]
		public DataItem_UInt32 fcPlcfbkfBPRepairs;

		[Order(33uL)]
		public DataItem_UInt32 lcbPlcfbkfBPRepairs;

		[Order(34uL)]
		public DataItem_UInt32 fcPlcfbklBPRepairs;

		[Order(35uL)]
		public DataItem_UInt32 lcbPlcfbklBPRepairs;

		[Order(36uL)]
		public DataItem_UInt32 fcPmsNew;

		[Order(37uL)]
		public DataItem_UInt32 lcbPmsNew;

		[Order(38uL)]
		public DataItem_UInt32 fcODSO;

		[Order(39uL)]
		public DataItem_UInt32 lcbODSO;

		[Order(40uL)]
		public DataItem_UInt32 fcPlcfpmiOldXP;

		[Order(41uL)]
		public DataItem_UInt32 lcbPlcfpmiOldXP;

		[Order(42uL)]
		public DataItem_UInt32 fcPlcfpmiNewXP;

		[Order(43uL)]
		public DataItem_UInt32 lcbPlcfpmiNewXP;

		[Order(44uL)]
		public DataItem_UInt32 fcPlcfpmiMixedXP;

		[Order(45uL)]
		public DataItem_UInt32 lcbPlcfpmiMixedXP;

		[Order(46uL)]
		public DataItem_UInt32 fcUnused2;

		[Order(47uL)]
		public DataItem_UInt32 lcbUnused2;

		[Order(48uL)]
		public DataItem_UInt32 fcPlcffactoid;

		[Order(49uL)]
		public DataItem_UInt32 lcbPlcffactoid;

		[Order(50uL)]
		public DataItem_UInt32 fcPlcflvcOldXP;

		[Order(51uL)]
		public DataItem_UInt32 lcbPlcflvcOldXP;

		[Order(52uL)]
		public DataItem_UInt32 fcPlcflvcNewXP;

		[Order(53uL)]
		public DataItem_UInt32 lcbPlcflvcNewXP;

		[Order(54uL)]
		public DataItem_UInt32 fcPlcflvcMixedXP;

		[Order(55uL)]
		public DataItem_UInt32 lcbPlcflvcMixedXP;

		public FIBRGFCLCB2002(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class FIBRGFCLCB2003 : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt32 fcHplxsdr;

		[Order(1uL)]
		public DataItem_UInt32 lcbHplxsdr;

		[Order(2uL)]
		public DataItem_UInt32 fcSttbfBkmkSdt;

		[Order(3uL)]
		public DataItem_UInt32 lcbSttbfBkmkSdt;

		[Order(4uL)]
		public DataItem_UInt32 fcPlcfBkfSdt;

		[Order(5uL)]
		public DataItem_UInt32 lcbPlcfBkfSdt;

		[Order(6uL)]
		public DataItem_UInt32 fcPlcfBklSdt;

		[Order(7uL)]
		public DataItem_UInt32 lcbPlcfBklSdt;

		[Order(8uL)]
		public DataItem_UInt32 fcCustomXForm;

		[Order(9uL)]
		public DataItem_UInt32 lcbCustomXForm;

		[Order(10uL)]
		public DataItem_UInt32 fcSttbfBkmkProt;

		[Order(11uL)]
		public DataItem_UInt32 lcbSttbfBkmkProt;

		[Order(12uL)]
		public DataItem_UInt32 fcPlcfBkfProt;

		[Order(13uL)]
		public DataItem_UInt32 lcbPlcfBkfProt;

		[Order(14uL)]
		public DataItem_UInt32 fcPlcfBklProt;

		[Order(15uL)]
		public DataItem_UInt32 lcbPlcfBklProt;

		[Order(16uL)]
		public DataItem_UInt32 fcSttbProtUser;

		[Order(17uL)]
		public DataItem_UInt32 lcbSttbProtUser;

		[Order(18uL)]
		public DataItem_UInt32 fcUnused;

		[Order(19uL)]
		public DataItem_UInt32 lcbUnused;

		[Order(20uL)]
		public DataItem_UInt32 fcPlcfpmiOld;

		[Order(21uL)]
		public DataItem_UInt32 lcbPlcfpmiOld;

		[Order(22uL)]
		public DataItem_UInt32 fcPlcfpmiOldInline;

		[Order(23uL)]
		public DataItem_UInt32 lcbPlcfpmiOldInline;

		[Order(24uL)]
		public DataItem_UInt32 fcPlcfpmiNew;

		[Order(25uL)]
		public DataItem_UInt32 lcbPlcfpmiNew;

		[Order(26uL)]
		public DataItem_UInt32 fcPlcfpmiNewInline;

		[Order(27uL)]
		public DataItem_UInt32 lcbPlcfpmiNewInline;

		[Order(28uL)]
		public DataItem_UInt32 fcPlcflvcOld;

		[Order(29uL)]
		public DataItem_UInt32 lcbPlcflvcOld;

		[Order(30uL)]
		public DataItem_UInt32 fcPlcflvcOldInline;

		[Order(31uL)]
		public DataItem_UInt32 lcbPlcflvcOldInline;

		[Order(32uL)]
		public DataItem_UInt32 fcPlcflvcNew;

		[Order(33uL)]
		public DataItem_UInt32 lcbPlcflvcNew;

		[Order(34uL)]
		public DataItem_UInt32 fcPlcflvcNewInline;

		[Order(35uL)]
		public DataItem_UInt32 lcbPlcflvcNewInline;

		[Order(36uL)]
		public DataItem_UInt32 fcPgdMother;

		[Order(37uL)]
		public DataItem_UInt32 lcbPgdMother;

		[Order(38uL)]
		public DataItem_UInt32 fcBkdMother;

		[Order(39uL)]
		public DataItem_UInt32 lcbBkdMother;

		[Order(40uL)]
		public DataItem_UInt32 fcAfdMother;

		[Order(41uL)]
		public DataItem_UInt32 lcbAfdMother;

		[Order(42uL)]
		public DataItem_UInt32 fcPgdFtn;

		[Order(43uL)]
		public DataItem_UInt32 lcbPgdFtn;

		[Order(44uL)]
		public DataItem_UInt32 fcBkdFtn;

		[Order(45uL)]
		public DataItem_UInt32 lcbBkdFtn;

		[Order(46uL)]
		public DataItem_UInt32 fcAfdFtn;

		[Order(47uL)]
		public DataItem_UInt32 lcbAfdFtn;

		[Order(48uL)]
		public DataItem_UInt32 fcPgdEdn;

		[Order(49uL)]
		public DataItem_UInt32 lcbPgdEdn;

		[Order(50uL)]
		public DataItem_UInt32 fcBkdEdn;

		[Order(51uL)]
		public DataItem_UInt32 lcbBkdEdn;

		[Order(52uL)]
		public DataItem_UInt32 fcAfdEdn;

		[Order(53uL)]
		public DataItem_UInt32 lcbAfdEdn;

		[Order(54uL)]
		public DataItem_UInt32 fcAfd;

		[Order(55uL)]
		public DataItem_UInt32 lcbAfd;

		public FIBRGFCLCB2003(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class FIBRGFCLCB2007 : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt32 fcPlcfmthd;

		[Order(1uL)]
		public DataItem_UInt32 lcbPlcfmthd;

		[Order(2uL)]
		public DataItem_UInt32 fcSttbfBkmkMoveFrom;

		[Order(3uL)]
		public DataItem_UInt32 lcbSttbfBkmkMoveFrom;

		[Order(4uL)]
		public DataItem_UInt32 fcPlcfBkfMoveFrom;

		[Order(5uL)]
		public DataItem_UInt32 lcbPlcfBkfMoveFrom;

		[Order(6uL)]
		public DataItem_UInt32 fcPlcfBklMoveFrom;

		[Order(7uL)]
		public DataItem_UInt32 lcbPlcfBklMoveFrom;

		[Order(8uL)]
		public DataItem_UInt32 fcSttbfBkmkMoveTo;

		[Order(9uL)]
		public DataItem_UInt32 lcbSttbfBkmkMoveTo;

		[Order(10uL)]
		public DataItem_UInt32 fcPlcfBkfMoveTo;

		[Order(11uL)]
		public DataItem_UInt32 lcbPlcfBkfMoveTo;

		[Order(12uL)]
		public DataItem_UInt32 fcPlcfBklMoveTo;

		[Order(13uL)]
		public DataItem_UInt32 lcbPlcfBklMoveTo;

		[Order(14uL)]
		public DataItem_UInt32 fcUnused1;

		[Order(15uL)]
		public DataItem_UInt32 lcbUnused1;

		[Order(16uL)]
		public DataItem_UInt32 fcUnused2;

		[Order(17uL)]
		public DataItem_UInt32 lcbUnused2;

		[Order(18uL)]
		public DataItem_UInt32 fcUnused3;

		[Order(19uL)]
		public DataItem_UInt32 lcbUnused3;

		[Order(20uL)]
		public DataItem_UInt32 fcSttbfBkmkArto;

		[Order(21uL)]
		public DataItem_UInt32 lcbSttbfBkmkArto;

		[Order(22uL)]
		public DataItem_UInt32 fcPlcfBkfArto;

		[Order(23uL)]
		public DataItem_UInt32 lcbPlcfBkfArto;

		[Order(24uL)]
		public DataItem_UInt32 fcPlcfBklArto;

		[Order(25uL)]
		public DataItem_UInt32 lcbPlcfBklArto;

		[Order(26uL)]
		public DataItem_UInt32 fcArtoData;

		[Order(27uL)]
		public DataItem_UInt32 lcbArtoData;

		[Order(28uL)]
		public DataItem_UInt32 fcUnused4;

		[Order(29uL)]
		public DataItem_UInt32 lcbUnused4;

		[Order(30uL)]
		public DataItem_UInt32 fcUnused5;

		[Order(31uL)]
		public DataItem_UInt32 lcbUnused5;

		[Order(32uL)]
		public DataItem_UInt32 fcUnused6;

		[Order(33uL)]
		public DataItem_UInt32 lcbUnused6;

		[Order(34uL)]
		public DataItem_UInt32 fcOssTheme;

		[Order(35uL)]
		public DataItem_UInt32 lcbOssTheme;

		[Order(36uL)]
		public DataItem_UInt32 fcColorSchemeMapping;

		[Order(37uL)]
		public DataItem_UInt32 lcbColorSchemeMapping;

		public FIBRGFCLCB2007(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class FIBRGLW97 : DataStructure
	{
		[Order(0uL)]
		public DataItem_Int32 cbMac;

		[Order(1uL)]
		public DataItem_Int32 reserved1;

		[Order(2uL)]
		public DataItem_Int32 reserved2;

		[Order(3uL)]
		public DataItem_Int32 ccpText;

		[Order(4uL)]
		public DataItem_Int32 ccpFtn;

		[Order(5uL)]
		public DataItem_Int32 ccpHdd;

		[Order(6uL)]
		public DataItem_Int32 reserved3;

		[Order(7uL)]
		public DataItem_Int32 ccpAtn;

		[Order(8uL)]
		public DataItem_Int32 ccpEdn;

		[Order(9uL)]
		public DataItem_Int32 ccpTxbx;

		[Order(10uL)]
		public DataItem_Int32 ccpHdrTxbx;

		[Order(11uL)]
		public DataItem_Int32 reserved4;

		[Order(12uL)]
		public DataItem_Int32 reserved5;

		[Order(13uL)]
		public DataItem_Int32 reserved6;

		[Order(14uL)]
		public DataItem_Int32 reserved7;

		[Order(15uL)]
		public DataItem_Int32 reserved8;

		[Order(16uL)]
		public DataItem_Int32 reserved9;

		[Order(17uL)]
		public DataItem_Int32 reserved10;

		[Order(18uL)]
		public DataItem_Int32 reserved11;

		[Order(19uL)]
		public DataItem_Int32 reserved12;

		[Order(20uL)]
		public DataItem_Int32 reserved13;

		[Order(21uL)]
		public DataItem_Int32 reserved14;

		public FIBRGLW97(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class FIB : DataStructure
	{
		public FIBBASE FIBBase;

		public DataItem_UInt16 csw;

		public DataItem_ByteArray FibRgW97;

		public DataItem_UInt16 cslw;

		public FIBRGLW97 FibRgLw97;

		public DataItem_UInt16 cbRgFcLcb;

		public FIBRGFCLCB97 FIBTable97;

		public FIBRGFCLCB2000 FIBTable2000;

		public FIBRGFCLCB2002 FIBTable2002;

		public FIBRGFCLCB2003 FIBTable2003;

		public FIBRGFCLCB2007 FIBTable2007;

		public DataItem_UInt16 cswNew;

		public FIBRGCSWNEW FibRgCswNew;

		public FIB(DataInByteArray Data)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			FIBBase = new FIBBASE(Data);
			csw = new DataItem_UInt16(Data);
			FibRgW97 = new DataItem_ByteArray(Data, 28uL);
			cslw = new DataItem_UInt16(Data);
			FibRgLw97 = new FIBRGLW97(Data);
			cbRgFcLcb = new DataItem_UInt16(Data);
			ulong currentPosition = Data.CurrentPosition;
			if (!Data.Seek(Data.CurrentPosition + (ulong)((long)cbRgFcLcb.Value.Value * 8L)))
			{
				AddParsingNote(ParsingNoteType.Error, "EOF in FIB, because cbRgFcLcb was too big", Data.CurrentPosition);
				return;
			}
			cswNew = new DataItem_UInt16(Data);
			uint num = 0u;
			ushort? value = cswNew.Value;
			if (value.GetValueOrDefault() == 0 && value.HasValue)
			{
				num = FIBBase.nFib.Value.Value;
			}
			else
			{
				FibRgCswNew = new FIBRGCSWNEW(Data);
				num = FibRgCswNew.nFibNew.Value.Value;
			}
			Data.Seek(currentPosition);
			if (num != 193 && num != 217 && num != 257 && num != 268 && num != 274)
			{
				AddParsingNote(ParsingNoteType.Warning, "nFIB is an invalid value - matching as best we can", Data.CurrentPosition);
			}
			if (num < 193)
			{
				AddParsingNote(ParsingNoteType.Error, "nFIB is too low to interpret", Data.CurrentPosition);
			}
			if (num >= 193)
			{
				FIBTable97 = new FIBRGFCLCB97(Data);
			}
			if (num >= 217)
			{
				FIBTable2000 = new FIBRGFCLCB2000(Data);
			}
			if (num >= 257)
			{
				FIBTable2002 = new FIBRGFCLCB2002(Data);
			}
			if (num >= 268)
			{
				FIBTable2003 = new FIBRGFCLCB2003(Data);
			}
			if (num >= 274)
			{
				FIBTable2007 = new FIBRGFCLCB2007(Data);
			}
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class PLCTCH : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt32 Length;

		[Order(1uL)]
		public List<DataItem_UInt32> CPs;

		[Order(2uL)]
		public List<FTCH> TCHs;

		public PLCTCH(DataInByteArray Data, ulong lcbLength)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			ulong num = (lcbLength - 4) / 8;
			Length = new DataItem_UInt32(Data);
			CPs = new List<DataItem_UInt32>();
			ulong num2 = 0uL;
			while (Data.HasDataLeftToRead && num2 < num)
			{
				CPs.Add(new DataItem_UInt32(Data));
				num2++;
			}
			TCHs = new List<FTCH>();
			ulong num3 = 0uL;
			while (Data.HasDataLeftToRead && num3 < num)
			{
				TCHs.Add(new FTCH(Data));
				num3++;
			}
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class FTCH : DataStructure
	{
		[Order(0uL)]
		[BitFieldSize(1u)]
		[BeginBitField(4u)]
		public DataItem_UInt16 fUnk;

		[BitFieldSize(1u)]
		[Order(1uL)]
		public DataItem_UInt16 fTableChar;

		[Order(2uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fTtp;

		[Order(3uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fBegin;

		[Order(4uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fNoWrap;

		[BitFieldSize(1u)]
		[Order(5uL)]
		public DataItem_UInt16 fNextedLayout;

		[BitFieldSize(1u)]
		[Order(6uL)]
		public DataItem_UInt16 fFrameChange;

		[Order(7uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fPageViewFit;

		[Order(8uL)]
		[BitFieldSize(16u)]
		public DataItem_UInt16 itap;

		[BitFieldSize(1u)]
		[Order(9uL)]
		public DataItem_UInt16 fNeverBeenAutofit;

		[Order(10uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt16 fNestedTable;

		[Order(11uL)]
		[BitFieldSize(2u)]
		public DataItem_UInt16 fcss;

		[Order(12uL)]
		[BitFieldSize(4u)]
		public DataItem_UInt16 reserved;

		public FTCH(DataInByteArray Data)
			: base(Data)
		{
		}

		public override string ToString()
		{
			if (itap != null && ((int?)itap.Value).HasValue)
			{
				return $"itap: {itap.Value:X}";
			}
			return base.ToString();
		}
	}
	public class PNFKPCHPX : DataStructure
	{
		[Order(0uL)]
		[BitFieldSize(22u)]
		[BeginBitField(4u)]
		public DataItem_UInt32 pn;

		[Order(1uL)]
		[BitFieldSize(10u)]
		public DataItem_UInt32 unused;

		public PNFKPCHPX(DataInByteArray Data)
			: base(Data)
		{
		}

		public override string ToString()
		{
			if (pn != null && pn.Value.HasValue)
			{
				return pn.Value.ToString();
			}
			return "";
		}
	}
	public class PNFKPPAPX : DataStructure
	{
		[BitFieldSize(22u)]
		[BeginBitField(4u)]
		[Order(0uL)]
		public DataItem_UInt32 pn;

		[Order(1uL)]
		[BitFieldSize(10u)]
		public DataItem_UInt32 unused;

		public PNFKPPAPX(DataInByteArray Data)
			: base(Data)
		{
		}

		public override string ToString()
		{
			if (pn != null && pn.Value.HasValue)
			{
				return pn.Value.ToString();
			}
			return "";
		}
	}
	public class PLCBTECHPX : DataStructure
	{
		[Order(0uL)]
		public List<DataItem_UInt32> aFC;

		[Order(1uL)]
		public List<PNFKPCHPX> aPnBteChpx;

		public PLCBTECHPX(DataInByteArray Data, uint NumberOfItems)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			aFC = new List<DataItem_UInt32>();
			uint num = 0u;
			while (Data.HasDataLeftToRead && num < NumberOfItems + 1)
			{
				aFC.Add(new DataItem_UInt32(Data));
				num++;
			}
			aPnBteChpx = new List<PNFKPCHPX>();
			uint num2 = 0u;
			while (Data.HasDataLeftToRead && num2 < NumberOfItems)
			{
				aPnBteChpx.Add(new PNFKPCHPX(Data));
				num2++;
			}
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class PLCBTEPAPX : DataStructure
	{
		[Order(0uL)]
		public List<DataItem_UInt32> aFC;

		[Order(1uL)]
		public List<PNFKPPAPX> aPnBtePapx;

		public PLCBTEPAPX(DataInByteArray Data, uint NumberOfItems)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			aFC = new List<DataItem_UInt32>();
			uint num = 0u;
			while (Data.HasDataLeftToRead && num < NumberOfItems + 1)
			{
				aFC.Add(new DataItem_UInt32(Data));
				num++;
			}
			aPnBtePapx = new List<PNFKPPAPX>();
			uint num2 = 0u;
			while (Data.HasDataLeftToRead && num2 < NumberOfItems)
			{
				aPnBtePapx.Add(new PNFKPPAPX(Data));
				num2++;
			}
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class CHPXFKP : DataStructure
	{
		[Order(0uL)]
		public List<DataItem_UInt32> rgfc;

		[Order(1uL)]
		public List<DataItem_UInt8> rgb;

		[Order(2uL)]
		public List<CHPX> Chpxs;

		[Order(3uL)]
		public DataItem_UInt8 crun;

		public CHPXFKP(DataInByteArray Data)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			Data.Seek(Data.CurrentPosition + 511);
			crun = new DataItem_UInt8(Data);
			Data.Seek(base.DataStructureOffset);
			if (((int?)crun.Value).HasValue)
			{
				uint value = crun.Value.Value;
				rgfc = new List<DataItem_UInt32>();
				uint num = 0u;
				while (Data.HasDataLeftToRead && num < value + 1)
				{
					rgfc.Add(new DataItem_UInt32(Data));
					num++;
				}
				rgb = new List<DataItem_UInt8>();
				uint num2 = 0u;
				while (Data.HasDataLeftToRead && num2 < value)
				{
					rgb.Add(new DataItem_UInt8(Data));
					num2++;
				}
				Chpxs = new List<CHPX>();
				List<ulong> list = new List<ulong>();
				foreach (DataItem_UInt8 item in rgb)
				{
					if (!(item.Value > 0))
					{
						continue;
					}
					if (item.Value * 2 < 510)
					{
						ulong num3 = base.DataStructureOffset + (ulong)((long)item.Value.Value * 2L);
						if (!list.Contains(num3))
						{
							Data.Seek(num3);
							Chpxs.Add(new CHPX(Data));
							list.Add(num3);
						}
					}
					else
					{
						AddParsingNote(ParsingNoteType.Warning, "A CHPX offset within a CHPXFKP would have been outside of the 512 byte block (offset was " + item.Value * 2 + ")", base.DataStructureOffset, 512uL);
					}
				}
			}
			Data.Seek(base.DataStructureOffset + 512);
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class CHPX : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt8 cb;

		[Order(1uL)]
		public List<PRL> grpprl;

		public CHPX(DataInByteArray Data)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			cb = new DataItem_UInt8(Data);
			if (((int?)cb.Value).HasValue)
			{
				grpprl = new List<PRL>();
				while (Data.HasDataLeftToRead && Data.CurrentPosition < base.DataStructureOffset + cb.Length + cb.Value)
				{
					grpprl.Add(new PRL(Data));
				}
				ulong num = Data.CurrentPosition - base.DataStructureOffset - cb.Length;
				if (num != cb.Value)
				{
					AddParsingNote(ParsingNoteType.Warning, "A grpprl in a Chpx was parsed to be the wrong length (was " + num + " expected " + cb.Value + ")", base.DataStructureOffset);
				}
			}
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}

		public override string ToString()
		{
			if (grpprl != null)
			{
				string text = "";
				foreach (PRL item in grpprl)
				{
					if (item != null && item.sprm != null)
					{
						text = text + item.sprm.ToString() + ", ";
					}
				}
				if (text.EndsWith(", "))
				{
					text = text.Substring(0, text.Length - 2);
				}
				return text;
			}
			return "";
		}
	}
	public class BXPAP : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt8 bOffset;

		[ConstantLength(12uL)]
		[Order(1uL)]
		public List<DataItem_UInt8> reserved;

		public BXPAP(DataInByteArray Data)
			: base(Data)
		{
		}

		public override string ToString()
		{
			if (bOffset == null || !((int?)bOffset.Value).HasValue)
			{
				return null;
			}
			return "0x" + ((ushort)(bOffset.Value * 2).Value).ToString("X");
		}
	}
	public class PAPXFKP : DataStructure
	{
		[Order(0uL)]
		public List<DataItem_UInt32> rgfc;

		[Order(1uL)]
		public List<BXPAP> rgbx;

		[Order(2uL)]
		public List<PAPXINFKP> PapxInFkps;

		[Order(3uL)]
		public DataItem_UInt8 byLength;

		public PAPXFKP(DataInByteArray Data)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			Data.Seek(Data.CurrentPosition + 511);
			byLength = new DataItem_UInt8(Data);
			Data.Seek(base.DataStructureOffset);
			if (((int?)byLength.Value).HasValue)
			{
				uint value = byLength.Value.Value;
				rgfc = new List<DataItem_UInt32>((int)(value + 1));
				uint num = 0u;
				while (Data.HasDataLeftToRead && num < value + 1)
				{
					rgfc.Add(new DataItem_UInt32(Data));
					num++;
				}
				rgbx = new List<BXPAP>((int)value);
				uint num2 = 0u;
				while (Data.HasDataLeftToRead && num2 < value)
				{
					rgbx.Add(new BXPAP(Data));
					num2++;
				}
				PapxInFkps = new List<PAPXINFKP>((int)value);
				List<ulong> list = new List<ulong>();
				foreach (BXPAP item in rgbx)
				{
					if (item.bOffset == null || !((int?)item.bOffset.Value).HasValue)
					{
						continue;
					}
					byte? value2 = item.bOffset.Value;
					if (value2.GetValueOrDefault() == 0 && value2.HasValue)
					{
						continue;
					}
					if (item.bOffset.Value * 2 < 510)
					{
						ulong num3 = base.DataStructureOffset + (ulong)((long)item.bOffset.Value.Value * 2L);
						if (!list.Contains(num3))
						{
							Data.Seek(num3);
							PapxInFkps.Add(new PAPXINFKP(Data));
							list.Add(num3);
						}
					}
					else
					{
						AddParsingNote(ParsingNoteType.Warning, "A PAPXINFKP offset within a PAPXFKP would have been outside of the 512 byte block (offset was " + item.bOffset.Value * 2 + ")", base.DataStructureOffset, 512uL);
					}
				}
			}
			Data.Seek(base.DataStructureOffset + 512);
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}

		public override string ToString()
		{
			if (PapxInFkps != null)
			{
				string text = "";
				foreach (PAPXINFKP papxInFkp in PapxInFkps)
				{
					if (papxInFkp != null)
					{
						text = text + papxInFkp.ToString() + ", ";
					}
				}
				if (text.EndsWith(", "))
				{
					text = text.Substring(0, text.Length - 2);
				}
				return text;
			}
			return "";
		}
	}
	public class PAPXINFKP : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt8 cb;

		[Order(1uL)]
		public DataItem_UInt8 cb_prime;

		[Order(2uL)]
		public GRPPRLANDISTD grpprlInPapx;

		public PAPXINFKP(DataInByteArray Data)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			ulong num = 0uL;
			cb = new DataItem_UInt8(Data);
			if (((int?)cb.Value).HasValue)
			{
				byte? value = cb.Value;
				if (value.GetValueOrDefault() == 0 && value.HasValue)
				{
					cb_prime = new DataItem_UInt8(Data);
					if (((int?)cb_prime.Value).HasValue)
					{
						num = (ulong)cb_prime.Value.Value * 2uL;
					}
				}
				else
				{
					num = (ulong)((long)cb.Value.Value * 2L - 1);
				}
			}
			ulong currentPosition = Data.CurrentPosition;
			grpprlInPapx = new GRPPRLANDISTD(Data, num);
			ulong num2 = Data.CurrentPosition - currentPosition;
			if (num2 != num)
			{
				AddParsingNote(ParsingNoteType.Warning, "A grpprlInPapx in a PapxInFkp was parsed to be the wrong length (was " + num2 + " expected " + num + ")", base.DataStructureOffset);
			}
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}

		public override string ToString()
		{
			if (grpprlInPapx != null && grpprlInPapx.grpprl != null)
			{
				string text = "";
				foreach (PRL item in grpprlInPapx.grpprl)
				{
					if (item != null && item.sprm != null)
					{
						text = text + item.sprm.ToString() + ", ";
					}
				}
				if (text.EndsWith(", "))
				{
					text = text.Substring(0, text.Length - 2);
				}
				return text;
			}
			return "";
		}
	}
	public class GRPPRLANDISTD : DataStructure
	{
		[Order(0uL)]
		public DataItem_Int16 istd;

		[Order(1uL)]
		public List<PRL> grpprl;

		public GRPPRLANDISTD(DataInByteArray Data, ulong LengthInBytes)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			if (LengthInBytes >= 2)
			{
				istd = new DataItem_Int16(Data);
				grpprl = new List<PRL>();
				while (Data.HasDataLeftToRead && Data.CurrentPosition < base.DataStructureOffset + LengthInBytes)
				{
					grpprl.Add(new PRL(Data));
				}
			}
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class PAPX : DataStructure
	{
		[Order(0uL)]
		public DataItem_Int8 abyPad;

		[Order(1uL)]
		public DataItem_Int8 byLength;

		[Order(2uL)]
		public DataItem_UInt16 ISTD;

		public PAPX(DataInByteArray Data)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			byte? b = Data.PeekUInt8();
			if (b.GetValueOrDefault() == 0 && b.HasValue)
			{
				abyPad = new DataItem_Int8(Data);
			}
			byLength = new DataItem_Int8(Data);
			ISTD = new DataItem_UInt16(Data);
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class SPRMWithData : DataStructure
	{
		[Order(0uL)]
		public SPRM sprm;

		[Order(1uL)]
		public DataItem_ByteArray abyData;

		public SPRMWithData(DataInByteArray Data)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			sprm = new SPRM(Data);
			uint num = 0u;
			if (sprm != null && sprm.spra != null && ((int?)sprm.spra.Value).HasValue)
			{
				switch (sprm.spra.Value)
				{
				case 0:
				case 1:
					num = 1u;
					break;
				case 2:
				case 4:
				case 5:
					num = 2u;
					break;
				case 3:
					num = 4u;
					break;
				case 6:
					Data.Seek(Data.CurrentPosition - 2);
					switch (Data.ReadUInt16().Value)
					{
					case 54790:
					case 54792:
						num = (uint)(Data.PeekUInt16().Value + 1);
						break;
					default:
						num = (uint)(Data.PeekUInt8().Value + 1);
						break;
					}
					break;
				case 7:
					num = 3u;
					break;
				}
			}
			abyData = new DataItem_ByteArray(Data, num);
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}

		public override string ToString()
		{
			if (sprm == null || sprm.sgc == null || !((int?)sprm.sgc.Value).HasValue)
			{
				return null;
			}
			string result = "Unknown";
			switch (sprm.sgc.Value)
			{
			case 1:
				result = "PAP";
				break;
			case 2:
				result = "CHP";
				break;
			case 3:
				result = "PIC";
				break;
			case 4:
				result = "SEP";
				break;
			case 5:
				result = "TAP";
				break;
			case 7:
				result = "BAD";
				break;
			}
			return result;
		}
	}
	public class LVLFFlags : DataStructure
	{
		[BitFieldSize(2u)]
		[Order(0uL)]
		[BeginBitField(1u)]
		public DataItem_UInt8 jc;

		[BitFieldSize(1u)]
		[Order(1uL)]
		public DataItem_UInt8 fLegal;

		[Order(2uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fNoRestart;

		[BitFieldSize(1u)]
		[Order(3uL)]
		public DataItem_UInt8 fIndentSav;

		[Order(4uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fConverted;

		[BitFieldSize(1u)]
		[Order(5uL)]
		public DataItem_UInt8 unused1;

		[Order(6uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fTentative;

		public LVLFFlags(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class LVLF : DataStructure
	{
		[Order(0uL)]
		public DataItem_Int32 iStartAt;

		[Order(1uL)]
		public DataItem_UInt8 nfc;

		[Order(2uL)]
		public LVLFFlags Flags;

		[ConstantLength(9uL)]
		[Order(3uL)]
		public DataItem_ByteArray rgbxchNums;

		[Order(4uL)]
		public DataItem_UInt8 ixchFollow;

		[Order(5uL)]
		public DataItem_Int32 dxaIndentSav;

		[Order(6uL)]
		public DataItem_Int32 unused2;

		[Order(7uL)]
		public DataItem_UInt8 cbGrpprlChpx;

		[Order(8uL)]
		public DataItem_UInt8 cbGrpprlPapx;

		[Order(9uL)]
		public DataItem_UInt8 ilvlRestartLim;

		[Order(10uL)]
		public DataItem_Int8 grfhic;

		public LVLF(DataInByteArray Data)
			: base(Data)
		{
			if (iStartAt.Value > 32767 || iStartAt.Value < 0)
			{
				AddParsingNote(ParsingNoteType.Warning, "LVLF has an invalid iStartAt value (" + iStartAt.Value + ")", iStartAt.Offset, iStartAt.Length);
			}
			if (nfc.Value == 8 || nfc.Value == 9 || nfc.Value == 15 || nfc.Value == 19)
			{
				AddParsingNote(ParsingNoteType.Warning, "LVLF has an invalid nfc value (" + nfc.Value + ")", nfc.Offset, nfc.Length);
			}
			if (Flags.jc != null && Flags.jc.Value == 3)
			{
				AddParsingNote(ParsingNoteType.Warning, "LVLF has an invalid jc value (" + Flags.jc.Value + ")", Flags.jc.Offset, Flags.jc.Length);
			}
			bool flag = false;
			if (rgbxchNums == null || rgbxchNums.Value == null)
			{
				return;
			}
			for (int i = 1; i < 9; i++)
			{
				if (rgbxchNums.Value[i] != 0)
				{
					if (flag)
					{
						AddParsingNote(ParsingNoteType.Warning, "LVLF has an invalid rgbxchNums (data after 0)", rgbxchNums.Offset + (ulong)i, 1uL);
						break;
					}
					if (rgbxchNums.Value[i] <= rgbxchNums.Value[i - 1])
					{
						AddParsingNote(ParsingNoteType.Warning, "LVLF has an invalid rgbxchNums (values not unique and ascending)", rgbxchNums.Offset + (ulong)i, 1uL);
						break;
					}
				}
				else
				{
					flag = true;
				}
			}
		}
	}
	public class XST : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 cch;

		[Order(1uL)]
		public DataItem_UnicodeString rgtchar;

		public XST(DataInByteArray Data)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			cch = new DataItem_UInt16(Data);
			if (((int?)cch.Value).HasValue)
			{
				rgtchar = new DataItem_UnicodeString(Data, cch.Value.Value);
			}
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class LVL : DataStructure
	{
		[Order(0uL)]
		public LVLF lvlf;

		[Order(1uL)]
		public DataItem_UByteArray grpprlPapx;

		[Order(2uL)]
		public DataItem_UByteArray grpprlChpx;

		[Order(3uL)]
		public XST xst;

		public LVL(DataInByteArray Data)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			lvlf = new LVLF(Data);
			if (((int?)lvlf.cbGrpprlPapx.Value).HasValue && ((int?)lvlf.cbGrpprlChpx.Value).HasValue)
			{
				grpprlPapx = new DataItem_UByteArray(Data, lvlf.cbGrpprlPapx.Value.Value);
				grpprlChpx = new DataItem_UByteArray(Data, lvlf.cbGrpprlChpx.Value.Value);
				xst = new XST(Data);
				base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
			}
		}
	}
	public class LSTFFlags : DataStructure
	{
		[Order(0uL)]
		[BeginBitField(1u)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fSimpleList;

		[BitFieldSize(1u)]
		[Order(1uL)]
		public DataItem_UInt8 unused1;

		[BitFieldSize(1u)]
		[Order(2uL)]
		public DataItem_UInt8 fAutoNum;

		[Order(3uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 unused2;

		[Order(4uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fHybrid;

		[BitFieldSize(3u)]
		[Order(5uL)]
		public DataItem_UInt8 reserved1;

		public LSTFFlags(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class LSTF : DataStructure
	{
		[Order(0uL)]
		public DataItem_Int32 lsid;

		[Order(1uL)]
		public DataItem_UInt32 tplc;

		[Order(2uL)]
		[ConstantLength(9uL)]
		public List<DataItem_Int16> rgistPara;

		[Order(3uL)]
		public LSTFFlags Flags;

		[Order(4uL)]
		public DataItem_Int8 grfhic;

		public LSTF(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class PLFLST : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 cLst;

		[Order(1uL)]
		public List<LSTF> rgLstf;

		[Order(2uL)]
		public List<LVL> TrailingLVLs;

		public PLFLST(DataInByteArray Data)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			cLst = new DataItem_UInt16(Data);
			rgLstf = new List<LSTF>();
			ushort num = 0;
			while (true)
			{
				int num2 = num;
				ushort? value = cLst.Value;
				if (num2 >= value.GetValueOrDefault() || !value.HasValue || !Data.HasDataLeftToRead)
				{
					break;
				}
				rgLstf.Add(new LSTF(Data));
				num++;
			}
			ulong num3 = 0uL;
			for (ushort num4 = 0; num4 < rgLstf.Count; num4++)
			{
				if (rgLstf[num4] != null && rgLstf[num4].Flags != null && rgLstf[num4].Flags.fSimpleList != null && ((int?)rgLstf[num4].Flags.fSimpleList.Value).HasValue)
				{
					num3 = ((rgLstf[num4].Flags.fSimpleList.Value != 1) ? (num3 + 9) : (num3 + 1));
				}
			}
			TrailingLVLs = new List<LVL>();
			for (ulong num5 = 0uL; num5 < num3; num5++)
			{
				if (!Data.HasDataLeftToRead)
				{
					break;
				}
				TrailingLVLs.Add(new LVL(Data));
			}
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class LFO : DataStructure
	{
		[Order(0uL)]
		public DataItem_Int32 lsid;

		[Order(1uL)]
		public DataItem_UInt32 unused1;

		[Order(2uL)]
		public DataItem_UInt32 unused2;

		[Order(3uL)]
		public DataItem_UInt8 clfolvl;

		[Order(4uL)]
		public DataItem_UInt8 ibstFltAutoNum;

		[Order(5uL)]
		public DataItem_Int8 grjhic;

		[Order(6uL)]
		public DataItem_Int8 nused3;

		public LFO(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class LFOLVLFlags : DataStructure
	{
		[Order(0uL)]
		[BitFieldSize(4u)]
		[BeginBitField(4u)]
		public DataItem_UInt8 iLvl;

		[Order(1uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fStartAt;

		[BitFieldSize(1u)]
		[Order(2uL)]
		public DataItem_UInt8 fFormatting;

		[BitFieldSize(8u)]
		[Order(3uL)]
		public DataItem_UInt8 grfhic;

		[BitFieldSize(15u)]
		[Order(4uL)]
		public DataItem_UInt16 unused1;

		[Order(5uL)]
		[BitFieldSize(3u)]
		public DataItem_UInt16 unused2;

		public LFOLVLFlags(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class LFOLVL : DataStructure
	{
		[Order(0uL)]
		public DataItem_Int32 iStartAt;

		[Order(1uL)]
		public LFOLVLFlags Flags;

		[Order(2uL)]
		public LVL lvl;

		public LFOLVL(DataInByteArray Data)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			iStartAt = new DataItem_Int32(Data);
			Flags = new LFOLVLFlags(Data);
			byte? value = Flags.fFormatting.Value;
			if (value.GetValueOrDefault() != 0 || !value.HasValue)
			{
				lvl = new LVL(Data);
			}
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class LFODATA : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt32 cp;

		[Order(1uL)]
		public List<LFOLVL> rgLfoLvl;

		public LFODATA(DataInByteArray Data, uint LFOLVLCount)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			cp = new DataItem_UInt32(Data);
			rgLfoLvl = new List<LFOLVL>();
			for (uint num = 0u; num < LFOLVLCount; num++)
			{
				if (!Data.HasDataLeftToRead)
				{
					break;
				}
				rgLfoLvl.Add(new LFOLVL(Data));
			}
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class PLFLFO : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt32 lfoMac;

		[Order(1uL)]
		public List<LFO> rgLfo;

		[Order(2uL)]
		public List<LFODATA> rgLfoData;

		public PLFLFO(DataInByteArray Data)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			lfoMac = new DataItem_UInt32(Data);
			rgLfo = new List<LFO>();
			uint num = 0u;
			while (true)
			{
				uint num2 = num;
				uint? value = lfoMac.Value;
				if (num2 >= value.GetValueOrDefault() || !value.HasValue || !Data.HasDataLeftToRead)
				{
					break;
				}
				rgLfo.Add(new LFO(Data));
				num++;
			}
			rgLfoData = new List<LFODATA>();
			uint num3 = 0u;
			while (true)
			{
				uint num4 = num3;
				uint? value2 = lfoMac.Value;
				if (num4 >= value2.GetValueOrDefault() || !value2.HasValue || !Data.HasDataLeftToRead)
				{
					break;
				}
				rgLfoData.Add(new LFODATA(Data, rgLfo[(int)num3].clfolvl.Value.Value));
				num3++;
			}
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class SmartTag : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 WordCount;

		[Order(1uL)]
		public DataItem_UInt32 ID;

		[Order(2uL)]
		public DataItem_UInt16 Flags;

		[Order(3uL)]
		public DataItem_UInt16 Reserved;

		[Order(4uL)]
		public DataItem_UInt32 Pointer;

		[Order(5uL)]
		public DataItem_UByteArray Data;

		public SmartTag(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			WordCount = new DataItem_UInt16(DataToRead);
			ID = new DataItem_UInt32(DataToRead);
			Flags = new DataItem_UInt16(DataToRead);
			Reserved = new DataItem_UInt16(DataToRead);
			Pointer = new DataItem_UInt32(DataToRead);
			if (WordCount != null && ((int?)WordCount.Value).HasValue && WordCount.Value >= 6)
			{
				Data = new DataItem_UByteArray(DataToRead, (ulong)(WordCount.Value - 6).Value * 2uL);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class SmartTagSection : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 AlwaysFFFF;

		[Order(1uL)]
		public DataItem_UInt16 NumSmartTags;

		[Order(2uL)]
		public DataItem_UInt16 ExtraData;

		[Order(3uL)]
		public List<SmartTag> SmartTags;

		public SmartTagSection(DataInByteArray Data)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			AlwaysFFFF = new DataItem_UInt16(Data);
			NumSmartTags = new DataItem_UInt16(Data);
			ExtraData = new DataItem_UInt16(Data);
			if (NumSmartTags != null && ((int?)NumSmartTags.Value).HasValue)
			{
				SmartTags = new List<SmartTag>();
				ushort num = 0;
				while (num < NumSmartTags.Value.Value && Data.HasDataLeftToRead)
				{
					SmartTags.Add(new SmartTag(Data));
					num++;
				}
			}
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class FCCOMPRESSED : DataStructure
	{
		[Order(0uL)]
		[BeginBitField(4u)]
		[BitFieldSize(30u)]
		public DataItem_UInt32 fc;

		[BitFieldSize(1u)]
		[Order(1uL)]
		public DataItem_UInt8 fCompressed;

		[Order(2uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 r1;

		public FCCOMPRESSED(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class SPRM : DataStructure
	{
		[BeginBitField(2u)]
		[Order(0uL)]
		[BitFieldSize(9u)]
		public DataItem_UInt16 ispmd;

		[Order(1uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fSpec;

		[BitFieldSize(3u)]
		[Order(2uL)]
		public DataItem_UInt8 sgc;

		[Order(3uL)]
		[BitFieldSize(3u)]
		public DataItem_UInt8 spra;

		[DoNotAutoProcess]
		public WordSPRMCodes? SPRMCode = null;

		public SPRM(DataInByteArray DataToRead)
			: base(DataToRead)
		{
			ulong currentPosition = DataToRead.CurrentPosition;
			DataToRead.Seek(currentPosition - 2);
			SPRMCode = (WordSPRMCodes)DataToRead.ReadUInt16().Value;
			if (sgc != null && ((int?)sgc.Value).HasValue)
			{
				byte? value = sgc.Value;
				if ((value.GetValueOrDefault() == 0 && value.HasValue) || sgc.Value > 5)
				{
					AddParsingNote(ParsingNoteType.Warning, "Invalid sprm.sgc value found", sgc.Offset, sgc.Length);
				}
			}
		}

		public override string ToString()
		{
			if (SPRMCode.HasValue)
			{
				return SPRMCode.ToString();
			}
			return "";
		}
	}
	public class PRM : DataStructure
	{
		[BeginBitField(2u)]
		[Order(0uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fComplex;

		[BitFieldSize(15u)]
		[Order(1uL)]
		public DataItem_UInt16 data;

		public PRM(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class PLCPCD : DataStructure
	{
		[Order(0uL)]
		public List<DataItem_UInt32> aCP;

		[Order(1uL)]
		public List<PCD> aPcd;

		public PLCPCD(DataInByteArray DataToRead, ulong ExpectedLength)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			ulong num = (ExpectedLength - 4) / 12;
			if (num > 10000)
			{
				AddParsingNote(ParsingNoteType.Error, "A huge number of PCDs was requested in a PLCPCD (" + num + ") only processing first 10000", base.DataStructureOffset);
				num = 10000uL;
			}
			aCP = new List<DataItem_UInt32>();
			for (ulong num2 = 0uL; num2 < num + 1; num2++)
			{
				aCP.Add(new DataItem_UInt32(DataToRead));
			}
			aPcd = new List<PCD>();
			for (ulong num3 = 0uL; num3 < num; num3++)
			{
				aPcd.Add(new PCD(DataToRead));
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class PCDT : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt8 clxt;

		[Order(1uL)]
		public DataItem_UInt32 lcb;

		[Order(2uL)]
		public PLCPCD PlcPcd;

		public PCDT(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			clxt = new DataItem_UInt8(DataToRead);
			lcb = new DataItem_UInt32(DataToRead);
			if (lcb.Value.HasValue)
			{
				PlcPcd = new PLCPCD(DataToRead, lcb.Value.Value);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class PCD : DataStructure
	{
		[BeginBitField(2u)]
		[Order(0uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fNoParaLast;

		[Order(1uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fR1;

		[Order(2uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fDirty;

		[Order(3uL)]
		[BitFieldSize(13u)]
		public DataItem_UInt8 fR2;

		[Order(4uL)]
		public FCCOMPRESSED fc;

		[Order(5uL)]
		public PRM prm;

		public PCD(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class PCHGTABSOPERAND : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt8 cb;

		[Order(1uL)]
		public DataItem_UByteArray Operand;

		[Order(2uL)]
		public DataItem_UInt8 PChgTabsDelClose_CB;

		[Order(3uL)]
		public DataItem_UByteArray PChgTabsDelClose_Data;

		[Order(4uL)]
		public DataItem_UInt8 PChgTabsAdd_CB;

		[Order(5uL)]
		public DataItem_UByteArray PChgTabsAdd_Data;

		public PCHGTABSOPERAND(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			cb = new DataItem_UInt8(DataToRead);
			if (((int?)cb.Value).HasValue)
			{
				if (cb.Value != byte.MaxValue)
				{
					Operand = new DataItem_UByteArray(DataToRead, cb.Value.Value);
				}
				else
				{
					PChgTabsDelClose_CB = new DataItem_UInt8(DataToRead);
					if (((int?)PChgTabsDelClose_CB.Value).HasValue)
					{
						PChgTabsDelClose_Data = new DataItem_UByteArray(DataToRead, PChgTabsDelClose_CB.Value.Value);
					}
					PChgTabsAdd_CB = new DataItem_UInt8(DataToRead);
					if (((int?)PChgTabsAdd_CB.Value).HasValue)
					{
						PChgTabsAdd_Data = new DataItem_UByteArray(DataToRead, PChgTabsAdd_CB.Value.Value);
					}
				}
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class PCHGTABSDEL : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt8 cTabs;

		[Order(1uL)]
		public List<DataItem_UInt16> rgdxaDel;

		public PCHGTABSDEL(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}

		protected override void ParseData(DataInByteArray Data)
		{
			cTabs = new DataItem_UInt8(Data);
			if (((int?)cTabs.Value).HasValue)
			{
				rgdxaDel = new List<DataItem_UInt16>();
				for (int i = 0; i < cTabs.Value; i++)
				{
					rgdxaDel.Add(new DataItem_UInt16(Data));
				}
			}
		}
	}
	public class PCHGTABSADD : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt8 cTabs;

		[Order(1uL)]
		public List<DataItem_UInt16> rgdxaAdd;

		[Order(2uL)]
		public List<DataItem_UInt8> rgtbdAdd;

		public PCHGTABSADD(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}

		protected override void ParseData(DataInByteArray Data)
		{
			cTabs = new DataItem_UInt8(Data);
			if (((int?)cTabs.Value).HasValue)
			{
				rgdxaAdd = new List<DataItem_UInt16>();
				for (int i = 0; i < cTabs.Value; i++)
				{
					rgdxaAdd.Add(new DataItem_UInt16(Data));
				}
				rgtbdAdd = new List<DataItem_UInt8>();
				for (int j = 0; j < cTabs.Value; j++)
				{
					rgtbdAdd.Add(new DataItem_UInt8(Data));
				}
			}
		}
	}
	public class PCHGTABSPAPXOPERAND : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt8 cb;

		[Order(1uL)]
		public PCHGTABSDEL PChgTabsDel;

		[Order(2uL)]
		public PCHGTABSADD PChgTabsAdd;

		[Order(3uL)]
		[DoNotAutoProcess]
		public DataItem_UByteArray Slack;

		public PCHGTABSPAPXOPERAND(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}

		protected override void ParseData(DataInByteArray Data)
		{
			base.ParseData(Data);
			if (Data.CurrentPosition - base.DataStructureOffset < cb.Value)
			{
				Slack = new DataItem_UByteArray(Data, cb.Value.Value - (Data.CurrentPosition - base.DataStructureOffset));
			}
		}
	}
	public class PRL : DataStructure
	{
		[Order(0uL)]
		public SPRM sprm;

		[Order(1uL)]
		public DataItem_UInt8 OneByteSizeOfRestOfOperand;

		[Order(2uL)]
		public DataItem_UInt16 TwoByteSizeOfRestOfOperand;

		[Order(3uL)]
		public DataItem_UByteArray operand;

		[Order(4uL)]
		public PCHGTABSOPERAND PChgTabsOperand;

		[Order(5uL)]
		public PCHGTABSPAPXOPERAND PChgTabsPapxOperand;

		public PRL(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			ushort value = DataToRead.PeekUInt16().Value;
			sprm = new SPRM(DataToRead);
			if (value == ushort.MaxValue)
			{
				operand = new DataItem_UByteArray(DataToRead, 4uL);
			}
			else if (sprm.spra != null)
			{
				switch (sprm.spra.Value)
				{
				case 0:
					operand = new DataItem_UByteArray(DataToRead, 1uL);
					break;
				case 1:
					operand = new DataItem_UByteArray(DataToRead, 1uL);
					break;
				case 2:
					operand = new DataItem_UByteArray(DataToRead, 2uL);
					break;
				case 3:
					operand = new DataItem_UByteArray(DataToRead, 4uL);
					break;
				case 4:
					operand = new DataItem_UByteArray(DataToRead, 2uL);
					break;
				case 5:
					operand = new DataItem_UByteArray(DataToRead, 2uL);
					break;
				case 6:
					if (sprm.SPRMCode == WordSPRMCodes.sprmTDefTable)
					{
						TwoByteSizeOfRestOfOperand = new DataItem_UInt16(DataToRead);
						if (((int?)TwoByteSizeOfRestOfOperand.Value).HasValue)
						{
							if (TwoByteSizeOfRestOfOperand.Value > 0)
							{
								operand = new DataItem_UByteArray(DataToRead, (ulong)TwoByteSizeOfRestOfOperand.Value.Value - 1uL);
							}
							else
							{
								operand = new DataItem_UByteArray(DataToRead, 47uL);
							}
						}
					}
					else if (sprm.SPRMCode == WordSPRMCodes.sprmPChgTabs)
					{
						PChgTabsOperand = new PCHGTABSOPERAND(DataToRead);
					}
					else if (sprm.SPRMCode == WordSPRMCodes.sprmPChgTabsPapx)
					{
						PChgTabsPapxOperand = new PCHGTABSPAPXOPERAND(DataToRead);
					}
					else if (sprm.SPRMCode == WordSPRMCodes.sprmTSetShdTable)
					{
						OneByteSizeOfRestOfOperand = new DataItem_UInt8(DataToRead);
						if (((int?)OneByteSizeOfRestOfOperand.Value).HasValue)
						{
							operand = new DataItem_UByteArray(DataToRead, 10uL);
						}
					}
					else if (sprm.SPRMCode == WordSPRMCodes.sprmTSetBrc)
					{
						OneByteSizeOfRestOfOperand = new DataItem_UInt8(DataToRead);
						if (((int?)OneByteSizeOfRestOfOperand.Value).HasValue)
						{
							operand = new DataItem_UByteArray(DataToRead, 11uL);
						}
					}
					else if (sprm.SPRMCode == WordSPRMCodes.sprmSBrcBottom)
					{
						AddParsingNote(ParsingNoteType.Comment, "Found a sprmSBrcBottom", sprm.DataStructureOffset, 2uL);
						OneByteSizeOfRestOfOperand = new DataItem_UInt8(DataToRead);
						if (((int?)OneByteSizeOfRestOfOperand.Value).HasValue)
						{
							operand = new DataItem_UByteArray(DataToRead, 8uL);
						}
					}
					else if (sprm.SPRMCode == WordSPRMCodes.sprmTDefTableShd)
					{
						OneByteSizeOfRestOfOperand = new DataItem_UInt8(DataToRead);
						if (((int?)OneByteSizeOfRestOfOperand.Value).HasValue)
						{
							byte? value2 = OneByteSizeOfRestOfOperand.Value;
							ulong size = (ulong)((value2.GetValueOrDefault() != 0 || !value2.HasValue) ? OneByteSizeOfRestOfOperand.Value.Value : 10);
							operand = new DataItem_UByteArray(DataToRead, size);
						}
					}
					else if (sprm.SPRMCode == WordSPRMCodes.sprmTDefTableShdRaw)
					{
						OneByteSizeOfRestOfOperand = new DataItem_UInt8(DataToRead);
						if (((int?)OneByteSizeOfRestOfOperand.Value).HasValue)
						{
							byte? value3 = OneByteSizeOfRestOfOperand.Value;
							ulong size2 = (ulong)((value3.GetValueOrDefault() != 0 || !value3.HasValue) ? OneByteSizeOfRestOfOperand.Value.Value : 10);
							operand = new DataItem_UByteArray(DataToRead, size2);
						}
					}
					else
					{
						OneByteSizeOfRestOfOperand = new DataItem_UInt8(DataToRead);
						if (((int?)OneByteSizeOfRestOfOperand.Value).HasValue)
						{
							operand = new DataItem_UByteArray(DataToRead, OneByteSizeOfRestOfOperand.Value.Value);
						}
					}
					break;
				case 7:
					operand = new DataItem_UByteArray(DataToRead, 3uL);
					break;
				}
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}

		public override string ToString()
		{
			if (sprm != null)
			{
				return sprm.ToString();
			}
			return "";
		}
	}
	public class PRCDATA : DataStructure
	{
		[Order(0uL)]
		public DataItem_Int16 cbGrpprl;

		[Order(1uL)]
		public List<PRL> GrpPrl;

		public PRCDATA(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			cbGrpprl = new DataItem_Int16(DataToRead);
			if (((int?)cbGrpprl.Value).HasValue)
			{
				GrpPrl = new List<PRL>();
				while (DataToRead.HasDataLeftToRead && DataToRead.CurrentPosition < (ulong)((long)(base.DataStructureOffset + cbGrpprl.Length) + (long)cbGrpprl.Value.Value))
				{
					GrpPrl.Add(new PRL(DataToRead));
				}
				if (DataToRead.CurrentPosition != (ulong)((long)(base.DataStructureOffset + cbGrpprl.Length) + (long)cbGrpprl.Value.Value))
				{
					AddParsingNote(ParsingNoteType.Warning, "The total list of PRLs was bigger than expected - fixing CurrentPosition", base.DataStructureOffset + cbGrpprl.Length, DataToRead.CurrentPosition - (base.DataStructureOffset + cbGrpprl.Length));
					DataToRead.Seek(base.DataStructureOffset + cbGrpprl.Length + (ulong)cbGrpprl.Value.Value);
				}
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class PRC : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt8 clxt;

		[Order(1uL)]
		public PRCDATA data;

		public PRC(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class CLX : DataStructure
	{
		[Order(0uL)]
		public List<PRC> RgPrc;

		[Order(1uL)]
		public PCDT Pcdt;

		public CLX(DataInByteArray DataToRead, ulong MaxLength)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			RgPrc = new List<PRC>();
			while (DataToRead.HasDataLeftToRead && DataToRead.PeekUInt8() != 2 && DataToRead.CurrentPosition - base.DataStructureOffset <= MaxLength)
			{
				RgPrc.Add(new PRC(DataToRead));
			}
			if (DataToRead.CurrentPosition - base.DataStructureOffset > MaxLength)
			{
				AddParsingNote(ParsingNoteType.Warning, "The list of PRCs was longer than expected - moving CurrentPosition back", base.DataStructureOffset);
				DataToRead.Seek(base.DataStructureOffset + MaxLength);
			}
			else
			{
				Pcdt = new PCDT(DataToRead);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class GRPXST : DataStructure
	{
		[Order(0uL)]
		public List<XST> XSTs;

		public GRPXST(DataInByteArray DataToRead, ulong BytesToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			XSTs = new List<XST>();
			while (DataToRead.HasDataLeftToRead && DataToRead.CurrentPosition < base.DataStructureOffset + BytesToRead)
			{
				XSTs.Add(new XST(DataToRead));
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class PMS : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 Wpms;

		[Order(1uL)]
		public DataItem_UInt8 ipmfMF;

		[Order(2uL)]
		public DataItem_UInt8 ipmfFetch;

		[Order(3uL)]
		public DataItem_UInt32 iRecCur;

		[Order(4uL)]
		[ConstantLength(16uL)]
		public DataItem_UByteArray rgpmfs;

		[Order(5uL)]
		public DataItem_UInt32 rfs;

		[Order(6uL)]
		public DataItem_UInt16 cblszSqlStr;

		[Order(7uL)]
		public DataItem_UnicodeString lxszSqlStr;

		[Order(8uL)]
		public DataItem_UByteArray NotYetDeeplyParsed;

		public PMS(DataInByteArray DataToRead, ulong Length)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Wpms = new DataItem_UInt16(DataToRead);
			ipmfMF = new DataItem_UInt8(DataToRead);
			ipmfFetch = new DataItem_UInt8(DataToRead);
			iRecCur = new DataItem_UInt32(DataToRead);
			rgpmfs = new DataItem_UByteArray(DataToRead, 16uL);
			rfs = new DataItem_UInt32(DataToRead);
			cblszSqlStr = new DataItem_UInt16(DataToRead);
			if (((int?)cblszSqlStr.Value).HasValue)
			{
				lxszSqlStr = new DataItem_UnicodeString(DataToRead, (ulong)cblszSqlStr.Value.Value / 2uL);
			}
			ulong num = DataToRead.CurrentPosition - base.DataStructureOffset;
			if (num < Length)
			{
				NotYetDeeplyParsed = new DataItem_UByteArray(DataToRead, Length - num);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class PLCFSED : DataStructure
	{
		[Order(0uL)]
		public List<DataItem_UInt32> aCP;

		[Order(1uL)]
		public List<SED> aSED;

		public PLCFSED(DataInByteArray DataToRead, ulong SizeInBytes)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			ulong num = (SizeInBytes - 4) / 16;
			ulong num2 = num + 1;
			if (num2 >= 268435456)
			{
				AddParsingNote(ParsingNoteType.Error, "aCPCount was >= 0x10000000, parsing can not continue");
				return;
			}
			aCP = new List<DataItem_UInt32>((int)num2);
			int num3 = 0;
			while (DataToRead.HasDataLeftToRead && num3 < (int)num2)
			{
				aCP.Add(new DataItem_UInt32(DataToRead));
				num3++;
			}
			aSED = new List<SED>((int)num);
			int num4 = 0;
			while (DataToRead.HasDataLeftToRead && num4 < (int)num)
			{
				aSED.Add(new SED(DataToRead));
				num4++;
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class SED : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 fn;

		[Order(1uL)]
		public DataItem_Int32 fcSepx;

		[Order(2uL)]
		public DataItem_UInt16 fnMpr;

		[Order(3uL)]
		public DataItem_UInt32 fcMpr;

		public SED(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class SEPX : DataStructure
	{
		[Order(0uL)]
		public DataItem_Int16 cb;

		[Order(1uL)]
		public List<PRL> grpprl;

		public SEPX(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			cb = new DataItem_Int16(DataToRead);
			if (((int?)cb.Value).HasValue)
			{
				if (cb.Value < 0)
				{
					AddParsingNote(ParsingNoteType.Warning, "The cb in a SEPX was < 0", cb.Offset, cb.Length);
				}
				else
				{
					grpprl = new List<PRL>();
					while (DataToRead.HasDataLeftToRead && DataToRead.CurrentPosition - (base.DataStructureOffset + cb.Length) < (ulong)cb.Value.Value)
					{
						grpprl.Add(new PRL(DataToRead));
					}
					if (DataToRead.CurrentPosition != (ulong)((long)(base.DataStructureOffset + cb.Length) + (long)cb.Value.Value))
					{
						AddParsingNote(ParsingNoteType.Warning, "The group of PRLs in a SEPX were longer than expected, moving the CurrentPosition back into place", base.DataStructureOffset);
						DataToRead.Seek(base.DataStructureOffset + cb.Length + (ulong)cb.Value.Value);
					}
				}
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class WordBinaryDocument : DataStructure
	{
		public FIB WordFIB;

		public OLESSDirectoryEntry WordDocumentStream;

		public OLESSDirectoryEntry ZeroTableDocumentStream;

		public OLESSDirectoryEntry OneTableDocumentStream;

		public OLESSDirectoryEntry DataStream;

		public PLFLST PLFLSTInTableStream;

		public PLFLFO PLFLFOInTableStream;

		public SmartTagSection SmartTagSectionInTableStream;

		public DataItem_UInt32 fcMin_Undo;

		public DataItem_UInt32 Real_Undo;

		public CLX Clx;

		public DataItem_UByteArray SttbfAssoc;

		public PLCBTECHPX stChpxBte;

		public PLCBTEPAPX stPapxBte;

		public List<CHPXFKP> stChpxFKPs;

		public List<PAPXFKP> stPapxFKPs;

		public PLCTCH PlcfTch;

		public GRPXST GrpXstAtnOwners;

		public PMS Pms;

		public PLCFSED PlcfSed;

		public List<SEPX> Sepxs;

		public List<PRCDATA> PrcData;

		public WordBinaryDocument(DataInByteArray Data, OLESSDataFile OLESSRoot, OLESSDirectoryEntry TheWordDocumentStream)
		{
			WordDocumentStream = TheWordDocumentStream;
			_ = DateTime.Now;
			Data.Seek(WordDocumentStream.Data.Offset);
			WordFIB = new FIB(Data);
			foreach (OLESSDirectoryEntry directoryEntry in OLESSRoot.DirectoryEntries)
			{
				if (directoryEntry.FullPath.Replace("\0", "") == WordDocumentStream.FullPath.Replace("\0", ""))
				{
					if (string.Compare(directoryEntry.EleName.Value.Replace("\0", ""), "0Table", ignoreCase: true) == 0)
					{
						ZeroTableDocumentStream = directoryEntry;
					}
					else if (string.Compare(directoryEntry.EleName.Value.Replace("\0", ""), "1Table", ignoreCase: true) == 0)
					{
						OneTableDocumentStream = directoryEntry;
					}
					else if (string.Compare(directoryEntry.EleName.Value.Replace("\0", ""), "Data", ignoreCase: true) == 0)
					{
						DataStream = directoryEntry;
					}
				}
			}
			if (GetActiveTableStream() == null)
			{
				AddParsingNote(ParsingNoteType.Error, "Could not find the table stream called for by the FIB", WordDocumentStream.Data.Offset);
				return;
			}
			_ = DateTime.Now;
			if (WordFIB.FIBTable97 != null)
			{
				uint? value = WordFIB.FIBTable97.lcbPlfLst.Value;
				if (value.GetValueOrDefault() != 0 || !value.HasValue)
				{
					Data.Seek(GetActiveTableStream().Data.Offset + WordFIB.FIBTable97.fcPlfLst.Value.Value);
					PLFLSTInTableStream = new PLFLST(Data);
				}
			}
			_ = DateTime.Now;
			if (WordFIB.FIBTable97 != null)
			{
				uint? value2 = WordFIB.FIBTable97.lcbPlfLfo.Value;
				if (value2.GetValueOrDefault() != 0 || !value2.HasValue)
				{
					Data.Seek(GetActiveTableStream().Data.Offset + WordFIB.FIBTable97.fcPlfLfo.Value.Value);
					PLFLFOInTableStream = new PLFLFO(Data);
				}
			}
			_ = DateTime.Now;
			if (WordFIB.FIBTable2002 != null)
			{
				uint? value3 = WordFIB.FIBTable2002.lcbSttbfBkmkFactoid.Value;
				if (value3.GetValueOrDefault() != 0 || !value3.HasValue)
				{
					Data.Seek(GetActiveTableStream().Data.Offset + WordFIB.FIBTable2002.fcSttbfBkmkFactoid.Value.Value);
					SmartTagSectionInTableStream = new SmartTagSection(Data);
				}
			}
			_ = DateTime.Now;
			if (WordFIB.FIBTable97 != null && WordFIB.FIBTable97.lcbDocUndoWord9 != null)
			{
				uint? value4 = WordFIB.FIBTable97.lcbDocUndoWord9.Value;
				if (value4.GetValueOrDefault() != 0 && value4.HasValue)
				{
					Real_Undo = new DataItem_UInt32(WordFIB.FIBTable97.fcDocUndoWord9.Value & 0xFFFFFFE0u, WordFIB.FIBTable97.fcDocUndoWord9.Offset, WordFIB.FIBTable97.fcDocUndoWord9.Length);
					if (Real_Undo.Value + 24 < WordDocumentStream.Data.Length)
					{
						Data.Seek(WordDocumentStream.Data.Offset + Real_Undo.Value.Value + 24);
						fcMin_Undo = new DataItem_UInt32(Data);
					}
				}
			}
			_ = DateTime.Now;
			if (WordFIB.FIBTable97 != null && WordFIB.FIBTable97.fcClx != null && WordFIB.FIBTable97.lcbClx != null)
			{
				uint? value5 = WordFIB.FIBTable97.lcbClx.Value;
				if (value5.GetValueOrDefault() != 0 && value5.HasValue)
				{
					Data.Seek(GetActiveTableStream().Data.Offset + WordFIB.FIBTable97.fcClx.Value.Value);
					Clx = new CLX(Data, WordFIB.FIBTable97.lcbClx.Value.Value);
				}
			}
			_ = DateTime.Now;
			if (WordFIB.FIBTable97 != null && WordFIB.FIBTable97.fcSttbfAssoc != null && WordFIB.FIBTable97.lcbSttbfAssoc != null)
			{
				uint? value6 = WordFIB.FIBTable97.lcbSttbfAssoc.Value;
				if (value6.GetValueOrDefault() != 0 && value6.HasValue)
				{
					Data.Seek(GetActiveTableStream().Data.Offset + WordFIB.FIBTable97.fcSttbfAssoc.Value.Value);
					SttbfAssoc = new DataItem_UByteArray(Data, WordFIB.FIBTable97.lcbSttbfAssoc.Value.Value);
				}
			}
			_ = DateTime.Now;
			if (WordFIB.FIBTable97 != null && WordFIB.FIBTable97.fcPlcfBteChpx != null && WordFIB.FIBTable97.lcbPlcfBteChpx != null && WordFIB.FIBTable97.lcbPlcfBteChpx.Value >= 4)
			{
				Data.Seek(GetActiveTableStream().Data.Offset + WordFIB.FIBTable97.fcPlcfBteChpx.Value.Value);
				stChpxBte = new PLCBTECHPX(Data, (WordFIB.FIBTable97.lcbPlcfBteChpx.Value.Value - 4) / 8);
				if (stChpxBte.aPnBteChpx != null)
				{
					stChpxFKPs = new List<CHPXFKP>();
					foreach (PNFKPCHPX item in stChpxBte.aPnBteChpx)
					{
						if (item == null || item.pn == null || !item.pn.Value.HasValue)
						{
							continue;
						}
						Data.Seek(WordDocumentStream.Data.Offset + (ulong)(512L * (long)item.pn.Value.Value));
						CHPXFKP cHPXFKP = new CHPXFKP(Data);
						stChpxFKPs.Add(cHPXFKP);
						if (DataStream == null || cHPXFKP.Chpxs == null)
						{
							continue;
						}
						foreach (CHPX chpx in cHPXFKP.Chpxs)
						{
							if (chpx == null || chpx.grpprl == null)
							{
								continue;
							}
							foreach (PRL item2 in chpx.grpprl)
							{
								if (item2 != null && item2.sprm != null && (item2.sprm.SPRMCode == WordSPRMCodes.sprmPHugePapx || item2.sprm.SPRMCode == WordSPRMCodes.sprmPTableProps))
								{
									if (!Data.Seek(DataStream.Data.Offset + BitConverter.ToUInt32(item2.operand.Value, 0)))
									{
										AddParsingNote(ParsingNoteType.Error, "A PRL in the stChpxFKP list contained an operand which would have caused us to seek out of bounds to read a PRCDATA", item2.operand.Offset, item2.operand.Length);
									}
									else
									{
										PrcData.Add(new PRCDATA(Data));
									}
								}
							}
						}
					}
				}
			}
			_ = DateTime.Now;
			PrcData = new List<PRCDATA>();
			if (WordFIB.FIBTable97 != null && WordFIB.FIBTable97.fcPlcfBtePapx != null && WordFIB.FIBTable97.lcbPlcfBtePapx != null && WordFIB.FIBTable97.lcbPlcfBtePapx.Value >= 4)
			{
				Data.Seek(GetActiveTableStream().Data.Offset + WordFIB.FIBTable97.fcPlcfBtePapx.Value.Value);
				stPapxBte = new PLCBTEPAPX(Data, (WordFIB.FIBTable97.lcbPlcfBtePapx.Value.Value - 4) / 8);
				_ = DateTime.Now;
				if (stPapxBte.aPnBtePapx != null)
				{
					stPapxFKPs = new List<PAPXFKP>();
					foreach (PNFKPPAPX item3 in stPapxBte.aPnBtePapx)
					{
						if (item3 == null || item3.pn == null || !item3.pn.Value.HasValue)
						{
							continue;
						}
						Data.Seek(WordDocumentStream.Data.Offset + (ulong)(512L * (long)item3.pn.Value.Value));
						PAPXFKP pAPXFKP = new PAPXFKP(Data);
						stPapxFKPs.Add(pAPXFKP);
						if (DataStream == null || pAPXFKP.PapxInFkps == null)
						{
							continue;
						}
						foreach (PAPXINFKP papxInFkp in pAPXFKP.PapxInFkps)
						{
							if (papxInFkp == null || papxInFkp.grpprlInPapx == null || papxInFkp.grpprlInPapx.grpprl == null)
							{
								continue;
							}
							foreach (PRL item4 in papxInFkp.grpprlInPapx.grpprl)
							{
								if (item4 != null && item4.sprm != null && (item4.sprm.SPRMCode == WordSPRMCodes.sprmPHugePapx || item4.sprm.SPRMCode == WordSPRMCodes.sprmPTableProps))
								{
									if (!Data.Seek(DataStream.Data.Offset + BitConverter.ToUInt32(item4.operand.Value, 0)))
									{
										AddParsingNote(ParsingNoteType.Error, "A PRL in the stPapxFKP list contained an operand which would have caused us to seek out of bounds to read a PRCDATA", item4.operand.Offset, item4.operand.Length);
									}
									else
									{
										PrcData.Add(new PRCDATA(Data));
									}
								}
							}
						}
					}
				}
			}
			_ = DateTime.Now;
			if (WordFIB.FIBTable2000 != null && WordFIB.FIBTable2000.fcPlcfTch != null && WordFIB.FIBTable2000.lcbPlcfTch != null)
			{
				uint? value7 = WordFIB.FIBTable2000.lcbPlcfTch.Value;
				if (value7.GetValueOrDefault() != 0 || !value7.HasValue)
				{
					Data.Seek(GetActiveTableStream().Data.Offset + WordFIB.FIBTable2000.fcPlcfTch.Value.Value);
					PlcfTch = new PLCTCH(Data, WordFIB.FIBTable2000.lcbPlcfTch.Value.Value);
				}
			}
			_ = DateTime.Now;
			if (WordFIB.FIBTable97 != null && WordFIB.FIBTable97.fcGrpXstAtnOwners != null && WordFIB.FIBTable97.lcbGrpXstAtnOwners != null)
			{
				uint? value8 = WordFIB.FIBTable97.lcbGrpXstAtnOwners.Value;
				if (value8.GetValueOrDefault() != 0 || !value8.HasValue)
				{
					Data.Seek(GetActiveTableStream().Data.Offset + WordFIB.FIBTable97.fcGrpXstAtnOwners.Value.Value);
					GrpXstAtnOwners = new GRPXST(Data, WordFIB.FIBTable97.lcbGrpXstAtnOwners.Value.Value);
				}
			}
			_ = DateTime.Now;
			if (WordFIB.FIBTable2002 != null && WordFIB.FIBTable2002.fcPmsNew != null && WordFIB.FIBTable2002.fcPmsNew != null && WordFIB.FIBTable2002.lcbPmsNew != null && WordFIB.FIBTable2002.lcbPmsNew != null)
			{
				uint? value9 = WordFIB.FIBTable2002.lcbPmsNew.Value;
				if (value9.GetValueOrDefault() != 0 || !value9.HasValue)
				{
					Data.Seek(GetActiveTableStream().Data.Offset + WordFIB.FIBTable2002.fcPmsNew.Value.Value);
					Pms = new PMS(Data, WordFIB.FIBTable2002.lcbPmsNew.Value.Value);
					goto IL_0fa1;
				}
			}
			if (WordFIB.FIBTable97 != null && WordFIB.FIBTable97.fcPms != null && WordFIB.FIBTable97.fcPms != null && WordFIB.FIBTable97.lcbPms != null && WordFIB.FIBTable97.lcbPms != null)
			{
				uint? value10 = WordFIB.FIBTable97.lcbPms.Value;
				if (value10.GetValueOrDefault() != 0 || !value10.HasValue)
				{
					Data.Seek(GetActiveTableStream().Data.Offset + WordFIB.FIBTable97.fcPms.Value.Value);
					Pms = new PMS(Data, WordFIB.FIBTable97.lcbPms.Value.Value);
				}
			}
			goto IL_0fa1;
			IL_0fa1:
			_ = DateTime.Now;
			if (WordFIB.FIBTable97 != null && WordFIB.FIBTable97.fcPlcfSed != null && WordFIB.FIBTable97.fcPlcfSed.Value.HasValue && WordFIB.FIBTable97.lcbPlcfSed != null && WordFIB.FIBTable97.lcbPlcfSed.Value.HasValue)
			{
				uint? value = WordFIB.FIBTable97.lcbPlcfSed.Value;
				if (value.GetValueOrDefault() != 0 || !value.HasValue)
				{
					Data.Seek(GetActiveTableStream().Data.Offset + WordFIB.FIBTable97.fcPlcfSed.Value.Value);
					PlcfSed = new PLCFSED(Data, WordFIB.FIBTable97.lcbPlcfSed.Value.Value);
				}
			}
			_ = DateTime.Now;
			if (PlcfSed == null || WordFIB.FIBTable97 == null || PlcfSed.aSED == null)
			{
				return;
			}
			Sepxs = new List<SEPX>();
			foreach (SED item5 in PlcfSed.aSED)
			{
				if (item5 != null && item5.fcSepx != null && item5.fcSepx.Value.HasValue)
				{
					Data.Seek(WordDocumentStream.Data.Offset + (ulong)item5.fcSepx.Value.Value);
					Sepxs.Add(new SEPX(Data));
				}
			}
		}

		public OLESSDirectoryEntry GetActiveTableStream()
		{
			if (WordFIB == null || WordFIB.FIBBase == null || WordFIB.FIBBase.fWhichTblStm == null || !((int?)WordFIB.FIBBase.fWhichTblStm.Value).HasValue)
			{
				throw new ParsingFailed("Tried to calculate the active table stream, but the FIB was not yet fulled parsed");
			}
			return (uint)WordFIB.FIBBase.fWhichTblStm.Value.Value switch
			{
				0u => ZeroTableDocumentStream, 
				1u => OneTableDocumentStream, 
				_ => null, 
			};
		}
	}
}
namespace GUT.DataFormats.PowerPoint97_2003BinaryFormat
{
	public enum PowerPointRecordType : ushort
	{
		PST_Document = 1000,
		PST_Slide = 1006,
		PST_Notes = 1008,
		PST_Environment = 1010,
		PST_Scheme = 1012,
		PST_DocViewInfo = 1014,
		PST_MainMaster = 1016,
		PST_SlideViewInfo = 1018,
		PST_ViewInfo = 1020,
		PST_VBAInfo = 1023,
		PST_Summary = 1026,
		PST_DocRoutingSlip = 1030,
		PST_OutlineViewInfo = 1031,
		PST_SorterViewInfo = 1032,
		PST_ExObjList = 1033,
		PST_PPDrawingGroup = 1035,
		PST_PPDrawing = 1036,
		PST_NamedShows = 1040,
		PST_NotesTextViewInfo = 1043,
		PST_NormalViewSetInfo = 1044,
		PST_RoundTripCustomTableStyles12 = 1064,
		PST_List = 2000,
		PST_FontCollection = 2005,
		PST_FontCollection10 = 2006,
		PST_BookmarkCollection = 2019,
		PST_SoundCollection = 2020,
		PST_Sound = 2022,
		PST_RunArray = 2028,
		PST_BlipCollection = 2040,
		PST_OEPlaceHolderAtom = 3011,
		PST_GScaling = 3032,
		PST_TextHeaderAtom = 3999,
		PST_TxMasterStyleAtom = 4003,
		PST_TextBytesAtom = 4008,
		PST_OutlineTextProps9 = 4014,
		PST_TxMasterStyle10Atom = 4018,
		PST_OutlineTextProps10 = 4019,
		PST_OutlineTextProps11 = 4021,
		PST_TypeFace = 4025,
		PST_MetaFile = 4033,
		PST_ExOleObj = 4034,
		PST_SrKinsoku = 4040,
		PST_Handout = 4041,
		PST_ExEmbed = 4044,
		PST_ExLink = 4046,
		PST_ExPlain = 4053,
		PST_ExHyperlink = 4055,
		PST_HeadersFooters = 4057,
		PST_ExHyperlink9 = 4068,
		PST_ExQuickTimeMovie = 4074,
		PST_ExSubscription = 4076,
		PST_ExSubscriptionSection = 4077,
		PST_ExControl = 4078,
		PST_SlideListWithText = 4080,
		PST_InteractiveInfo = 4082,
		PST_SlideList = 4084,
		PST_ExVideo = 4101,
		PST_ExAviMovie = 4102,
		PST_ExMCIMovie = 4103,
		PST_ExMIDIAudio = 4109,
		PST_ExCDAudio = 4110,
		PST_ExWAVAudioEmbedded = 4111,
		PST_ExWAVAudioLink = 4112,
		PST_AnimationInfo = 4116,
		PST_ExHyperlinkFlags = 4120,
		PST_ProgTags = 5000,
		PST_ProgStringTag = 5001,
		PST_ProgBinaryTag = 5002,
		PST_BinaryTagData = 5003,
		PST_PrintOptions = 6000,
		PST_MacPrinterSettings = 6003,
		PST_PresAdvisorFlags9 = 6010,
		PST_HTMLPublishInfo9 = 6013,
		PST_BroadcastDocInfo9 = 6014,
		PST_TreeNode = 11004,
		PST_BeginList = 11005,
		PST_EndList = 11006,
		PST_ModifierList = 11007,
		PST_BuildList = 11010,
		PST_ChartBuild = 11012,
		PST_DiagramBuild = 11014,
		PST_ParaBuild = 11016,
		PST_Comment10 = 12000,
		PST_CommentIndex10 = 12004,
		PST_SlideFlags10 = 12010,
		PST_DiffTree10 = 12012,
		PST_Diff10 = 12013,
		PST_SlideListTableSize = 12015,
		PST_SlideListTable10 = 12017,
		PST_OEShapeHighPrecisionAnchor = 12018,
		PST_DocumentEncryptionData = 12050,
		PST_MsoCryptSession = 12052,
		PST_FontEmbedFlags10 = 13000,
		PST_FilterPrivacyFlags10 = 14000,
		PST_SmartTagStore11 = 14003,
		PST_SmartTagAtom11 = 14004,
		PST_DrawingGroupContainer = 61440,
		PST_DrawingContainer = 61442,
		PST_GroupShapeContainer = 61443,
		PST_ShapeContainer = 61444,
		PST_DrawingGroupAtom = 61446,
		PST_MSOfbtBSE = 61447,
		PST_OfficeArtFDG = 61448,
		PST_OfficeArtFSPGR = 61449,
		PST_OfficeArtFSP = 61450,
		PST_MSOShapeAtom = 61450,
		PST_MSOfbtOPT = 61451,
		PST_ClientTextBox = 61453,
		PST_MSOfbtTextbox = 61455,
		PST_ShapeClientContainer = 61457,
		PST_MSOfbtSecondaryOPT = 61729,
		PST_MSOfbtTertiaryOPT = 61730,
		PST_DocumentAtom = 1001,
		PST_EndDocumentAtom = 1002,
		PST_SlideAtom = 1007,
		PST_NotesAtom = 1009,
		PST_SlidePersistAtom = 1011,
		PST_SlideShowSlideInfoAtom = 1017,
		PST_GuideAtom = 1019,
		PST_ViewInfoAtom = 1021,
		PST_SlideViewInfoAtom = 1022,
		PST_VbaInfoAtom = 1024,
		PST_SlideShowDocInfoAtom = 1025,
		PST_ExternalObjectListAtom = 1034,
		PST_GridSpacing10Atom = 1037,
		PST_RoundTripTheme12Atom = 1038,
		PST_RoundTripColorMapping12Atom = 1039,
		PST_NamedShow = 1041,
		PST_NamedShowSlidesAtom = 1042,
		PST_NormalViewSetInfo9Atom = 1045,
		PST_RoundTripOriginalMainMasterId12Atom = 1052,
		PST_RoundTripCompositeMasterId12Atom = 1053,
		PST_RoundTripContentMasterInfo12Atom = 1054,
		PST_RoundTripShapeId12Atom = 1055,
		PST_RoundTripHFPlaceholder12Atom = 1056,
		PST_RoundTripContentMasterId12Atom = 1058,
		PST_RoundTripOArtTextStyles12Atom = 1059,
		PST_RoundTripHeaderFooterDefaults12Atom = 1060,
		PST_RoundTripDocFlags12Atom = 1061,
		PST_RoundTripShapeCheckSumForCL12Atom = 1062,
		PST_RoundTripNotesMasterTextStyles12Atom = 1063,
		PST_SoundCollectionAtom = 2021,
		PST_SoundDataBlob = 2023,
		PST_BookmarkSeedAtom = 2025,
		PST_ColorSchemeAtom = 2032,
		PST_BlipEntity9Atom = 2041,
		PST_ExternalObjectRefAtom = 3009,
		PST_PlaceholderAtom = 3011,
		PST_ShapeAtom = 3035,
		PST_ShapeFlags10Atom = 3036,
		PST_RoundTripNewPlaceholderId12Atom = 3037,
		PST_OutlineTextRefAtom = 3998,
		PST_TextCharsAtom = 4000,
		PST_StyleTextPropAtom = 4001,
		PST_MasterTextPropAtom = 4002,
		PST_TextCharFormatExceptionAtom = 4004,
		PST_TextParagraphFormatExceptionAtom = 4005,
		PST_TextRulerAtom = 4006,
		PST_TextBookmarkAtom = 4007,
		PST_TextSpecialInfoDefaultAtom = 4009,
		PST_TextSpecialInfoAtom = 4010,
		PST_DefaultRulerAtom = 4011,
		PST_StyleTextProp9Atom = 4012,
		PST_TextMasterStyle9Atom = 4013,
		PST_OutlineTextPropsHeader9Atom = 4015,
		PST_TextDefaults9Atom = 4016,
		PST_StyleTextProp10Atom = 4017,
		PST_TextDefaults10Atom = 4020,
		PST_StyleTextProp11Atom = 4022,
		PST_FontEntityAtom = 4023,
		PST_FontEmbedDataBlob = 4024,
		PST_CString = 4026,
		PST_ExternalOleObjectAtom = 4035,
		PST_ExternalOleEmbedAtom = 4045,
		PST_BookmarkEntityAtom = 4048,
		PST_ExternalOleLinkAtom = 4049,
		PST_KinsokuAtom = 4050,
		PST_ExternalHyperlinkAtom = 4051,
		PST_SlideNumberMetaCharAtom = 4056,
		PST_HeadersFootersAtom = 4058,
		PST_TextInteractiveInfoAtom = 4063,
		PST_RecolorInfoAtom = 4071,
		PST_AnimationInfoAtom = 4081,
		PST_InteractiveInfoAtom = 4083,
		PST_UserEditAtom = 4085,
		PST_CurrentUserAtom = 4086,
		PST_DateTimeMetaCharAtom = 4087,
		PST_GenericDateMetaCharAtom = 4088,
		PST_HeaderMetaCharAtom = 4089,
		PST_FooterMetaCharAtom = 4090,
		PST_ExternalOleControlAtom = 4091,
		PST_ExternalMediaAtom = 4100,
		PST_ExternalOleObjectStg = 4113,
		PST_ExternalCdAudioAtom = 4114,
		PST_ExternalWavAudioEmbeddedAtom = 4115,
		PST_RtfDateTimeMetaCharAtom = 4117,
		PST_PersistDirectoryAtom = 6002,
		PST_HtmlDocInfo9Atom = 6011,
		PST_HtmlPublishInfoAtom = 6012,
		PST_BroadcastDocInfo9Atom = 6015,
		PST_EnvelopeFlags9Atom = 6020,
		PST_EnvelopeData9Atom = 6021,
		PST_VisualShapeAtom = 11003,
		PST_HashCodeAtom = 11008,
		PST_VisualPageAtom = 11009,
		PST_BuildAtom = 11011,
		PST_ChartBuildAtom = 11013,
		PST_DiagramBuildAtom = 11015,
		PST_ParaBuildAtom = 11017,
		PST_LevelInfoAtom = 11018,
		PST_RoundTripAnimationAtom12Atom = 11019,
		PST_RoundTripAnimationHashAtom12Atom = 11021,
		PST_Comment10Atom = 12001,
		PST_CommentIndex10Atom = 12005,
		PST_LinkedShape10Atom = 12006,
		PST_LinkedSlide10Atom = 12007,
		PST_SlideTime10Atom = 12011,
		PST_Diff10Atom = 12014,
		PST_SlideListEntry10Atom = 12016,
		PST_DocToolbarStates10Atom = 14001,
		PST_PhotoAlbumInfo10Atom = 14002,
		PST_RoundTripSlideSyncInfo12 = 14100,
		PST_RoundTripSlideSyncInfoAtom12 = 14101,
		PST_TimeConditionContainer = 61733,
		PST_TimeNode = 61735,
		PST_TimeCondition = 61736,
		PST_TimeModifier = 61737,
		PST_TimeBehaviorContainer = 61738,
		PST_TimeAnimateBehaviorContainer = 61739,
		PST_TimeColorBehaviorContainer = 61740,
		PST_TimeEffectBehaviorContainer = 61741,
		PST_TimeMotionBehaviorContainer = 61742,
		PST_TimeRotationBehaviorContainer = 61743,
		PST_TimeScaleBehaviorContainer = 61744,
		PST_TimeSetBehaviorContainer = 61745,
		PST_TimeCommandBehaviorContainer = 61746,
		PST_TimeBehavior = 61747,
		PST_TimeAnimateBehavior = 61748,
		PST_TimeColorBehavior = 61749,
		PST_TimeEffectBehavior = 61750,
		PST_TimeMotionBehavior = 61751,
		PST_TimeRotationBehavior = 61752,
		PST_TimeScaleBehavior = 61753,
		PST_TimeSetBehavior = 61754,
		PST_TimeCommandBehavior = 61755,
		PST_TimeClientVisualElement = 61756,
		PST_TimePropertyList = 61757,
		PST_TimeVariantList = 61758,
		PST_TimeAnimationValueList = 61759,
		PST_TimeIterateData = 61760,
		PST_TimeSequenceData = 61761,
		PST_TimeVariant = 61762,
		PST_TimeAnimationValue = 61763,
		PST_TimeExtTimeNodeContainer = 61764,
		PST_TimeSlaveContainer = 61765
	}
	public enum MSORecordType : ushort
	{
		msoblipFirstClient = 32,
		msoblipLastClient = 255,
		msofbtMin = 61440,
		msofbtDggContainer = 61440,
		msofbtBstoreContainer = 61441,
		msofbtDgContainer = 61442,
		msofbtSpgrContainer = 61443,
		msofbtSpContainer = 61444,
		msofbtSolverContainer = 61445,
		msofbtDgg = 61446,
		msofbtBSE = 61447,
		msofbtDg = 61448,
		msofbtSpgr = 61449,
		msofbtSp = 61450,
		msofbtOPT = 61451,
		msofbtTextbox = 61452,
		msofbtClientTextbox = 61453,
		msofbtAnchor = 61454,
		msofbtChildAnchor = 61455,
		msofbtClientAnchor = 61456,
		msofbtClientData = 61457,
		msofbtConnectorRule = 61458,
		msofbtAlignRule = 61459,
		msofbtArcRule = 61460,
		msofbtClientRule = 61461,
		msofbtCLSID = 61462,
		msofbtCalloutRule = 61463,
		msofbtBlipFirst = 61464,
		msofbtBlipFirstClient = 61496,
		msofbtBlipLast = 61719,
		msofbtRegroupItems = 61720,
		msofbtSelection = 61721,
		msofbtColorMRU = 61722,
		msofbtUndoContainer = 61723,
		msofbtUndo = 61724,
		msofbtDeletedPspl = 61725,
		msofbtSplitMenuColors = 61726,
		msofbtOleObject = 61727,
		msofbtColorScheme = 61728,
		msofbtMax = ushort.MaxValue
	}
	public delegate void ProcessPowerPointAtom(Record record);
	public class PowerPointRecordHeader : DataStructure
	{
		[BeginBitField(2u)]
		[Order(0uL)]
		[BitFieldSize(4u)]
		public DataItem_UInt16 Version;

		[Order(1uL)]
		[BitFieldSize(12u)]
		public DataItem_UInt16 Instance;

		[Order(2uL)]
		public DataItem_UInt16 Type;

		[Order(3uL)]
		public DataItem_Int32 Length;

		public PowerPointRecordHeader(DataInByteArray Data)
			: base(Data)
		{
		}
	}
	public class Record : DataStructure
	{
		[Order(0uL)]
		public PowerPointRecordHeader Header;

		public Record()
		{
		}

		public Record(DataInByteArray Data)
			: base(Data)
		{
		}

		public override string ToString()
		{
			if (Header != null && Header.Type != null)
			{
				return ((PowerPointRecordType)Header.Type.Value.Value).ToString();
			}
			return base.ToString();
		}
	}
	public class Container : Record
	{
		[Order(1uL)]
		public List<Record> Children;

		public Container(DataInByteArray Data)
		{
			base.DataStructureOffset = Data.CurrentPosition;
			Header = new PowerPointRecordHeader(Data);
			Children = new List<Record>();
			while (Data.CurrentPosition < (ulong)((long)base.DataStructureOffset + (long)Header.Length.Value.Value + (long)Header.DataStructureLength))
			{
				if (!Data.HasDataLeftToRead)
				{
					AddParsingNote(ParsingNoteType.Error, "Hit EOF while reading a container", base.DataStructureOffset, 0uL);
					break;
				}
				Children.Add(PowerPoint97_2003BinaryFormat.FigureOutWhatTypeOfRecordThisIs(Data));
			}
			if (Data.CurrentPosition != (ulong)((long)(base.DataStructureOffset + Header.DataStructureLength) + (long)Header.Length.Value.Value))
			{
				AddParsingNote(ParsingNoteType.Warning, "A container of type " + ((PowerPointRecordType)Header.Type.Value.Value).ToString() + " had more child data than the header allowed for", base.DataStructureOffset, Header.DataStructureLength + (ulong)Header.Length.Value.Value);
				Data.Seek(base.DataStructureOffset + Header.DataStructureLength + (ulong)Header.Length.Value.Value);
			}
			base.DataStructureLength = Data.CurrentPosition - base.DataStructureOffset;
		}

		public List<T> FindRecordsOfType<T>(bool Recursive) where T : Record
		{
			return FindRecordsOfType<T>(Children, Recursive);
		}

		public static List<T> FindRecordsOfType<T>(List<Record> RecordList, bool Recursive) where T : Record
		{
			List<T> list = new List<T>();
			if (RecordList != null)
			{
				foreach (Record Record in RecordList)
				{
					if (Record is T)
					{
						list.Add((T)Record);
					}
					if (Recursive && Record is Container)
					{
						list.AddRange(FindRecordsOfType<T>(((Container)Record).Children, Recursive));
					}
				}
			}
			return list;
		}
	}
	public class Atom : Record
	{
		[Order(1uL)]
		public DataItem_UByteArray Data;

		public Atom(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Header = new PowerPointRecordHeader(DataToRead);
			if (Header.Length != null && Header.Length.Value.HasValue)
			{
				Data = new DataItem_UByteArray(DataToRead, (ulong)Header.Length.Value.Value);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class UserEditAtom : Record
	{
		[Order(1uL)]
		public DataItem_UInt32 lastSlideIdRef;

		[Order(2uL)]
		[BeginBitField(4u)]
		[BitFieldSize(16u)]
		public DataItem_UInt32 version;

		[Order(3uL)]
		[BitFieldSize(8u)]
		public DataItem_UInt32 minorVersion;

		[Order(4uL)]
		[BitFieldSize(8u)]
		public DataItem_UInt32 majorVersion;

		[Order(5uL)]
		public DataItem_UInt32 offsetLastEdit;

		[Order(6uL)]
		public DataItem_UInt32 offsetPersistDirectory;

		[Order(7uL)]
		public DataItem_UInt32 docPersistIdRef;

		[Order(8uL)]
		public DataItem_UInt32 persistIdSeed;

		[Order(9uL)]
		public DataItem_UInt16 lastView;

		[Order(10uL)]
		public DataItem_UInt16 unused;

		[Order(11uL)]
		public DataItem_UInt32 encryptSessionPersistIdRef;

		public UserEditAtom(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class PersistOffsetEntry : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt32 offset;

		public PersistOffsetEntry(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class PersistDirectoryEntry : DataStructure
	{
		[BitFieldSize(20u)]
		[BeginBitField(4u)]
		[Order(0uL)]
		public DataItem_UInt32 persistId;

		[Order(1uL)]
		[BitFieldSize(12u)]
		public DataItem_UInt32 cPersist;

		[Order(2uL)]
		[DoNotAutoProcess]
		public List<PersistOffsetEntry> rgPersistOffset;

		public PersistDirectoryEntry(DataInByteArray DataToRead)
			: base(DataToRead)
		{
			rgPersistOffset = new List<PersistOffsetEntry>();
			for (uint num = 0u; num < cPersist.Value; num++)
			{
				rgPersistOffset.Add(new PersistOffsetEntry(DataToRead));
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class PersistDirectoryAtom : Record
	{
		[Order(1uL)]
		[DoNotAutoProcess]
		public List<PersistDirectoryEntry> rgPersistDirEntryArray;

		public PersistDirectoryAtom(DataInByteArray DataToRead)
			: base(DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			int num = 0;
			rgPersistDirEntryArray = new List<PersistDirectoryEntry>();
			for (num = Header.Length.Value.Value - 8; num > 0; num -= (int)rgPersistDirEntryArray[rgPersistDirEntryArray.Count - 1].DataStructureLength)
			{
				rgPersistDirEntryArray.Add(new PersistDirectoryEntry(DataToRead));
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class CurrentUserAtom : Record
	{
		[Order(1uL)]
		public DataItem_UInt32 size;

		[Order(2uL)]
		public DataItem_UInt32 headerToken;

		[Order(3uL)]
		public DataItem_UInt32 offsetToCurrentEdit;

		[Order(4uL)]
		public DataItem_UInt16 lenUserName;

		[Order(5uL)]
		public DataItem_UInt16 docFileVersion;

		[Order(6uL)]
		public DataItem_UInt8 majorVersion;

		[Order(7uL)]
		public DataItem_UInt8 minorVersion;

		[Order(8uL)]
		public DataItem_UInt16 unused;

		[Order(9uL)]
		[DoNotAutoProcess]
		public DataItem_ASCIIString ansiUserName;

		[Order(10uL)]
		public DataItem_UInt32 relVersion;

		[Order(11uL)]
		[DoNotAutoProcess]
		public DataItem_UnicodeString unicodeUserName;

		public CurrentUserAtom(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}

		protected override void ParseData(DataInByteArray DataToRead)
		{
			Header = new PowerPointRecordHeader(DataToRead);
			size = new DataItem_UInt32(DataToRead);
			headerToken = new DataItem_UInt32(DataToRead);
			offsetToCurrentEdit = new DataItem_UInt32(DataToRead);
			lenUserName = new DataItem_UInt16(DataToRead);
			docFileVersion = new DataItem_UInt16(DataToRead);
			majorVersion = new DataItem_UInt8(DataToRead);
			minorVersion = new DataItem_UInt8(DataToRead);
			unused = new DataItem_UInt16(DataToRead);
			ansiUserName = new DataItem_ASCIIString(DataToRead, lenUserName.Value.Value);
			relVersion = new DataItem_UInt32(DataToRead);
			unicodeUserName = new DataItem_UnicodeString(DataToRead, lenUserName.Value.Value);
		}
	}
	public class PointStruct : DataStructure
	{
		[Order(0uL)]
		public DataItem_Int32 x;

		[Order(1uL)]
		public DataItem_Int32 y;

		public PointStruct(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class RatioStruct : DataStructure
	{
		[Order(0uL)]
		public DataItem_Int32 numer;

		[Order(1uL)]
		public DataItem_Int32 denom;

		public RatioStruct(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class DocumentAtom : Record
	{
		[Order(1uL)]
		public PointStruct SlideSize;

		[Order(2uL)]
		public PointStruct NotesSize;

		[Order(3uL)]
		public RatioStruct ServerZoom;

		[Order(4uL)]
		public DataItem_UInt32 notesMasterPersistIdRef;

		[Order(5uL)]
		public DataItem_UInt32 handoutsMasterPersistIdRef;

		[Order(6uL)]
		public DataItem_UInt16 firstSlideNumber;

		[Order(7uL)]
		public DataItem_UInt16 slideSizeType;

		[Order(8uL)]
		public DataItem_UInt8 fSaveWithFonts;

		[Order(9uL)]
		public DataItem_UInt8 fOmitTitlePlace;

		[Order(10uL)]
		public DataItem_UInt8 fRightToLeft;

		[Order(11uL)]
		public DataItem_UInt8 fShowComments;

		public DocumentAtom(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class SlidePersistAtom : Record
	{
		[Order(1uL)]
		public DataItem_UInt32 PSRReference;

		[Order(2uL)]
		public DataItem_UInt32 Flags;

		[Order(3uL)]
		public DataItem_Int32 NumberTexts;

		[Order(4uL)]
		public DataItem_Int32 SlideID;

		[Order(5uL)]
		public DataItem_Int32 Unused;

		public SlidePersistAtom(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class TextMasterStyle10Atom : Record
	{
		[Order(1uL)]
		public DataItem_Int16 NumLevels;

		[Order(2uL)]
		public DataItem_UByteArray Data;

		public TextMasterStyle10Atom(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Header = new PowerPointRecordHeader(DataToRead);
			NumLevels = new DataItem_Int16(DataToRead);
			if (Header.Length != null)
			{
				Data = new DataItem_UByteArray(DataToRead, (ulong)Header.Length.Value.Value - 2uL);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class RoundTripCustomTableStyles12 : Record
	{
		[Order(1uL)]
		public DataItem_UByteArray styles12Zip;

		public RoundTripCustomTableStyles12(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Header = new PowerPointRecordHeader(DataToRead);
			if (Header.Length != null)
			{
				styles12Zip = new DataItem_UByteArray(DataToRead, (ulong)Header.Length.Value.Value);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class Fopte : DataStructure
	{
		[Order(0uL)]
		[BitFieldSize(14u)]
		[BeginBitField(2u)]
		public DataItem_UInt16 pid;

		[BitFieldSize(1u)]
		[Order(1uL)]
		public DataItem_UInt8 fBid;

		[Order(2uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt8 fComplex;

		[Order(3uL)]
		public DataItem_UInt32 op;

		public Fopte(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class MSOPropertyTable : Record
	{
		[Order(1uL)]
		public List<Fopte> FopteArray;

		[Order(2uL)]
		public List<FOPTEComplex> FopteComplexArray;

		public MSOPropertyTable(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Header = new PowerPointRecordHeader(DataToRead);
			if (Header.Length != null)
			{
				FopteArray = new List<Fopte>();
				for (int i = 0; i < Header.Instance.Value; i++)
				{
					FopteArray.Add(new Fopte(DataToRead));
				}
				if (FopteArray != null)
				{
					FopteComplexArray = new List<FOPTEComplex>();
					foreach (Fopte item in FopteArray)
					{
						byte? value = item.fComplex.Value;
						if (value.GetValueOrDefault() != 0 || !value.HasValue || item.pid.Value == 1026 || item.pid.Value == 1027 || item.pid.Value == 1028 || item.pid.Value == 1029 || item.pid.Value == 1030 || item.pid.Value == 1031 || item.pid.Value == 1032 || item.pid.Value == 1033 || item.pid.Value == 1034 || item.pid.Value == 1035 || item.pid.Value == 1036 || item.pid.Value == 1037 || item.pid.Value == 1038 || item.pid.Value == 1284 || item.pid.Value == 1288 || item.pid.Value == 1349 || item.pid.Value == 1350 || item.pid.Value == 1359 || item.pid.Value == 1372 || item.pid.Value == 1376 || item.pid.Value == 1413 || item.pid.Value == 1414 || item.pid.Value == 1423 || item.pid.Value == 1436 || item.pid.Value == 1440 || item.pid.Value == 1477 || item.pid.Value == 1478 || item.pid.Value == 1487 || item.pid.Value == 1500 || item.pid.Value == 1504 || item.pid.Value == 1541 || item.pid.Value == 1542 || item.pid.Value == 1551 || item.pid.Value == 1564 || item.pid.Value == 1568 || item.pid.Value == 1605 || item.pid.Value == 1606 || item.pid.Value == 1615 || item.pid.Value == 1628 || item.pid.Value == 1632 || item.pid.Value == 1664 || item.pid.Value == 1666 || item.pid.Value == 1728 || item.pid.Value == 1792 || item.pid.Value == 192 || item.pid.Value == 193 || item.pid.Value == 197 || item.pid.Value == 198 || item.pid.Value == 271 || item.pid.Value == 272 || item.pid.Value == 274 || item.pid.Value == 280 || item.pid.Value == 286 || item.pid.Value == 325 || item.pid.Value == 326 || item.pid.Value == 337 || item.pid.Value == 338 || item.pid.Value == 341 || item.pid.Value == 342 || item.pid.Value == 343 || item.pid.Value == 345 || item.pid.Value == 391 || item.pid.Value == 407 || item.pid.Value == 417 || item.pid.Value == 421 || item.pid.Value == 454 || item.pid.Value == 463 || item.pid.Value == 476 || item.pid.Value == 480 || item.pid.Value == 533 || item.pid.Value == 537 || item.pid.Value == 652 || item.pid.Value == 896 || item.pid.Value == 897 || item.pid.Value == 898 || item.pid.Value == 899 || item.pid.Value == 909 || item.pid.Value == 910 || item.pid.Value == 919 || item.pid.Value == 921 || item.pid.Value == 922 || item.pid.Value == 928 || item.pid.Value == 930 || item.pid.Value == 932 || item.pid.Value == 933 || item.pid.Value == 934 || item.pid.Value == 936)
						{
							FopteComplexArray.Add(new FOPTEComplex(item, DataToRead));
						}
					}
				}
			}
			if (DataToRead.CurrentPosition < (ulong)((long)(base.DataStructureOffset + Header.DataStructureLength) + (long)Header.Length.Value.Value))
			{
				DataToRead.Seek(base.DataStructureOffset + Header.DataStructureLength + (ulong)Header.Length.Value.Value);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class MSOFBTBSE : Record
	{
		[Order(1uL)]
		public DataItem_UInt8 btWin32;

		[Order(2uL)]
		public DataItem_UInt8 btMacOS;

		[ConstantLength(16uL)]
		[Order(3uL)]
		public DataItem_UByteArray rgbUid;

		[Order(4uL)]
		public DataItem_UInt16 Tag;

		[Order(5uL)]
		public DataItem_UInt32 Size;

		[Order(6uL)]
		public DataItem_UInt32 cRef;

		[Order(7uL)]
		public DataItem_UInt32 foDelay;

		[Order(8uL)]
		public DataItem_UInt8 Usage;

		[Order(9uL)]
		public DataItem_UInt8 cbName;

		[Order(10uL)]
		public DataItem_UInt8 Unused2;

		[Order(11uL)]
		public DataItem_UInt8 Unused3;

		public MSOFBTBSE(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class FOPTEComplex : DataStructure
	{
		[DoNotAutoProcess]
		public Fopte Owner;

		[Order(0uL)]
		public DataItem_ByteArray Data;

		[Order(1uL)]
		public DataItem_UInt16 iMac;

		[Order(2uL)]
		public DataItem_UInt16 iMax;

		public FOPTEComplex(Fopte owner, DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Owner = owner;
			if (DataToRead.CurrentPosition + Owner.op.Value <= DataToRead.Length)
			{
				if (Owner.pid.Value == 928)
				{
					ulong currentPosition = DataToRead.CurrentPosition;
					iMac = new DataItem_UInt16(DataToRead);
					iMax = new DataItem_UInt16(DataToRead);
					DataToRead.Seek(currentPosition);
				}
				Data = new DataItem_ByteArray(DataToRead, Owner.op.Value ?? 0);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class MSOShapeAtom : Record
	{
		[Order(1uL)]
		public DataItem_UInt32 SPID;

		[Order(2uL)]
		[BitFieldSize(1u)]
		[BeginBitField(4u)]
		public DataItem_UInt32 fGroup;

		[BitFieldSize(1u)]
		[Order(3uL)]
		public DataItem_UInt32 fChild;

		[BitFieldSize(1u)]
		[Order(4uL)]
		public DataItem_UInt32 fPatriarch;

		[Order(5uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt32 fDeleted;

		[Order(6uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt32 fOleShape;

		[BitFieldSize(1u)]
		[Order(7uL)]
		public DataItem_UInt32 fHaveMaster;

		[BitFieldSize(1u)]
		[Order(8uL)]
		public DataItem_UInt32 fFlipH;

		[Order(9uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt32 fFlipV;

		[Order(10uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt32 fConnector;

		[BitFieldSize(1u)]
		[Order(11uL)]
		public DataItem_UInt32 fHaveAnchor;

		[Order(12uL)]
		[BitFieldSize(1u)]
		public DataItem_UInt32 fBackground;

		[BitFieldSize(1u)]
		[Order(13uL)]
		public DataItem_UInt32 fHaveSpt;

		[BitFieldSize(20u)]
		[Order(14uL)]
		public DataItem_UInt32 unused;

		public MSOShapeAtom(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class TextHeaderAtom : Record
	{
		[Order(1uL)]
		public DataItem_UInt32 textType;

		public TextHeaderAtom(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class MetaCharAtom : Record
	{
		[Order(1uL)]
		public DataItem_Int32 Position;

		[Order(2uL)]
		public DataItem_ByteArray Data;

		public MetaCharAtom(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Header = new PowerPointRecordHeader(DataToRead);
			Position = new DataItem_Int32(DataToRead);
			if (Header.Length.Value > 4)
			{
				Data = new DataItem_ByteArray(DataToRead, (ulong)Header.Length.Value.Value - 4uL);
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class DocRoutingSlipString : DataStructure
	{
		[Order(0uL)]
		public DataItem_UInt16 stringType;

		[Order(1uL)]
		public DataItem_UInt16 stringLength;

		[Order(2uL)]
		public DataItem_ASCIIString stringData;

		[Order(3uL)]
		public DataItem_UInt8 ignored;

		public DocRoutingSlipString(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			stringType = new DataItem_UInt16(DataToRead);
			stringLength = new DataItem_UInt16(DataToRead);
			if (((int?)stringLength.Value).HasValue)
			{
				if (((int?)stringType.Value).HasValue && (stringType.Value == 1 || stringType.Value == 2))
				{
					stringData = new DataItem_ASCIIString(DataToRead, stringLength.Value.Value);
					ignored = new DataItem_UInt8(DataToRead);
				}
				else if (((int?)stringType.Value).HasValue && (stringType.Value == 3 || stringType.Value == 4))
				{
					stringData = new DataItem_ASCIIString(DataToRead, (ulong)stringLength.Value.Value + 1uL);
				}
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}

		public override string ToString()
		{
			if (stringData != null && stringData.Value != null)
			{
				return stringData.Value;
			}
			return null;
		}
	}
	public class DocRoutingSlip : Record
	{
		[Order(1uL)]
		public DataItem_UInt32 length;

		[Order(2uL)]
		public DataItem_UInt32 unused1;

		[Order(3uL)]
		public DataItem_UInt32 recipientCount;

		[Order(4uL)]
		public DataItem_UInt32 currentRecipient;

		[Order(5uL)]
		public DataItem_UInt32 routingOptions;

		[Order(6uL)]
		public DataItem_UInt32 unused2;

		[Order(7uL)]
		public DocRoutingSlipString originatorString;

		[Order(8uL)]
		public List<DocRoutingSlipString> rgRecipientRoutingSlipStrings;

		[Order(9uL)]
		public DocRoutingSlipString subjectString;

		[Order(10uL)]
		public DocRoutingSlipString messageString;

		[Order(11uL)]
		public DataItem_UByteArray unused3;

		public DocRoutingSlip(DataInByteArray DataToRead)
		{
			base.DataStructureOffset = DataToRead.CurrentPosition;
			Header = new PowerPointRecordHeader(DataToRead);
			length = new DataItem_UInt32(DataToRead);
			unused1 = new DataItem_UInt32(DataToRead);
			recipientCount = new DataItem_UInt32(DataToRead);
			currentRecipient = new DataItem_UInt32(DataToRead);
			routingOptions = new DataItem_UInt32(DataToRead);
			unused2 = new DataItem_UInt32(DataToRead);
			originatorString = new DocRoutingSlipString(DataToRead);
			if (recipientCount.Value.HasValue)
			{
				rgRecipientRoutingSlipStrings = new List<DocRoutingSlipString>();
				for (int i = 0; i < recipientCount.Value; i++)
				{
					rgRecipientRoutingSlipStrings.Add(new DocRoutingSlipString(DataToRead));
				}
			}
			subjectString = new DocRoutingSlipString(DataToRead);
			messageString = new DocRoutingSlipString(DataToRead);
			if (Header.Length.Value > length.Value)
			{
				unused3 = new DataItem_UByteArray(DataToRead, (ulong)(Header.Length.Value.Value - length.Value.Value));
			}
			base.DataStructureLength = DataToRead.CurrentPosition - base.DataStructureOffset;
		}
	}
	public class NamedShows : Container
	{
		public NamedShows(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class DrawingContainer : Container
	{
		public DrawingContainer(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class ClientTextBox : Container
	{
		public ClientTextBox(DataInByteArray DataToRead)
			: base(DataToRead)
		{
		}
	}
	public class OEPlaceHolderAtom : Record
	{
		[Order(1uL)]
		public DataItem_UInt32 PlacementID;

		[Order(2uL)]
		public DataItem_UInt8 PlaceholderID;

		[Order(3uL)]
		public DataItem_UInt8 Size;

		[Order(4uL)]
		public DataItem_UInt16 Unused;

		public OEPlaceHolderAtom(DataInByteArray DataToRead)
			: base(DataToRead)
		{
			if (DataToRead.CurrentPosition != (ulong)((long)(base.DataStructureOffset + Header.DataStructureLength) + (long)Header.Length.Value.Value))
			{
				AddParsingNote(ParsingNoteType.Warning, "An OEPlaceHolderAtom's length didn't match its claimed value ", base.DataStructureOffset, Header.DataStructureLength + (ulong)Header.Length.Value.Value);
				DataToRead.Seek(base.DataStructureOffset + Header.DataStructureLength + (ulong)Header.Length.Value.Value);
			}
		}
	}
	public class PowerPointBinaryDocument : DataStructure
	{
		public CurrentUserAtom TheCurrentUserAtom;

		public List<Record> Children;

		private void ParseStreamOldSchool(DataInByteArray Data, ulong PowerPointDocumentStreamOffset, ulong PowerPointDocumentStreamLength, string TheReason)
		{
			AddParsingNote(ParsingNoteType.Warning, TheReason + " - Attempting alternate parsing strategy for analysis purposes only (the file may not open in PowerPoint).");
			Children = new List<Record>();
			Data.Seek(PowerPointDocumentStreamOffset);
			while (Data.CurrentPosition < PowerPointDocumentStreamOffset + PowerPointDocumentStreamLength && Data.HasDataLeftToRead)
			{
				Children.Add(PowerPoint97_2003BinaryFormat.FigureOutWhatTypeOfRecordThisIs(Data));
			}
		}

		public PowerPointBinaryDocument(DataInByteArray Data, ulong CurrentUserStreamOffset, ulong PowerPointDocumentStreamOffset, ulong PowerPointDocumentStreamLength)
		{
			bool flag = false;
			uint num = 0u;
			ulong num2 = 0uL;
			ulong num3 = 0uL;
			ulong num4 = 0uL;
			bool flag2 = false;
			List<uint> list = new List<uint>();
			Children = new List<Record>();
			if (flag)
			{
				ParseStreamOldSchool(Data, PowerPointDocumentStreamOffset, PowerPointDocumentStreamLength, "ParseOldSchool variable was true.");
				return;
			}
			Data.Seek(CurrentUserStreamOffset);
			TheCurrentUserAtom = new CurrentUserAtom(Data);
			if (TheCurrentUserAtom.Header.Type.Value != 4086)
			{
				AddParsingNote(ParsingNoteType.Warning, "Failed to find a valid CurrentUserAtom record type in the CurrentUser stream.", TheCurrentUserAtom.Header.Type.Offset, TheCurrentUserAtom.Header.Type.Length);
				return;
			}
			if (TheCurrentUserAtom != null && TheCurrentUserAtom.offsetToCurrentEdit != null && !Data.Seek((PowerPointDocumentStreamOffset + TheCurrentUserAtom.offsetToCurrentEdit.Value).Value))
			{
				AddParsingNote(ParsingNoteType.Warning, $"Failed to seek to the offset of the first UserEdit (0x{PowerPointDocumentStreamOffset + TheCurrentUserAtom.offsetToCurrentEdit.Value:X})atom.  This file may need to be defragmented or it may be corrupt.", TheCurrentUserAtom.Header.Type.Offset, TheCurrentUserAtom.Header.Type.Length);
				return;
			}
			while (!flag2)
			{
				num3 = Data.CurrentPosition;
				if (Data.PeekUInt16(2uL) != 4085)
				{
					AddParsingNote(ParsingNoteType.Error, $"Read a record type of: 0x{Data.PeekUInt16(2uL):X} (expected a UserEdit atom with recType 0xFF5) at offset 0x{num3:X}.  This file may need to be defragmented or it may be corrupt.", Data.CurrentPosition + 2, 2uL);
					ParseStreamOldSchool(Data, PowerPointDocumentStreamOffset, PowerPointDocumentStreamLength, "Could not find a valid UserEdit atom.");
					return;
				}
				if (Data.Length - Data.CurrentPosition >= 32)
				{
					Children.Add(PowerPoint97_2003BinaryFormat.FigureOutWhatTypeOfRecordThisIs(Data));
					Data.Seek(num3);
					if ((long)Data.PeekInt32(16uL).Value == 0)
					{
						flag2 = true;
					}
					if (Data.PeekInt32(20uL).HasValue)
					{
						num4 = (ulong)Data.PeekInt32(16uL).Value;
						num2 = PowerPointDocumentStreamOffset + (ulong)Data.PeekInt32(20uL).Value;
						if (Data.Seek(num2))
						{
							Children.Add(PowerPoint97_2003BinaryFormat.FigureOutWhatTypeOfRecordThisIs(Data));
							Data.Seek(num2);
							if (Data.PeekInt32(4uL).HasValue)
							{
								num = (uint)(Data.PeekInt32(4uL) - 4).Value / 4u;
								for (int i = 0; i < num; i++)
								{
									list.Add(Data.PeekUInt32((ulong)(12 + 4 * i)).Value);
								}
							}
							Data.Seek(PowerPointDocumentStreamOffset + num4);
							continue;
						}
						AddParsingNote(ParsingNoteType.Error, $"Could not read the offset of the next PersistDirectory atom in PowerPoint Document stream at offset: 0x{Data.CurrentPosition + 20:X}.  This file may need to be defragmented.", Data.CurrentPosition, 4uL);
						return;
					}
					AddParsingNote(ParsingNoteType.Error, $"Failed to seek to UserEdit atom in PowerPoint Document stream at offset: 0x{num2:X}.  This file may need to be defragmented.", Data.CurrentPosition, 4uL);
					return;
				}
				AddParsingNote(ParsingNoteType.Error, $"There is not enough data at stream at offset: 0x{num3:X} to read a UserEdit atom.  This file may need to be defragmented.", Data.CurrentPosition, 4uL);
				return;
			}
			list.Sort();
			foreach (uint item in list)
			{
				if (Data.Seek(PowerPointDocumentStreamOffset + (ulong)(int)item))
				{
					Children.Add(PowerPoint97_2003BinaryFormat.FigureOutWhatTypeOfRecordThisIs(Data));
				}
			}
		}

		public List<T> FindRecordsOfType<T>(bool Recursive) where T : Record
		{
			return Container.FindRecordsOfType<T>(Children, Recursive);
		}
	}
}
