using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Design;
using System.Drawing.Drawing2D;
using System.Globalization;
using System.IO;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Security.Permissions;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using System.Windows.Forms.Design;
using System.Windows.Forms.VisualStyles;
using Be.Windows.Forms.Design;

[assembly: AssemblyTrademark("")]
[assembly: AssemblyDelaySign(false)]
[assembly: CLSCompliant(true)]
[assembly: AssemblyCopyright("")]
[assembly: ComVisible(false)]
[assembly: CompilationRelaxations(8)]
[assembly: RuntimeCompatibility(WrapNonExceptionThrows = true)]
[assembly: AssemblyTitle("Be.Windows.Forms.HexBox")]
[assembly: AssemblyDescription("hex edit control (C# DOTNET)")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyCompany("Be")]
[assembly: AssemblyProduct("Be.Windows.Forms.HexBox")]
[assembly: AssemblyVersion("1.3.0.41466")]
namespace Be.Windows.Forms
{
	public interface IByteProvider
	{
		long Length { get; }

		event EventHandler LengthChanged;

		event EventHandler Changed;

		byte ReadByte(long index);

		void WriteByte(long index, byte value);

		void InsertBytes(long index, byte[] bs);

		void DeleteBytes(long index, long length);

		bool HasChanges();

		void ApplyChanges();

		bool SupportsWriteByte();

		bool SupportsInsertBytes();

		bool SupportsDeleteBytes();
	}
	public class DynamicByteProvider : IByteProvider
	{
		private bool _hasChanges;

		private ByteCollection _bytes;

		public ByteCollection Bytes => _bytes;

		public long Length => _bytes.Count;

		public event EventHandler Changed;

		public event EventHandler LengthChanged;

		public DynamicByteProvider(byte[] data)
			: this(new ByteCollection(data))
		{
		}

		public DynamicByteProvider(ByteCollection bytes)
		{
			_bytes = bytes;
		}

		private void OnChanged(EventArgs e)
		{
			_hasChanges = true;
			if (this.Changed != null)
			{
				this.Changed(this, e);
			}
		}

		private void OnLengthChanged(EventArgs e)
		{
			if (this.LengthChanged != null)
			{
				this.LengthChanged(this, e);
			}
		}

		public bool HasChanges()
		{
			return _hasChanges;
		}

		public void ApplyChanges()
		{
			_hasChanges = false;
		}

		public byte ReadByte(long index)
		{
			return _bytes[(int)index];
		}

		public void WriteByte(long index, byte value)
		{
			_bytes[(int)index] = value;
			OnChanged(EventArgs.Empty);
		}

		public void DeleteBytes(long index, long length)
		{
			int index2 = (int)Math.Max(0L, index);
			int count = (int)Math.Min((int)Length, length);
			_bytes.RemoveRange(index2, count);
			OnLengthChanged(EventArgs.Empty);
			OnChanged(EventArgs.Empty);
		}

		public void InsertBytes(long index, byte[] bs)
		{
			_bytes.InsertRange((int)index, bs);
			OnLengthChanged(EventArgs.Empty);
			OnChanged(EventArgs.Empty);
		}

		public bool SupportsWriteByte()
		{
			return true;
		}

		public bool SupportsInsertBytes()
		{
			return true;
		}

		public bool SupportsDeleteBytes()
		{
			return true;
		}
	}
	internal abstract class DataBlock
	{
		internal DataMap _map;

		internal DataBlock _nextBlock;

		internal DataBlock _previousBlock;

		public abstract long Length { get; }

		public DataMap Map => _map;

		public DataBlock NextBlock => _nextBlock;

		public DataBlock PreviousBlock => _previousBlock;

		public abstract void RemoveBytes(long position, long count);
	}
	internal sealed class MemoryDataBlock : DataBlock
	{
		private byte[] _data;

		public override long Length => _data.LongLength;

		public byte[] Data => _data;

		public MemoryDataBlock(byte data)
		{
			_data = new byte[1] { data };
		}

		public MemoryDataBlock(byte[] data)
		{
			if (data == null)
			{
				throw new ArgumentNullException("data");
			}
			_data = (byte[])data.Clone();
		}

		public void AddByteToEnd(byte value)
		{
			byte[] array = new byte[_data.LongLength + 1];
			_data.CopyTo(array, 0);
			array[array.LongLength - 1] = value;
			_data = array;
		}

		public void AddByteToStart(byte value)
		{
			byte[] array = new byte[_data.LongLength + 1];
			array[0] = value;
			_data.CopyTo(array, 1);
			_data = array;
		}

		public void InsertBytes(long position, byte[] data)
		{
			byte[] array = new byte[_data.LongLength + data.LongLength];
			if (position > 0)
			{
				Array.Copy(_data, 0L, array, 0L, position);
			}
			Array.Copy(data, 0L, array, position, data.LongLength);
			if (position < _data.LongLength)
			{
				Array.Copy(_data, position, array, position + data.LongLength, _data.LongLength - position);
			}
			_data = array;
		}

		public override void RemoveBytes(long position, long count)
		{
			byte[] array = new byte[_data.LongLength - count];
			if (position > 0)
			{
				Array.Copy(_data, 0L, array, 0L, position);
			}
			if (position + count < _data.LongLength)
			{
				Array.Copy(_data, position + count, array, position, array.LongLength - position);
			}
			_data = array;
		}
	}
	internal class DataMap : ICollection, IEnumerable
	{
		internal class Enumerator : IEnumerator, IDisposable
		{
			private DataMap _map;

			private DataBlock _current;

			private int _index;

			private int _version;

			object IEnumerator.Current
			{
				get
				{
					if (_index < 0 || _index > _map.Count)
					{
						throw new InvalidOperationException("Enumerator is positioned before the first element or after the last element of the collection.");
					}
					return _current;
				}
			}

			internal Enumerator(DataMap map)
			{
				_map = map;
				_version = map._version;
				_current = null;
				_index = -1;
			}

			public bool MoveNext()
			{
				if (_version != _map._version)
				{
					throw new InvalidOperationException("Collection was modified after the enumerator was instantiated.");
				}
				if (_index >= _map.Count)
				{
					return false;
				}
				if (++_index == 0)
				{
					_current = _map.FirstBlock;
				}
				else
				{
					_current = _current.NextBlock;
				}
				return _index < _map.Count;
			}

			void IEnumerator.Reset()
			{
				if (_version != _map._version)
				{
					throw new InvalidOperationException("Collection was modified after the enumerator was instantiated.");
				}
				_index = -1;
				_current = null;
			}

			public void Dispose()
			{
			}
		}

		private readonly object _syncRoot = new object();

		internal int _count;

		internal DataBlock _firstBlock;

		internal int _version;

		public DataBlock FirstBlock => _firstBlock;

		public int Count => _count;

		public bool IsSynchronized => false;

		public object SyncRoot => _syncRoot;

		public DataMap()
		{
		}

		public DataMap(IEnumerable collection)
		{
			if (collection == null)
			{
				throw new ArgumentNullException("collection");
			}
			foreach (DataBlock item in collection)
			{
				AddLast(item);
			}
		}

		public void AddAfter(DataBlock block, DataBlock newBlock)
		{
			AddAfterInternal(block, newBlock);
		}

		public void AddBefore(DataBlock block, DataBlock newBlock)
		{
			AddBeforeInternal(block, newBlock);
		}

		public void AddFirst(DataBlock block)
		{
			if (_firstBlock == null)
			{
				AddBlockToEmptyMap(block);
			}
			else
			{
				AddBeforeInternal(_firstBlock, block);
			}
		}

		public void AddLast(DataBlock block)
		{
			if (_firstBlock == null)
			{
				AddBlockToEmptyMap(block);
			}
			else
			{
				AddAfterInternal(GetLastBlock(), block);
			}
		}

		public void Remove(DataBlock block)
		{
			RemoveInternal(block);
		}

		public void RemoveFirst()
		{
			if (_firstBlock == null)
			{
				throw new InvalidOperationException("The collection is empty.");
			}
			RemoveInternal(_firstBlock);
		}

		public void RemoveLast()
		{
			if (_firstBlock == null)
			{
				throw new InvalidOperationException("The collection is empty.");
			}
			RemoveInternal(GetLastBlock());
		}

		public DataBlock Replace(DataBlock block, DataBlock newBlock)
		{
			AddAfterInternal(block, newBlock);
			RemoveInternal(block);
			return newBlock;
		}

		public void Clear()
		{
			DataBlock dataBlock = FirstBlock;
			while (dataBlock != null)
			{
				DataBlock nextBlock = dataBlock.NextBlock;
				InvalidateBlock(dataBlock);
				dataBlock = nextBlock;
			}
			_firstBlock = null;
			_count = 0;
			_version++;
		}

		private void AddAfterInternal(DataBlock block, DataBlock newBlock)
		{
			newBlock._previousBlock = block;
			newBlock._nextBlock = block._nextBlock;
			newBlock._map = this;
			if (block._nextBlock != null)
			{
				block._nextBlock._previousBlock = newBlock;
			}
			block._nextBlock = newBlock;
			_version++;
			_count++;
		}

		private void AddBeforeInternal(DataBlock block, DataBlock newBlock)
		{
			newBlock._nextBlock = block;
			newBlock._previousBlock = block._previousBlock;
			newBlock._map = this;
			if (block._previousBlock != null)
			{
				block._previousBlock._nextBlock = newBlock;
			}
			block._previousBlock = newBlock;
			if (_firstBlock == block)
			{
				_firstBlock = newBlock;
			}
			_version++;
			_count++;
		}

		private void RemoveInternal(DataBlock block)
		{
			DataBlock previousBlock = block._previousBlock;
			DataBlock nextBlock = block._nextBlock;
			if (previousBlock != null)
			{
				previousBlock._nextBlock = nextBlock;
			}
			if (nextBlock != null)
			{
				nextBlock._previousBlock = previousBlock;
			}
			if (_firstBlock == block)
			{
				_firstBlock = nextBlock;
			}
			InvalidateBlock(block);
			_count--;
			_version++;
		}

		private DataBlock GetLastBlock()
		{
			DataBlock result = null;
			for (DataBlock dataBlock = FirstBlock; dataBlock != null; dataBlock = dataBlock.NextBlock)
			{
				result = dataBlock;
			}
			return result;
		}

		private void InvalidateBlock(DataBlock block)
		{
			block._map = null;
			block._nextBlock = null;
			block._previousBlock = null;
		}

		private void AddBlockToEmptyMap(DataBlock block)
		{
			block._map = this;
			block._nextBlock = null;
			block._previousBlock = null;
			_firstBlock = block;
			_version++;
			_count++;
		}

		public void CopyTo(Array array, int index)
		{
			DataBlock[] array2 = array as DataBlock[];
			for (DataBlock dataBlock = FirstBlock; dataBlock != null; dataBlock = dataBlock.NextBlock)
			{
				array2[index++] = dataBlock;
			}
		}

		public IEnumerator GetEnumerator()
		{
			return new Enumerator(this);
		}
	}
	public class FileByteProvider : IByteProvider, IDisposable
	{
		private class WriteCollection : DictionaryBase
		{
			public byte this[long index]
			{
				get
				{
					return (byte)base.Dictionary[index];
				}
				set
				{
					base.Dictionary[index] = value;
				}
			}

			public void Add(long index, byte value)
			{
				base.Dictionary.Add(index, value);
			}

			public bool Contains(long index)
			{
				return base.Dictionary.Contains(index);
			}
		}

		private WriteCollection _writes = new WriteCollection();

		private string _fileName;

		private FileStream _fileStream;

		private bool _readOnly;

		public string FileName => _fileName;

		public long Length => _fileStream.Length;

		public event EventHandler Changed;

		public event EventHandler LengthChanged
		{
			add
			{
			}
			remove
			{
			}
		}

		public FileByteProvider(string fileName)
		{
			_fileName = fileName;
			try
			{
				_fileStream = File.Open(fileName, FileMode.Open, FileAccess.ReadWrite, FileShare.Read);
			}
			catch
			{
				try
				{
					_fileStream = File.Open(fileName, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
					_readOnly = true;
				}
				catch
				{
					throw;
				}
			}
		}

		~FileByteProvider()
		{
			Dispose();
		}

		private void OnChanged(EventArgs e)
		{
			if (this.Changed != null)
			{
				this.Changed(this, e);
			}
		}

		public bool HasChanges()
		{
			return _writes.Count > 0;
		}

		public void ApplyChanges()
		{
			if (_readOnly)
			{
				throw new Exception("File is in read-only mode.");
			}
			if (!HasChanges())
			{
				return;
			}
			IDictionaryEnumerator enumerator = _writes.GetEnumerator();
			while (enumerator.MoveNext())
			{
				long num = (long)enumerator.Key;
				byte b = (byte)enumerator.Value;
				if (_fileStream.Position != num)
				{
					_fileStream.Position = num;
				}
				_fileStream.Write(new byte[1] { b }, 0, 1);
			}
			_writes.Clear();
		}

		public void RejectChanges()
		{
			_writes.Clear();
		}

		public byte ReadByte(long index)
		{
			if (_writes.Contains(index))
			{
				return _writes[index];
			}
			if (_fileStream.Position != index)
			{
				_fileStream.Position = index;
			}
			return (byte)_fileStream.ReadByte();
		}

		public void WriteByte(long index, byte value)
		{
			if (_writes.Contains(index))
			{
				_writes[index] = value;
			}
			else
			{
				_writes.Add(index, value);
			}
			OnChanged(EventArgs.Empty);
		}

		public void DeleteBytes(long index, long length)
		{
			throw new NotSupportedException("FileByteProvider.DeleteBytes");
		}

		public void InsertBytes(long index, byte[] bs)
		{
			throw new NotSupportedException("FileByteProvider.InsertBytes");
		}

		public bool SupportsWriteByte()
		{
			return !_readOnly;
		}

		public bool SupportsInsertBytes()
		{
			return false;
		}

		public bool SupportsDeleteBytes()
		{
			return false;
		}

		public void Dispose()
		{
			if (_fileStream != null)
			{
				_fileName = null;
				_fileStream.Close();
				_fileStream = null;
			}
			GC.SuppressFinalize(this);
		}
	}
	public class ByteCollection : CollectionBase
	{
		public byte this[int index]
		{
			get
			{
				return (byte)base.List[index];
			}
			set
			{
				base.List[index] = value;
			}
		}

		public ByteCollection()
		{
		}

		public ByteCollection(byte[] bs)
		{
			AddRange(bs);
		}

		public void Add(byte b)
		{
			base.List.Add(b);
		}

		public void AddRange(byte[] bs)
		{
			base.InnerList.AddRange(bs);
		}

		public void Remove(byte b)
		{
			base.List.Remove(b);
		}

		public void RemoveRange(int index, int count)
		{
			base.InnerList.RemoveRange(index, count);
		}

		public void InsertRange(int index, byte[] bs)
		{
			base.InnerList.InsertRange(index, bs);
		}

		public byte[] GetBytes()
		{
			byte[] array = new byte[base.Count];
			base.InnerList.CopyTo(0, array, 0, array.Length);
			return array;
		}

		public void Insert(int index, byte b)
		{
			base.InnerList.Insert(index, b);
		}

		public int IndexOf(byte b)
		{
			return base.InnerList.IndexOf(b);
		}

		public bool Contains(bool b)
		{
			return base.InnerList.Contains(b);
		}

		public void CopyTo(byte[] bs, int index)
		{
			base.InnerList.CopyTo(bs, index);
		}

		public byte[] ToArray()
		{
			byte[] array = new byte[base.Count];
			CopyTo(array, 0);
			return array;
		}
	}
	internal sealed class FileDataBlock : DataBlock
	{
		private long _length;

		private long _fileOffset;

		public long FileOffset => _fileOffset;

		public override long Length => _length;

		public FileDataBlock(long fileOffset, long length)
		{
			_fileOffset = fileOffset;
			_length = length;
		}

		public void SetFileOffset(long value)
		{
			_fileOffset = value;
		}

		public void RemoveBytesFromEnd(long count)
		{
			if (count > _length)
			{
				throw new ArgumentOutOfRangeException("count");
			}
			_length -= count;
		}

		public void RemoveBytesFromStart(long count)
		{
			if (count > _length)
			{
				throw new ArgumentOutOfRangeException("count");
			}
			_fileOffset += count;
			_length -= count;
		}

		public override void RemoveBytes(long position, long count)
		{
			if (position > _length)
			{
				throw new ArgumentOutOfRangeException("offset");
			}
			if (position + count > _length)
			{
				throw new ArgumentOutOfRangeException("count");
			}
			long fileOffset = _fileOffset;
			long num = _length - count - position;
			long fileOffset2 = _fileOffset + position + count;
			if (position > 0 && num > 0)
			{
				_fileOffset = fileOffset;
				_length = position;
				_map.AddAfter(this, new FileDataBlock(fileOffset2, num));
			}
			else if (position > 0)
			{
				_fileOffset = fileOffset;
				_length = position;
			}
			else
			{
				_fileOffset = fileOffset2;
				_length = num;
			}
		}
	}
	internal class ByteView
	{
		public Brush ForegroundColor;

		public Brush BackgroundColor;
	}
	internal class HexRange
	{
		public Brush ForegroundColor;

		public Brush BackgroundColor;

		public long Start;

		public long End;

		public bool Contains(long index)
		{
			if (Start <= index)
			{
				return End >= index;
			}
			return false;
		}

		public bool Overlaps(HexRange range)
		{
			if (!Contains(range.Start) && !Contains(range.End) && !range.Contains(Start))
			{
				return range.Contains(End);
			}
			return true;
		}

		public bool Consecutive(HexRange range)
		{
			if (End + 1 != range.Start)
			{
				return Start == range.End + 1;
			}
			return true;
		}

		public bool StylesMatch(HexRange range)
		{
			if (ForegroundColor == range.ForegroundColor)
			{
				return BackgroundColor == range.BackgroundColor;
			}
			return false;
		}

		public bool Merge(HexRange range)
		{
			if ((Overlaps(range) || Consecutive(range)) && StylesMatch(range))
			{
				if (range.Start < Start)
				{
					Start = range.Start;
				}
				if (range.End > End)
				{
					End = range.End;
				}
				return true;
			}
			return false;
		}
	}
	public class HexRanges
	{
		private int LastRange;

		internal List<HexRange> Ranges = new List<HexRange>();

		public HexRanges()
		{
			LastRange = -1;
		}

		public void Clear()
		{
			Ranges.Clear();
		}

		private int FindRangeAfter(long index)
		{
			if (LastRange >= Ranges.Count)
			{
				LastRange = Ranges.Count - 1;
			}
			if (LastRange < 0)
			{
				LastRange = 0;
			}
			while (LastRange < Ranges.Count && LastRange >= 0)
			{
				if (Ranges[LastRange].Start >= index)
				{
					if (LastRange <= 0 || Ranges[LastRange - 1].Start < index)
					{
						return LastRange;
					}
					LastRange--;
				}
				else
				{
					LastRange++;
				}
			}
			return Ranges.Count;
		}

		public int FindRange(long index)
		{
			if (LastRange >= Ranges.Count)
			{
				LastRange = Ranges.Count - 1;
			}
			if (LastRange < 0)
			{
				LastRange = 0;
			}
			while (LastRange < Ranges.Count && LastRange >= 0)
			{
				if (Ranges[LastRange].Start > index)
				{
					LastRange--;
					if (LastRange >= 0 && Ranges[LastRange].End < index)
					{
						return -1;
					}
					continue;
				}
				if (Ranges[LastRange].End < index)
				{
					LastRange++;
					if (LastRange < Ranges.Count && Ranges[LastRange].Start > index)
					{
						return -1;
					}
					continue;
				}
				return LastRange;
			}
			return -1;
		}

		internal void GetByteView(long index, ref ByteView view)
		{
			int num = FindRange(index);
			if (num >= 0)
			{
				if (Ranges[num].ForegroundColor != null)
				{
					view.ForegroundColor = Ranges[num].ForegroundColor;
				}
				if (Ranges[num].ForegroundColor != null)
				{
					view.BackgroundColor = Ranges[num].BackgroundColor;
				}
			}
		}

		private void Add(HexRange range)
		{
			int num = FindRangeAfter(range.Start);
			Ranges.Insert(num, range);
			int num2 = num + 1;
			while (num2 < Ranges.Count)
			{
				HexRange hexRange = Ranges[num2];
				if (hexRange.Start > range.End)
				{
					break;
				}
				if (hexRange.End > range.End)
				{
					hexRange.Start = range.End + 1;
					break;
				}
				Ranges.RemoveAt(num2);
			}
			if (num > 0)
			{
				HexRange hexRange2 = Ranges[num - 1];
				if (hexRange2.End >= range.Start)
				{
					if (hexRange2.End > range.End)
					{
						HexRange hexRange3 = new HexRange();
						hexRange3.Start = range.End + 1;
						hexRange3.End = hexRange2.End;
						hexRange3.ForegroundColor = hexRange2.ForegroundColor;
						hexRange3.BackgroundColor = hexRange2.BackgroundColor;
						Ranges.Insert(num + 1, hexRange3);
					}
					if (hexRange2.Start < range.Start)
					{
						hexRange2.End = range.Start - 1;
					}
				}
			}
			if (num < Ranges.Count - 1 && Ranges[num + 1].Merge(Ranges[num]))
			{
				Ranges.RemoveAt(num);
			}
			if (num > 0 && Ranges[num - 1].Merge(Ranges[num]))
			{
				Ranges.RemoveAt(num);
			}
		}

		public void Add(long start, long length, Color fg, Color bg)
		{
			if (length > 0)
			{
				HexRange hexRange = new HexRange();
				hexRange.Start = start;
				hexRange.End = start + length - 1;
				hexRange.ForegroundColor = new SolidBrush(fg);
				hexRange.BackgroundColor = new SolidBrush(bg);
				Add(hexRange);
			}
		}

		public bool Validate()
		{
			long num = -1L;
			foreach (HexRange range in Ranges)
			{
				if (range.Start <= num || range.End < range.Start)
				{
					return false;
				}
				num = range.End;
			}
			return true;
		}

		public bool StrictValidate()
		{
			long num = -1L;
			foreach (HexRange range in Ranges)
			{
				if (range.Start != num + 1 || range.End < range.Start)
				{
					return false;
				}
				num = range.End;
			}
			return true;
		}
	}
	public enum HexCasing
	{
		Upper,
		Lower
	}
	internal struct BytePositionInfo
	{
		private int _characterPosition;

		private long _index;

		public int CharacterPosition => _characterPosition;

		public long Index => _index;

		public BytePositionInfo(long index, int characterPosition)
		{
			_index = index;
			_characterPosition = characterPosition;
		}
	}
	[ToolboxBitmap(typeof(HexBox), "HexBox.bmp")]
	public class HexBox : Control
	{
		private interface IKeyInterpreter
		{
			void Activate();

			void Deactivate();

			bool PreProcessWmKeyUp(ref Message m);

			bool PreProcessWmChar(ref Message m);

			bool PreProcessWmKeyDown(ref Message m);

			PointF GetCaretPointF(long byteIndex);
		}

		private class EmptyKeyInterpreter : IKeyInterpreter
		{
			private HexBox _hexBox;

			public EmptyKeyInterpreter(HexBox hexBox)
			{
				_hexBox = hexBox;
			}

			public void Activate()
			{
			}

			public void Deactivate()
			{
			}

			public bool PreProcessWmKeyUp(ref Message m)
			{
				return _hexBox.BasePreProcessMessage(ref m);
			}

			public bool PreProcessWmChar(ref Message m)
			{
				return _hexBox.BasePreProcessMessage(ref m);
			}

			public bool PreProcessWmKeyDown(ref Message m)
			{
				return _hexBox.BasePreProcessMessage(ref m);
			}

			public PointF GetCaretPointF(long byteIndex)
			{
				return default(PointF);
			}
		}

		private class KeyInterpreter : IKeyInterpreter
		{
			protected HexBox _hexBox;

			protected bool _shiftDown;

			private bool _mouseDown;

			private BytePositionInfo _bpiStart;

			private BytePositionInfo _bpi;

			public KeyInterpreter(HexBox hexBox)
			{
				_hexBox = hexBox;
			}

			public virtual void Activate()
			{
				_hexBox.MouseDown += BeginMouseSelection;
				_hexBox.MouseMove += UpdateMouseSelection;
				_hexBox.MouseUp += EndMouseSelection;
			}

			public virtual void Deactivate()
			{
				_hexBox.MouseDown -= BeginMouseSelection;
				_hexBox.MouseMove -= UpdateMouseSelection;
				_hexBox.MouseUp -= EndMouseSelection;
			}

			private void BeginMouseSelection(object sender, MouseEventArgs e)
			{
				_mouseDown = true;
				if (!_shiftDown)
				{
					_bpiStart = new BytePositionInfo(_hexBox._bytePos, _hexBox._byteCharacterPos);
					_hexBox.ReleaseSelection();
				}
				else
				{
					UpdateMouseSelection(this, e);
				}
			}

			private void UpdateMouseSelection(object sender, MouseEventArgs e)
			{
				if (_mouseDown)
				{
					_bpi = GetBytePositionInfo(new Point(e.X, e.Y));
					long index = _bpi.Index;
					long num;
					long num2;
					if (index < _bpiStart.Index)
					{
						num = index;
						num2 = _bpiStart.Index - index;
					}
					else if (index > _bpiStart.Index)
					{
						num = _bpiStart.Index;
						num2 = index - num;
					}
					else
					{
						num = _hexBox._bytePos;
						num2 = 0L;
					}
					if (num != _hexBox._bytePos || num2 != _hexBox._selectionLength)
					{
						_hexBox.InternalSelect(num, num2);
					}
				}
			}

			private void EndMouseSelection(object sender, MouseEventArgs e)
			{
				_mouseDown = false;
			}

			public virtual bool PreProcessWmKeyDown(ref Message m)
			{
				Keys keys = (Keys)m.WParam.ToInt32();
				Keys keys2 = keys | Control.ModifierKeys;
				switch (keys2)
				{
				case Keys.Back:
				case Keys.Tab:
				case Keys.Prior:
				case Keys.Next:
				case Keys.End:
				case Keys.Home:
				case Keys.Left:
				case Keys.Up:
				case Keys.Right:
				case Keys.Down:
				case Keys.Delete:
				case Keys.ShiftKey | Keys.Shift:
				case Keys.Left | Keys.Shift:
				case Keys.Up | Keys.Shift:
				case Keys.Right | Keys.Shift:
				case Keys.Down | Keys.Shift:
				case Keys.C | Keys.Control:
				case Keys.V | Keys.Control:
				case Keys.X | Keys.Control:
					if (RaiseKeyDown(keys2))
					{
						return true;
					}
					break;
				}
				switch (keys2)
				{
				case Keys.Left:
					return PreProcessWmKeyDown_Left(ref m);
				case Keys.Up:
					return PreProcessWmKeyDown_Up(ref m);
				case Keys.Right:
					return PreProcessWmKeyDown_Right(ref m);
				case Keys.Down:
					return PreProcessWmKeyDown_Down(ref m);
				case Keys.Prior:
					return PreProcessWmKeyDown_PageUp(ref m);
				case Keys.Next:
					return PreProcessWmKeyDown_PageDown(ref m);
				case Keys.Left | Keys.Shift:
					return PreProcessWmKeyDown_ShiftLeft(ref m);
				case Keys.Up | Keys.Shift:
					return PreProcessWmKeyDown_ShiftUp(ref m);
				case Keys.Right | Keys.Shift:
					return PreProcessWmKeyDown_ShiftRight(ref m);
				case Keys.Down | Keys.Shift:
					return PreProcessWmKeyDown_ShiftDown(ref m);
				case Keys.Tab:
					return PreProcessWmKeyDown_Tab(ref m);
				case Keys.Back:
					return PreProcessWmKeyDown_Back(ref m);
				case Keys.Delete:
					return PreProcessWmKeyDown_Delete(ref m);
				case Keys.Home:
					return PreProcessWmKeyDown_Home(ref m);
				case Keys.End:
					return PreProcessWmKeyDown_End(ref m);
				case Keys.ShiftKey | Keys.Shift:
					return PreProcessWmKeyDown_ShiftShiftKey(ref m);
				case Keys.C | Keys.Control:
					return PreProcessWmKeyDown_ControlC(ref m);
				case Keys.X | Keys.Control:
					return PreProcessWmKeyDown_ControlX(ref m);
				case Keys.V | Keys.Control:
					return PreProcessWmKeyDown_ControlV(ref m);
				default:
					_hexBox.ScrollByteIntoView();
					return _hexBox.BasePreProcessMessage(ref m);
				}
			}

			protected bool RaiseKeyDown(Keys keyData)
			{
				KeyEventArgs e = new KeyEventArgs(keyData);
				_hexBox.OnKeyDown(e);
				return e.Handled;
			}

			protected virtual bool PreProcessWmKeyDown_Left(ref Message m)
			{
				return PerformPosMoveLeft();
			}

			protected virtual bool PreProcessWmKeyDown_Up(ref Message m)
			{
				long bytePos = _hexBox._bytePos;
				int byteCharacterPos = _hexBox._byteCharacterPos;
				if (bytePos != 0 || byteCharacterPos != 0)
				{
					bytePos = Math.Max(-1L, bytePos - _hexBox._iHexMaxHBytes);
					if (bytePos == -1)
					{
						return true;
					}
					_hexBox.SetPosition(bytePos);
					if (bytePos < _hexBox._startByte)
					{
						_hexBox.PerformScrollLineUp();
					}
					_hexBox.UpdateCaret();
					_hexBox.Invalidate();
				}
				_hexBox.ScrollByteIntoView();
				_hexBox.ReleaseSelection();
				return true;
			}

			protected virtual bool PreProcessWmKeyDown_Right(ref Message m)
			{
				return PerformPosMoveRight();
			}

			protected virtual bool PreProcessWmKeyDown_Down(ref Message m)
			{
				long bytePos = _hexBox._bytePos;
				int num = _hexBox._byteCharacterPos;
				if (bytePos == _hexBox._byteProvider.Length && num == 0)
				{
					return true;
				}
				bytePos = Math.Min(_hexBox._byteProvider.Length, bytePos + _hexBox._iHexMaxHBytes);
				if (bytePos == _hexBox._byteProvider.Length)
				{
					num = 0;
				}
				_hexBox.SetPosition(bytePos, num);
				if (bytePos > _hexBox._endByte - 1)
				{
					_hexBox.PerformScrollLineDown();
				}
				_hexBox.UpdateCaret();
				_hexBox.ScrollByteIntoView();
				_hexBox.ReleaseSelection();
				_hexBox.Invalidate();
				return true;
			}

			protected virtual bool PreProcessWmKeyDown_PageUp(ref Message m)
			{
				long bytePos = _hexBox._bytePos;
				int byteCharacterPos = _hexBox._byteCharacterPos;
				if (bytePos == 0 && byteCharacterPos == 0)
				{
					return true;
				}
				bytePos = Math.Max(0L, bytePos - _hexBox._iHexMaxBytes);
				if (bytePos == 0)
				{
					return true;
				}
				_hexBox.SetPosition(bytePos);
				if (bytePos < _hexBox._startByte)
				{
					_hexBox.PerformScrollPageUp();
				}
				_hexBox.ReleaseSelection();
				_hexBox.UpdateCaret();
				_hexBox.Invalidate();
				return true;
			}

			protected virtual bool PreProcessWmKeyDown_PageDown(ref Message m)
			{
				long bytePos = _hexBox._bytePos;
				int num = _hexBox._byteCharacterPos;
				if (bytePos == _hexBox._byteProvider.Length && num == 0)
				{
					return true;
				}
				bytePos = Math.Min(_hexBox._byteProvider.Length, bytePos + _hexBox._iHexMaxBytes);
				if (bytePos == _hexBox._byteProvider.Length)
				{
					num = 0;
				}
				_hexBox.SetPosition(bytePos, num);
				if (bytePos > _hexBox._endByte - 1)
				{
					_hexBox.PerformScrollPageDown();
				}
				_hexBox.ReleaseSelection();
				_hexBox.UpdateCaret();
				_hexBox.Invalidate();
				return true;
			}

			protected virtual bool PreProcessWmKeyDown_ShiftLeft(ref Message m)
			{
				long num = _hexBox._bytePos;
				long selectionLength = _hexBox._selectionLength;
				if (num + selectionLength < 1)
				{
					return true;
				}
				if (num + selectionLength <= _bpiStart.Index)
				{
					if (num == 0)
					{
						return true;
					}
					num--;
					selectionLength++;
				}
				else
				{
					selectionLength = Math.Max(0L, selectionLength - 1);
				}
				_hexBox.ScrollByteIntoView();
				_hexBox.InternalSelect(num, selectionLength);
				return true;
			}

			protected virtual bool PreProcessWmKeyDown_ShiftUp(ref Message m)
			{
				long bytePos = _hexBox._bytePos;
				long selectionLength = _hexBox._selectionLength;
				if (bytePos - _hexBox._iHexMaxHBytes < 0 && bytePos <= _bpiStart.Index)
				{
					return true;
				}
				if (_bpiStart.Index >= bytePos + selectionLength)
				{
					bytePos -= _hexBox._iHexMaxHBytes;
					selectionLength += _hexBox._iHexMaxHBytes;
					_hexBox.InternalSelect(bytePos, selectionLength);
					_hexBox.ScrollByteIntoView();
				}
				else
				{
					selectionLength -= _hexBox._iHexMaxHBytes;
					if (selectionLength < 0)
					{
						bytePos = _bpiStart.Index + selectionLength;
						selectionLength = -selectionLength;
						_hexBox.InternalSelect(bytePos, selectionLength);
						_hexBox.ScrollByteIntoView();
					}
					else
					{
						selectionLength -= _hexBox._iHexMaxHBytes;
						_hexBox.InternalSelect(bytePos, selectionLength);
						_hexBox.ScrollByteIntoView(bytePos + selectionLength);
					}
				}
				return true;
			}

			protected virtual bool PreProcessWmKeyDown_ShiftRight(ref Message m)
			{
				long bytePos = _hexBox._bytePos;
				long selectionLength = _hexBox._selectionLength;
				if (bytePos + selectionLength >= _hexBox._byteProvider.Length)
				{
					return true;
				}
				if (_bpiStart.Index <= bytePos)
				{
					selectionLength++;
					_hexBox.InternalSelect(bytePos, selectionLength);
					_hexBox.ScrollByteIntoView(bytePos + selectionLength);
				}
				else
				{
					bytePos++;
					selectionLength = Math.Max(0L, selectionLength - 1);
					_hexBox.InternalSelect(bytePos, selectionLength);
					_hexBox.ScrollByteIntoView();
				}
				return true;
			}

			protected virtual bool PreProcessWmKeyDown_ShiftDown(ref Message m)
			{
				long bytePos = _hexBox._bytePos;
				long selectionLength = _hexBox._selectionLength;
				long length = _hexBox._byteProvider.Length;
				if (bytePos + selectionLength + _hexBox._iHexMaxHBytes > length)
				{
					return true;
				}
				if (_bpiStart.Index <= bytePos)
				{
					selectionLength += _hexBox._iHexMaxHBytes;
					_hexBox.InternalSelect(bytePos, selectionLength);
					_hexBox.ScrollByteIntoView(bytePos + selectionLength);
				}
				else
				{
					selectionLength -= _hexBox._iHexMaxHBytes;
					if (selectionLength < 0)
					{
						bytePos = _bpiStart.Index;
						selectionLength = -selectionLength;
					}
					else
					{
						bytePos += _hexBox._iHexMaxHBytes;
						selectionLength -= _hexBox._iHexMaxHBytes;
					}
					_hexBox.InternalSelect(bytePos, selectionLength);
					_hexBox.ScrollByteIntoView();
				}
				return true;
			}

			protected virtual bool PreProcessWmKeyDown_Tab(ref Message m)
			{
				if (_hexBox._stringViewVisible && _hexBox._keyInterpreter.GetType() == typeof(KeyInterpreter))
				{
					_hexBox.ActivateStringKeyInterpreter();
					_hexBox.ScrollByteIntoView();
					_hexBox.ReleaseSelection();
					_hexBox.UpdateCaret();
					_hexBox.Invalidate();
					return true;
				}
				if (_hexBox.Parent == null)
				{
					return true;
				}
				_hexBox.Parent.SelectNextControl(_hexBox, forward: true, tabStopOnly: true, nested: true, wrap: true);
				return true;
			}

			protected virtual bool PreProcessWmKeyDown_ShiftTab(ref Message m)
			{
				if (_hexBox._keyInterpreter is StringKeyInterpreter)
				{
					_shiftDown = false;
					_hexBox.ActivateKeyInterpreter();
					_hexBox.ScrollByteIntoView();
					_hexBox.ReleaseSelection();
					_hexBox.UpdateCaret();
					_hexBox.Invalidate();
					return true;
				}
				if (_hexBox.Parent == null)
				{
					return true;
				}
				_hexBox.Parent.SelectNextControl(_hexBox, forward: false, tabStopOnly: true, nested: true, wrap: true);
				return true;
			}

			protected virtual bool PreProcessWmKeyDown_Back(ref Message m)
			{
				if (!_hexBox._byteProvider.SupportsDeleteBytes())
				{
					return true;
				}
				long bytePos = _hexBox._bytePos;
				long selectionLength = _hexBox._selectionLength;
				long num = ((_hexBox._byteCharacterPos == 0 && selectionLength == 0) ? (bytePos - 1) : bytePos);
				if (num < 0 && selectionLength < 1)
				{
					return true;
				}
				long length = ((selectionLength > 0) ? selectionLength : 1);
				_hexBox._byteProvider.DeleteBytes(Math.Max(0L, num), length);
				_hexBox.UpdateScrollSize();
				if (selectionLength == 0)
				{
					PerformPosMoveLeftByte();
				}
				_hexBox.ReleaseSelection();
				_hexBox.Invalidate();
				return true;
			}

			protected virtual bool PreProcessWmKeyDown_Delete(ref Message m)
			{
				if (!_hexBox._byteProvider.SupportsDeleteBytes())
				{
					return true;
				}
				long bytePos = _hexBox._bytePos;
				long selectionLength = _hexBox._selectionLength;
				if (bytePos >= _hexBox._byteProvider.Length)
				{
					return true;
				}
				long length = ((selectionLength > 0) ? selectionLength : 1);
				_hexBox._byteProvider.DeleteBytes(bytePos, length);
				_hexBox.UpdateScrollSize();
				_hexBox.ReleaseSelection();
				_hexBox.Invalidate();
				return true;
			}

			protected virtual bool PreProcessWmKeyDown_Home(ref Message m)
			{
				long bytePos = _hexBox._bytePos;
				int byteCharacterPos = _hexBox._byteCharacterPos;
				if (bytePos < 1)
				{
					return true;
				}
				bytePos = 0L;
				byteCharacterPos = 0;
				_hexBox.SetPosition(bytePos, byteCharacterPos);
				_hexBox.ScrollByteIntoView();
				_hexBox.UpdateCaret();
				_hexBox.ReleaseSelection();
				return true;
			}

			protected virtual bool PreProcessWmKeyDown_End(ref Message m)
			{
				long bytePos = _hexBox._bytePos;
				int byteCharacterPos = _hexBox._byteCharacterPos;
				if (bytePos >= _hexBox._byteProvider.Length - 1)
				{
					return true;
				}
				bytePos = _hexBox._byteProvider.Length;
				byteCharacterPos = 0;
				_hexBox.SetPosition(bytePos, byteCharacterPos);
				_hexBox.ScrollByteIntoView();
				_hexBox.UpdateCaret();
				_hexBox.ReleaseSelection();
				return true;
			}

			protected virtual bool PreProcessWmKeyDown_ShiftShiftKey(ref Message m)
			{
				if (_mouseDown)
				{
					return true;
				}
				if (_shiftDown)
				{
					return true;
				}
				_shiftDown = true;
				if (_hexBox._selectionLength > 0)
				{
					return true;
				}
				_bpiStart = new BytePositionInfo(_hexBox._bytePos, _hexBox._byteCharacterPos);
				return true;
			}

			protected virtual bool PreProcessWmKeyDown_ControlC(ref Message m)
			{
				_hexBox.Copy();
				return true;
			}

			protected virtual bool PreProcessWmKeyDown_ControlX(ref Message m)
			{
				_hexBox.Cut();
				return true;
			}

			protected virtual bool PreProcessWmKeyDown_ControlV(ref Message m)
			{
				_hexBox.Paste();
				return true;
			}

			public virtual bool PreProcessWmChar(ref Message m)
			{
				if (Control.ModifierKeys == Keys.Control)
				{
					return _hexBox.BasePreProcessMessage(ref m);
				}
				bool flag = _hexBox._byteProvider.SupportsWriteByte();
				bool flag2 = _hexBox._byteProvider.SupportsInsertBytes();
				bool flag3 = _hexBox._byteProvider.SupportsDeleteBytes();
				long bytePos = _hexBox._bytePos;
				long selectionLength = _hexBox._selectionLength;
				int num = _hexBox._byteCharacterPos;
				if ((!flag && bytePos != _hexBox._byteProvider.Length) || (!flag2 && bytePos == _hexBox._byteProvider.Length))
				{
					return _hexBox.BasePreProcessMessage(ref m);
				}
				char c = (char)m.WParam.ToInt32();
				if (Uri.IsHexDigit(c))
				{
					if (RaiseKeyPress(c))
					{
						return true;
					}
					if (_hexBox.ReadOnly)
					{
						return true;
					}
					bool flag4 = bytePos == _hexBox._byteProvider.Length;
					if (!flag4 && flag2 && _hexBox._insertActive && num == 0)
					{
						flag4 = true;
					}
					if (flag3 && flag2 && selectionLength > 0)
					{
						_hexBox._byteProvider.DeleteBytes(bytePos, selectionLength);
						flag4 = true;
						num = 0;
						_hexBox.SetPosition(bytePos, num);
					}
					_hexBox.ReleaseSelection();
					string text = ((byte)((!flag4) ? _hexBox._byteProvider.ReadByte(bytePos) : 0)).ToString("X", Thread.CurrentThread.CurrentCulture);
					if (text.Length == 1)
					{
						text = "0" + text;
					}
					string text2 = c.ToString();
					text2 = ((num != 0) ? (text.Substring(0, 1) + text2) : (text2 + text.Substring(1, 1)));
					byte b = byte.Parse(text2, NumberStyles.AllowHexSpecifier, Thread.CurrentThread.CurrentCulture);
					if (flag4)
					{
						_hexBox._byteProvider.InsertBytes(bytePos, new byte[1] { b });
					}
					else
					{
						_hexBox._byteProvider.WriteByte(bytePos, b);
					}
					PerformPosMoveRight();
					_hexBox.Invalidate();
					return true;
				}
				return _hexBox.BasePreProcessMessage(ref m);
			}

			protected bool RaiseKeyPress(char keyChar)
			{
				KeyPressEventArgs e = new KeyPressEventArgs(keyChar);
				_hexBox.OnKeyPress(e);
				return e.Handled;
			}

			public virtual bool PreProcessWmKeyUp(ref Message m)
			{
				Keys keys = (Keys)m.WParam.ToInt32();
				Keys keys2 = keys | Control.ModifierKeys;
				Keys keys3 = keys2;
				if ((keys3 == Keys.ShiftKey || keys3 == Keys.Insert) && RaiseKeyUp(keys2))
				{
					return true;
				}
				switch (keys2)
				{
				case Keys.ShiftKey:
					_shiftDown = false;
					return true;
				case Keys.Insert:
					return PreProcessWmKeyUp_Insert(ref m);
				default:
					return _hexBox.BasePreProcessMessage(ref m);
				}
			}

			protected virtual bool PreProcessWmKeyUp_Insert(ref Message m)
			{
				_hexBox._insertActive = !_hexBox._insertActive;
				_hexBox.OnInsertActiveChanged(EventArgs.Empty);
				return true;
			}

			protected bool RaiseKeyUp(Keys keyData)
			{
				KeyEventArgs e = new KeyEventArgs(keyData);
				_hexBox.OnKeyUp(e);
				return e.Handled;
			}

			protected virtual bool PerformPosMoveLeft()
			{
				long num = _hexBox._bytePos;
				long selectionLength = _hexBox._selectionLength;
				int byteCharacterPos = _hexBox._byteCharacterPos;
				if (selectionLength != 0)
				{
					byteCharacterPos = 0;
					_hexBox.SetPosition(num, byteCharacterPos);
					_hexBox.ReleaseSelection();
				}
				else
				{
					if (num == 0 && byteCharacterPos == 0)
					{
						return true;
					}
					if (byteCharacterPos > 0)
					{
						byteCharacterPos--;
					}
					else
					{
						num = Math.Max(0L, num - 1);
						byteCharacterPos++;
					}
					_hexBox.SetPosition(num, byteCharacterPos);
					if (num < _hexBox._startByte)
					{
						_hexBox.PerformScrollLineUp();
					}
					_hexBox.UpdateCaret();
					_hexBox.Invalidate();
				}
				_hexBox.ScrollByteIntoView();
				return true;
			}

			protected virtual bool PerformPosMoveRight()
			{
				long num = _hexBox._bytePos;
				int byteCharacterPos = _hexBox._byteCharacterPos;
				long selectionLength = _hexBox._selectionLength;
				if (selectionLength != 0)
				{
					num += selectionLength;
					byteCharacterPos = 0;
					_hexBox.SetPosition(num, byteCharacterPos);
					_hexBox.ReleaseSelection();
				}
				else if (num != _hexBox._byteProvider.Length || byteCharacterPos != 0)
				{
					if (byteCharacterPos > 0)
					{
						num = Math.Min(_hexBox._byteProvider.Length, num + 1);
						byteCharacterPos = 0;
					}
					else
					{
						byteCharacterPos++;
					}
					_hexBox.SetPosition(num, byteCharacterPos);
					if (num > _hexBox._endByte - 1)
					{
						_hexBox.PerformScrollLineDown();
					}
					_hexBox.UpdateCaret();
					_hexBox.Invalidate();
				}
				_hexBox.ScrollByteIntoView();
				return true;
			}

			protected virtual bool PerformPosMoveLeftByte()
			{
				long bytePos = _hexBox._bytePos;
				int byteCharacterPos = _hexBox._byteCharacterPos;
				if (bytePos == 0)
				{
					return true;
				}
				bytePos = Math.Max(0L, bytePos - 1);
				byteCharacterPos = 0;
				_hexBox.SetPosition(bytePos, byteCharacterPos);
				if (bytePos < _hexBox._startByte)
				{
					_hexBox.PerformScrollLineUp();
				}
				_hexBox.UpdateCaret();
				_hexBox.ScrollByteIntoView();
				_hexBox.Invalidate();
				return true;
			}

			protected virtual bool PerformPosMoveRightByte()
			{
				long bytePos = _hexBox._bytePos;
				int byteCharacterPos = _hexBox._byteCharacterPos;
				if (bytePos == _hexBox._byteProvider.Length)
				{
					return true;
				}
				bytePos = Math.Min(_hexBox._byteProvider.Length, bytePos + 1);
				byteCharacterPos = 0;
				_hexBox.SetPosition(bytePos, byteCharacterPos);
				if (bytePos > _hexBox._endByte - 1)
				{
					_hexBox.PerformScrollLineDown();
				}
				_hexBox.UpdateCaret();
				_hexBox.ScrollByteIntoView();
				_hexBox.Invalidate();
				return true;
			}

			public virtual PointF GetCaretPointF(long byteIndex)
			{
				return _hexBox.GetBytePointF(byteIndex);
			}

			protected virtual BytePositionInfo GetBytePositionInfo(Point p)
			{
				return _hexBox.GetHexBytePositionInfo(p);
			}
		}

		private class StringKeyInterpreter : KeyInterpreter
		{
			public StringKeyInterpreter(HexBox hexBox)
				: base(hexBox)
			{
				_hexBox._byteCharacterPos = 0;
			}

			public override bool PreProcessWmKeyDown(ref Message m)
			{
				Keys keys = (Keys)m.WParam.ToInt32();
				Keys keys2 = keys | Control.ModifierKeys;
				Keys keys3 = keys2;
				if ((keys3 == Keys.Tab || keys3 == (Keys.Tab | Keys.Shift)) && RaiseKeyDown(keys2))
				{
					return true;
				}
				return keys2 switch
				{
					Keys.Tab | Keys.Shift => PreProcessWmKeyDown_ShiftTab(ref m), 
					Keys.Tab => PreProcessWmKeyDown_Tab(ref m), 
					_ => base.PreProcessWmKeyDown(ref m), 
				};
			}

			protected override bool PreProcessWmKeyDown_Left(ref Message m)
			{
				return PerformPosMoveLeftByte();
			}

			protected override bool PreProcessWmKeyDown_Right(ref Message m)
			{
				return PerformPosMoveRightByte();
			}

			public override bool PreProcessWmChar(ref Message m)
			{
				if (Control.ModifierKeys == Keys.Control)
				{
					return _hexBox.BasePreProcessMessage(ref m);
				}
				bool flag = _hexBox._byteProvider.SupportsWriteByte();
				bool flag2 = _hexBox._byteProvider.SupportsInsertBytes();
				bool flag3 = _hexBox._byteProvider.SupportsDeleteBytes();
				long bytePos = _hexBox._bytePos;
				long selectionLength = _hexBox._selectionLength;
				int byteCharacterPos = _hexBox._byteCharacterPos;
				if ((!flag && bytePos != _hexBox._byteProvider.Length) || (!flag2 && bytePos == _hexBox._byteProvider.Length))
				{
					return _hexBox.BasePreProcessMessage(ref m);
				}
				char c = (char)m.WParam.ToInt32();
				if (RaiseKeyPress(c))
				{
					return true;
				}
				if (_hexBox.ReadOnly)
				{
					return true;
				}
				bool flag4 = bytePos == _hexBox._byteProvider.Length;
				if (!flag4 && flag2 && _hexBox._insertActive)
				{
					flag4 = true;
				}
				if (flag3 && flag2 && selectionLength > 0)
				{
					_hexBox._byteProvider.DeleteBytes(bytePos, selectionLength);
					flag4 = true;
					byteCharacterPos = 0;
					_hexBox.SetPosition(bytePos, byteCharacterPos);
				}
				_hexBox.ReleaseSelection();
				if (flag4)
				{
					_hexBox._byteProvider.InsertBytes(bytePos, new byte[1] { (byte)c });
				}
				else
				{
					_hexBox._byteProvider.WriteByte(bytePos, (byte)c);
				}
				PerformPosMoveRightByte();
				_hexBox.Invalidate();
				return true;
			}

			public override PointF GetCaretPointF(long byteIndex)
			{
				Point gridBytePoint = _hexBox.GetGridBytePoint(byteIndex);
				return _hexBox.GetByteStringPointF(gridBytePoint);
			}

			protected override BytePositionInfo GetBytePositionInfo(Point p)
			{
				return _hexBox.GetStringBytePositionInfo(p);
			}
		}

		private const int THUMPTRACKDELAY = 50;

		public HexRanges Ranges = new HexRanges();

		public HexRanges HighlightRanges = new HexRanges();

		private Rectangle _recContent;

		private Rectangle _recLineInfo;

		private Rectangle _recHex;

		private Rectangle _recStringView;

		private StringFormat _stringFormat;

		private SizeF _charSize;

		private int _iHexMaxHBytes;

		private int _iHexMaxVBytes;

		private int _iHexMaxBytes;

		private long _scrollVmin;

		private long _scrollVmax;

		private long _scrollVpos;

		private VScrollBar _vScrollBar;

		private System.Windows.Forms.Timer _thumbTrackTimer = new System.Windows.Forms.Timer();

		private long _thumbTrackPosition;

		private int _lastThumbtrack;

		private int _recBorderLeft = SystemInformation.Border3DSize.Width;

		private int _recBorderRight = SystemInformation.Border3DSize.Width;

		private int _recBorderTop = SystemInformation.Border3DSize.Height;

		private int _recBorderBottom = SystemInformation.Border3DSize.Height;

		private long _startByte;

		private long _endByte;

		private long _bytePos = -1L;

		private int _byteCharacterPos;

		private string _hexStringFormat = "X";

		private IKeyInterpreter _keyInterpreter;

		private EmptyKeyInterpreter _eki;

		private KeyInterpreter _ki;

		private StringKeyInterpreter _ski;

		private bool _caretVisible;

		private bool _abortFind;

		private long _findingPos;

		private bool _insertActive;

		private Color _backColorDisabled = Color.FromName("WhiteSmoke");

		private bool _readOnly;

		private int _bytesPerLine = 16;

		private bool _useFixedBytesPerLine;

		private bool _vScrollBarVisible;

		private IByteProvider _byteProvider;

		private bool _lineInfoVisible;

		private BorderStyle _borderStyle = BorderStyle.Fixed3D;

		private bool _stringViewVisible;

		private long _selectionLength;

		private Color _lineInfoForeColor = Color.Empty;

		private Color _selectionBackColor = Color.Blue;

		private Color _selectionForeColor = Color.White;

		private bool _shadowSelectionVisible = true;

		private Color _shadowSelectionColor = Color.FromArgb(100, 60, 188, 255);

		private long _currentLine;

		private int _currentPositionInLine;

		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		[Browsable(false)]
		public long CurrentFindingPosition => _findingPos;

		[DefaultValue(typeof(Color), "White")]
		public override Color BackColor
		{
			get
			{
				return base.BackColor;
			}
			set
			{
				base.BackColor = value;
			}
		}

		[Editor(typeof(HexFontEditor), typeof(UITypeEditor))]
		public override Font Font
		{
			get
			{
				return base.Font;
			}
			set
			{
				base.Font = value;
			}
		}

		[Browsable(false)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		[EditorBrowsable(EditorBrowsableState.Never)]
		[Bindable(false)]
		public override string Text
		{
			get
			{
				return base.Text;
			}
			set
			{
				base.Text = value;
			}
		}

		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		[Bindable(false)]
		[EditorBrowsable(EditorBrowsableState.Never)]
		[Browsable(false)]
		public override RightToLeft RightToLeft
		{
			get
			{
				return base.RightToLeft;
			}
			set
			{
				base.RightToLeft = value;
			}
		}

		[DefaultValue(typeof(Color), "WhiteSmoke")]
		[Category("Appearance")]
		public Color BackColorDisabled
		{
			get
			{
				return _backColorDisabled;
			}
			set
			{
				_backColorDisabled = value;
			}
		}

		[Category("Hex")]
		[DefaultValue(false)]
		[Description("Gets or sets if the count of bytes in one line is fix.")]
		public bool ReadOnly
		{
			get
			{
				return _readOnly;
			}
			set
			{
				if (_readOnly != value)
				{
					_readOnly = value;
					OnReadOnlyChanged(EventArgs.Empty);
					Invalidate();
				}
			}
		}

		[Description("Gets or sets the maximum count of bytes in one line.")]
		[DefaultValue(16)]
		[Category("Hex")]
		public int BytesPerLine
		{
			get
			{
				return _bytesPerLine;
			}
			set
			{
				if (_bytesPerLine != value)
				{
					_bytesPerLine = value;
					OnByteProviderChanged(EventArgs.Empty);
					UpdateRectanglePositioning();
					Invalidate();
				}
			}
		}

		[Description("Gets or sets if the count of bytes in one line is fix.")]
		[Category("Hex")]
		[DefaultValue(false)]
		public bool UseFixedBytesPerLine
		{
			get
			{
				return _useFixedBytesPerLine;
			}
			set
			{
				if (_useFixedBytesPerLine != value)
				{
					_useFixedBytesPerLine = value;
					OnUseFixedBytesPerLineChanged(EventArgs.Empty);
					UpdateRectanglePositioning();
					Invalidate();
				}
			}
		}

		[Category("Hex")]
		[DefaultValue(false)]
		[Description("Gets or sets the visibility of a vertical scroll bar.")]
		public bool VScrollBarVisible
		{
			get
			{
				return _vScrollBarVisible;
			}
			set
			{
				if (_vScrollBarVisible != value)
				{
					_vScrollBarVisible = value;
					if (_vScrollBarVisible)
					{
						base.Controls.Add(_vScrollBar);
					}
					else
					{
						base.Controls.Remove(_vScrollBar);
					}
					UpdateRectanglePositioning();
					UpdateScrollSize();
					OnVScrollBarVisibleChanged(EventArgs.Empty);
				}
			}
		}

		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		[Browsable(false)]
		public IByteProvider ByteProvider
		{
			get
			{
				return _byteProvider;
			}
			set
			{
				if (_byteProvider == value)
				{
					return;
				}
				if (value == null)
				{
					ActivateEmptyKeyInterpreter();
				}
				else
				{
					ActivateKeyInterpreter();
				}
				if (_byteProvider != null)
				{
					_byteProvider.LengthChanged -= _byteProvider_LengthChanged;
				}
				_byteProvider = value;
				if (_byteProvider != null)
				{
					_byteProvider.LengthChanged += _byteProvider_LengthChanged;
				}
				OnByteProviderChanged(EventArgs.Empty);
				if (value == null)
				{
					_bytePos = -1L;
					_byteCharacterPos = 0;
					_selectionLength = 0L;
					DestroyCaret();
				}
				else
				{
					SetPosition(0L, 0);
					SetSelectionLength(0L);
					if (_caretVisible && Focused)
					{
						UpdateCaret();
					}
					else
					{
						CreateCaret();
					}
				}
				CheckCurrentLineChanged();
				CheckCurrentPositionInLineChanged();
				_scrollVpos = 0L;
				UpdateVisibilityBytes();
				UpdateRectanglePositioning();
				Invalidate();
			}
		}

		[Category("Hex")]
		[Description("Gets or sets the visibility of a line info.")]
		[DefaultValue(false)]
		public bool LineInfoVisible
		{
			get
			{
				return _lineInfoVisible;
			}
			set
			{
				if (_lineInfoVisible != value)
				{
					_lineInfoVisible = value;
					OnLineInfoVisibleChanged(EventArgs.Empty);
					UpdateRectanglePositioning();
					Invalidate();
				}
			}
		}

		[DefaultValue(typeof(BorderStyle), "Fixed3D")]
		[Description("Gets or sets the hex box\u00b4s border style.")]
		[Category("Hex")]
		public BorderStyle BorderStyle
		{
			get
			{
				return _borderStyle;
			}
			set
			{
				if (_borderStyle != value)
				{
					_borderStyle = value;
					switch (_borderStyle)
					{
					case BorderStyle.None:
						_recBorderLeft = (_recBorderTop = (_recBorderRight = (_recBorderBottom = 0)));
						break;
					case BorderStyle.Fixed3D:
						_recBorderLeft = (_recBorderRight = SystemInformation.Border3DSize.Width);
						_recBorderTop = (_recBorderBottom = SystemInformation.Border3DSize.Height);
						break;
					case BorderStyle.FixedSingle:
						_recBorderLeft = (_recBorderTop = (_recBorderRight = (_recBorderBottom = 1)));
						break;
					}
					UpdateRectanglePositioning();
					OnBorderStyleChanged(EventArgs.Empty);
				}
			}
		}

		[Description("Gets or sets the visibility of the string view.")]
		[DefaultValue(false)]
		[Category("Hex")]
		public bool StringViewVisible
		{
			get
			{
				return _stringViewVisible;
			}
			set
			{
				if (_stringViewVisible != value)
				{
					_stringViewVisible = value;
					OnStringViewVisibleChanged(EventArgs.Empty);
					UpdateRectanglePositioning();
					Invalidate();
				}
			}
		}

		[DefaultValue(typeof(HexCasing), "Upper")]
		[Category("Hex")]
		[Description("Gets or sets whether the HexBox control displays the hex characters in upper or lower case.")]
		public HexCasing HexCasing
		{
			get
			{
				if (_hexStringFormat == "X")
				{
					return HexCasing.Upper;
				}
				return HexCasing.Lower;
			}
			set
			{
				string text = ((value != HexCasing.Upper) ? "x" : "X");
				if (!(_hexStringFormat == text))
				{
					_hexStringFormat = text;
					OnHexCasingChanged(EventArgs.Empty);
					Invalidate();
				}
			}
		}

		[Browsable(false)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		public long SelectionStart
		{
			get
			{
				return _bytePos;
			}
			set
			{
				SetPosition(value, 0);
				ScrollByteIntoView();
				Invalidate();
			}
		}

		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		[Browsable(false)]
		public long SelectionLength
		{
			get
			{
				return _selectionLength;
			}
			set
			{
				SetSelectionLength(value);
				ScrollByteIntoView();
				Invalidate();
			}
		}

		[Description("Gets or sets the line info color. When this property is null, then ForeColor property is used.")]
		[Category("Hex")]
		[DefaultValue(typeof(Color), "Empty")]
		public Color LineInfoForeColor
		{
			get
			{
				return _lineInfoForeColor;
			}
			set
			{
				_lineInfoForeColor = value;
				Invalidate();
			}
		}

		[DefaultValue(typeof(Color), "Blue")]
		[Category("Hex")]
		[Description("Gets or sets the background color for the selected bytes.")]
		public Color SelectionBackColor
		{
			get
			{
				return _selectionBackColor;
			}
			set
			{
				_selectionBackColor = value;
				Invalidate();
			}
		}

		[Description("Gets or sets the foreground color for the selected bytes.")]
		[Category("Hex")]
		[DefaultValue(typeof(Color), "White")]
		public Color SelectionForeColor
		{
			get
			{
				return _selectionForeColor;
			}
			set
			{
				_selectionForeColor = value;
				Invalidate();
			}
		}

		[Category("Hex")]
		[DefaultValue(true)]
		[Description("Gets or sets the visibility of a shadow selection.")]
		public bool ShadowSelectionVisible
		{
			get
			{
				return _shadowSelectionVisible;
			}
			set
			{
				if (_shadowSelectionVisible != value)
				{
					_shadowSelectionVisible = value;
					Invalidate();
				}
			}
		}

		[Category("Hex")]
		[Description("Gets or sets the color of the shadow selection.")]
		public Color ShadowSelectionColor
		{
			get
			{
				return _shadowSelectionColor;
			}
			set
			{
				_shadowSelectionColor = value;
				Invalidate();
			}
		}

		[Browsable(false)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		public int HorizontalByteCount => _iHexMaxHBytes;

		[Browsable(false)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		public int VerticalByteCount => _iHexMaxVBytes;

		[Browsable(false)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		public long CurrentLine => _currentLine;

		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		[Browsable(false)]
		public long CurrentPositionInLine => _currentPositionInLine;

		[Browsable(false)]
		[DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
		public bool InsertActive => _insertActive;

		[Description("Occurs, when the value of InsertActive property has changed.")]
		public event EventHandler InsertActiveChanged;

		[Description("Occurs, when the value of ReadOnly property has changed.")]
		public event EventHandler ReadOnlyChanged;

		[Description("Occurs, when the value of ByteProvider property has changed.")]
		public event EventHandler ByteProviderChanged;

		[Description("Occurs, when the value of SelectionStart property has changed.")]
		public event EventHandler SelectionStartChanged;

		[Description("Occurs, when the value of SelectionLength property has changed.")]
		public event EventHandler SelectionLengthChanged;

		[Description("Occurs, when the value of LineInfoVisible property has changed.")]
		public event EventHandler LineInfoVisibleChanged;

		[Description("Occurs, when the value of StringViewVisible property has changed.")]
		public event EventHandler StringViewVisibleChanged;

		[Description("Occurs, when the value of BorderStyle property has changed.")]
		public event EventHandler BorderStyleChanged;

		[Description("Occurs, when the value of BytesPerLine property has changed.")]
		public event EventHandler BytesPerLineChanged;

		[Description("Occurs, when the value of UseFixedBytesPerLine property has changed.")]
		public event EventHandler UseFixedBytesPerLineChanged;

		[Description("Occurs, when the value of VScrollBarVisible property has changed.")]
		public event EventHandler VScrollBarVisibleChanged;

		[Description("Occurs, when the value of HexCasing property has changed.")]
		public event EventHandler HexCasingChanged;

		[Description("Occurs, when the value of HorizontalByteCount property has changed.")]
		public event EventHandler HorizontalByteCountChanged;

		[Description("Occurs, when the value of VerticalByteCount property has changed.")]
		public event EventHandler VerticalByteCountChanged;

		[Description("Occurs, when the value of VerticalByteCount property has changed.")]
		public event EventHandler VisibleBytesChanged;

		[Description("Occurs, when the value of CurrentLine property has changed.")]
		public event EventHandler CurrentLineChanged;

		[Description("Occurs, when the value of CurrentPositionInLine property has changed.")]
		public event EventHandler CurrentPositionInLineChanged;

		public HexBox()
		{
			_vScrollBar = new VScrollBar();
			_vScrollBar.Scroll += _vScrollBar_Scroll;
			BackColor = Color.White;
			Font = new Font("Courier New", 9f, FontStyle.Regular, GraphicsUnit.Point, 0);
			_stringFormat = new StringFormat(StringFormat.GenericTypographic);
			_stringFormat.FormatFlags = StringFormatFlags.MeasureTrailingSpaces;
			ActivateEmptyKeyInterpreter();
			SetStyle(ControlStyles.UserPaint, value: true);
			SetStyle(ControlStyles.DoubleBuffer, value: true);
			SetStyle(ControlStyles.AllPaintingInWmPaint, value: true);
			SetStyle(ControlStyles.ResizeRedraw, value: true);
			_thumbTrackTimer.Interval = 50;
			_thumbTrackTimer.Tick += PerformScrollThumbTrack;
		}

		private void _vScrollBar_Scroll(object sender, ScrollEventArgs e)
		{
			switch (e.Type)
			{
			case ScrollEventType.SmallIncrement:
				PerformScrollLineDown();
				break;
			case ScrollEventType.SmallDecrement:
				PerformScrollLineUp();
				break;
			case ScrollEventType.LargeIncrement:
				PerformScrollPageDown();
				break;
			case ScrollEventType.LargeDecrement:
				PerformScrollPageUp();
				break;
			case ScrollEventType.ThumbPosition:
			{
				long pos = FromScrollPos(e.NewValue);
				PerformScrollThumpPosition(pos);
				break;
			}
			case ScrollEventType.ThumbTrack:
			{
				if (_thumbTrackTimer.Enabled)
				{
					_thumbTrackTimer.Enabled = false;
				}
				_thumbTrackPosition = FromScrollPos(e.NewValue);
				int tickCount = Environment.TickCount;
				if (tickCount - _lastThumbtrack > 50)
				{
					PerformScrollThumbTrack(null, null);
					_lastThumbtrack = tickCount;
				}
				else
				{
					_thumbTrackTimer.Enabled = true;
				}
				break;
			}
			}
			e.NewValue = ToScrollPos(_scrollVpos);
		}

		private void PerformScrollThumbTrack(object sender, EventArgs e)
		{
			_thumbTrackTimer.Enabled = false;
			PerformScrollThumpPosition(_thumbTrackPosition);
			_lastThumbtrack = Environment.TickCount;
		}

		private void UpdateScrollSize()
		{
			if (VScrollBarVisible && _byteProvider != null && _byteProvider.Length > 0 && _iHexMaxHBytes != 0)
			{
				long val = (long)Math.Ceiling((double)_byteProvider.Length / (double)_iHexMaxHBytes - (double)_iHexMaxVBytes);
				val = Math.Max(0L, val);
				long num = _startByte / _iHexMaxHBytes;
				if (val != _scrollVmax || num != _scrollVpos)
				{
					_scrollVmin = 0L;
					_scrollVmax = val;
					_scrollVpos = Math.Min(num, val);
					UpdateVScroll();
				}
			}
			else if (VScrollBarVisible)
			{
				_scrollVmin = 0L;
				_scrollVmax = 0L;
				_scrollVpos = 0L;
				UpdateVScroll();
			}
		}

		private void UpdateVScroll()
		{
			int num = ToScrollMax(_scrollVmax);
			if (num > 0)
			{
				_vScrollBar.Minimum = 0;
				_vScrollBar.Maximum = num;
				_vScrollBar.Value = ToScrollPos(_scrollVpos);
				_vScrollBar.Enabled = true;
			}
			else
			{
				_vScrollBar.Enabled = false;
			}
		}

		private int ToScrollPos(long value)
		{
			int num = 65535;
			if (_scrollVmax < num)
			{
				return (int)value;
			}
			double num2 = (double)value / (double)_scrollVmax * 100.0;
			int num3 = (int)Math.Floor((double)num / 100.0 * num2);
			num3 = (int)Math.Max(_scrollVmin, num3);
			return (int)Math.Min(_scrollVmax, num3);
		}

		private long FromScrollPos(int value)
		{
			int num = 65535;
			if (_scrollVmax < num)
			{
				return value;
			}
			double num2 = (double)value / (double)num * 100.0;
			return (int)Math.Floor((double)_scrollVmax / 100.0 * num2);
		}

		private int ToScrollMax(long value)
		{
			long num = 65535L;
			if (value > num)
			{
				return (int)num;
			}
			return (int)value;
		}

		private void PerformScrollToLine(long pos)
		{
			if (pos < _scrollVmin)
			{
				pos = _scrollVmin;
			}
			if (pos > _scrollVmax)
			{
				pos = _scrollVmax;
			}
			if (pos != _scrollVpos)
			{
				_scrollVpos = pos;
				UpdateVScroll();
				UpdateVisibilityBytes();
				UpdateCaret();
				Invalidate();
			}
		}

		private void PerformScrollLines(int lines)
		{
			long pos;
			if (lines > 0)
			{
				pos = Math.Min(_scrollVmax, _scrollVpos + lines);
			}
			else
			{
				if (lines >= 0)
				{
					return;
				}
				pos = Math.Max(_scrollVmin, _scrollVpos + lines);
			}
			PerformScrollToLine(pos);
		}

		private void PerformScrollLineDown()
		{
			PerformScrollLines(1);
		}

		private void PerformScrollLineUp()
		{
			PerformScrollLines(-1);
		}

		private void PerformScrollPageDown()
		{
			PerformScrollLines(_iHexMaxVBytes);
		}

		private void PerformScrollPageUp()
		{
			PerformScrollLines(-_iHexMaxVBytes);
		}

		private void PerformScrollThumpPosition(long pos)
		{
			int num = ((_scrollVmax > 65535) ? 10 : 9);
			if (ToScrollPos(pos) == ToScrollMax(_scrollVmax) - num)
			{
				pos = _scrollVmax;
			}
			PerformScrollToLine(pos);
		}

		public void ScrollByteIntoView()
		{
			ScrollByteIntoView(_bytePos);
		}

		public void ScrollByteIntoView(long index)
		{
			if (_byteProvider != null && _keyInterpreter != null)
			{
				if (index < _startByte)
				{
					long pos = (long)Math.Floor((double)index / (double)_iHexMaxHBytes);
					PerformScrollToLine(pos);
				}
				else if (index > _endByte)
				{
					long num = (long)Math.Floor((double)index / (double)_iHexMaxHBytes);
					num -= _iHexMaxVBytes - 1;
					PerformScrollToLine(num);
				}
			}
		}

		public void ScrollByteIntoCenter(long index)
		{
			if (_byteProvider != null && _keyInterpreter != null)
			{
				long pos = (long)Math.Floor((double)index / (double)_iHexMaxHBytes) - _iHexMaxVBytes / 2;
				PerformScrollToLine(pos);
			}
		}

		public bool IsRangeVisible(long start, long length)
		{
			if (start < _endByte && start + length > _startByte)
			{
				return true;
			}
			return false;
		}

		public bool IsSelectionVisible()
		{
			return IsRangeVisible(_bytePos, _selectionLength);
		}

		private void ReleaseSelection()
		{
			if (_selectionLength != 0)
			{
				_selectionLength = 0L;
				OnSelectionLengthChanged(EventArgs.Empty);
				if (!_caretVisible)
				{
					CreateCaret();
				}
				else
				{
					UpdateCaret();
				}
				Invalidate();
			}
		}

		public void Select(long start, long length)
		{
			InternalSelect(start, length);
			ScrollByteIntoView();
		}

		private void InternalSelect(long start, long length)
		{
			int byteCharacterPos = 0;
			if (length > 0 && _caretVisible)
			{
				DestroyCaret();
			}
			else if (length == 0 && !_caretVisible)
			{
				CreateCaret();
			}
			SetPosition(start, byteCharacterPos);
			SetSelectionLength(length);
			UpdateCaret();
			Invalidate();
		}

		private void ActivateEmptyKeyInterpreter()
		{
			if (_eki == null)
			{
				_eki = new EmptyKeyInterpreter(this);
			}
			if (_eki != _keyInterpreter)
			{
				if (_keyInterpreter != null)
				{
					_keyInterpreter.Deactivate();
				}
				_keyInterpreter = _eki;
				_keyInterpreter.Activate();
			}
		}

		private void ActivateKeyInterpreter()
		{
			if (_ki == null)
			{
				_ki = new KeyInterpreter(this);
			}
			if (_ki != _keyInterpreter)
			{
				if (_keyInterpreter != null)
				{
					_keyInterpreter.Deactivate();
				}
				_keyInterpreter = _ki;
				_keyInterpreter.Activate();
			}
		}

		private void ActivateStringKeyInterpreter()
		{
			if (_ski == null)
			{
				_ski = new StringKeyInterpreter(this);
			}
			if (_ski != _keyInterpreter)
			{
				if (_keyInterpreter != null)
				{
					_keyInterpreter.Deactivate();
				}
				_keyInterpreter = _ski;
				_keyInterpreter.Activate();
			}
		}

		private void CreateCaret()
		{
			if (_byteProvider != null && _keyInterpreter != null && !_caretVisible && Focused)
			{
				NativeMethods.CreateCaret(base.Handle, IntPtr.Zero, 1, (int)_charSize.Height);
				UpdateCaret();
				NativeMethods.ShowCaret(base.Handle);
				_caretVisible = true;
			}
		}

		private void UpdateCaret()
		{
			if (_byteProvider != null && _keyInterpreter != null)
			{
				long byteIndex = _bytePos - _startByte;
				PointF caretPointF = _keyInterpreter.GetCaretPointF(byteIndex);
				caretPointF.X += (float)_byteCharacterPos * _charSize.Width;
				NativeMethods.SetCaretPos((int)caretPointF.X, (int)caretPointF.Y);
			}
		}

		private void DestroyCaret()
		{
			if (_caretVisible)
			{
				NativeMethods.DestroyCaret();
				_caretVisible = false;
			}
		}

		private void SetCaretPosition(Point p)
		{
			if (_byteProvider != null && _keyInterpreter != null)
			{
				long bytePos = _bytePos;
				int byteCharacterPos = _byteCharacterPos;
				if (_recHex.Contains(p))
				{
					BytePositionInfo hexBytePositionInfo = GetHexBytePositionInfo(p);
					bytePos = hexBytePositionInfo.Index;
					byteCharacterPos = hexBytePositionInfo.CharacterPosition;
					SetPosition(bytePos, byteCharacterPos);
					ActivateKeyInterpreter();
					UpdateCaret();
					Invalidate();
				}
				else if (_recStringView.Contains(p))
				{
					BytePositionInfo stringBytePositionInfo = GetStringBytePositionInfo(p);
					bytePos = stringBytePositionInfo.Index;
					byteCharacterPos = stringBytePositionInfo.CharacterPosition;
					SetPosition(bytePos, byteCharacterPos);
					ActivateStringKeyInterpreter();
					UpdateCaret();
					Invalidate();
				}
			}
		}

		private BytePositionInfo GetHexBytePositionInfo(Point p)
		{
			float num = (float)(p.X - _recHex.X) / _charSize.Width;
			float num2 = (float)(p.Y - _recHex.Y) / _charSize.Height;
			int num3 = (int)num;
			int num4 = (int)num2;
			int num5 = num3 / 3 + 1;
			long num6 = Math.Min(_byteProvider.Length, _startByte + (_iHexMaxHBytes * (num4 + 1) - _iHexMaxHBytes) + num5 - 1);
			int num7 = num3 % 3;
			if (num7 > 1)
			{
				num7 = 1;
			}
			if (num6 == _byteProvider.Length)
			{
				num7 = 0;
			}
			if (num6 < 0)
			{
				return new BytePositionInfo(0L, 0);
			}
			return new BytePositionInfo(num6, num7);
		}

		private BytePositionInfo GetStringBytePositionInfo(Point p)
		{
			float num = (float)(p.X - _recStringView.X) / _charSize.Width;
			float num2 = (float)(p.Y - _recStringView.Y) / _charSize.Height;
			int num3 = (int)num;
			int num4 = (int)num2;
			int num5 = num3 + 1;
			long num6 = Math.Min(_byteProvider.Length, _startByte + (_iHexMaxHBytes * (num4 + 1) - _iHexMaxHBytes) + num5 - 1);
			int characterPosition = 0;
			if (num6 < 0)
			{
				return new BytePositionInfo(0L, 0);
			}
			return new BytePositionInfo(num6, characterPosition);
		}

		[SecurityPermission(SecurityAction.LinkDemand, UnmanagedCode = true)]
		[SecurityPermission(SecurityAction.InheritanceDemand, UnmanagedCode = true)]
		public override bool PreProcessMessage(ref Message m)
		{
			return m.Msg switch
			{
				256 => _keyInterpreter.PreProcessWmKeyDown(ref m), 
				258 => _keyInterpreter.PreProcessWmChar(ref m), 
				257 => _keyInterpreter.PreProcessWmKeyUp(ref m), 
				_ => base.PreProcessMessage(ref m), 
			};
		}

		private bool BasePreProcessMessage(ref Message m)
		{
			return base.PreProcessMessage(ref m);
		}

		public long Find(byte[] bytes, long startIndex, int direction)
		{
			int num = 0;
			int num2 = bytes.Length;
			_abortFind = false;
			for (long num3 = startIndex; num3 >= 0 && num3 < _byteProvider.Length; num3 += direction)
			{
				if (_abortFind)
				{
					return -2L;
				}
				if (num3 % 1000 == 0)
				{
					Application.DoEvents();
				}
				if (_byteProvider.ReadByte(num3) != bytes[num])
				{
					num3 -= num;
					num = 0;
					_findingPos = num3;
					continue;
				}
				num++;
				if (num == num2)
				{
					long num4 = num3 - num2 + 1;
					Select(num4, num2);
					ScrollByteIntoView(_bytePos + _selectionLength);
					ScrollByteIntoView(_bytePos);
					return num4;
				}
			}
			return -1L;
		}

		public void AbortFind()
		{
			_abortFind = true;
		}

		public void Copy()
		{
			if (CanCopy())
			{
				byte[] array = new byte[_selectionLength];
				int num = -1;
				for (long num2 = _bytePos; num2 < _bytePos + _selectionLength; num2++)
				{
					num++;
					array[num] = _byteProvider.ReadByte(num2);
				}
				DataObject dataObject = new DataObject();
				string data = Encoding.ASCII.GetString(array, 0, array.Length);
				dataObject.SetData(typeof(string), data);
				MemoryStream data2 = new MemoryStream(array, 0, array.Length, writable: false, publiclyVisible: true);
				dataObject.SetData("BinaryData", data2);
				Clipboard.SetDataObject(dataObject, copy: true);
				UpdateCaret();
				ScrollByteIntoView();
				Invalidate();
			}
		}

		public bool CanCopy()
		{
			if (_selectionLength < 1 || _byteProvider == null)
			{
				return false;
			}
			return true;
		}

		public void Cut()
		{
			if (CanCut())
			{
				Copy();
				_byteProvider.DeleteBytes(_bytePos, _selectionLength);
				_byteCharacterPos = 0;
				UpdateCaret();
				ScrollByteIntoView();
				ReleaseSelection();
				Invalidate();
				Refresh();
			}
		}

		public bool CanCut()
		{
			if (ReadOnly || !base.Enabled)
			{
				return false;
			}
			if (_byteProvider == null)
			{
				return false;
			}
			if (_selectionLength < 1 || !_byteProvider.SupportsDeleteBytes())
			{
				return false;
			}
			return true;
		}

		public void Paste()
		{
			if (!CanPaste())
			{
				return;
			}
			if (_selectionLength > 0)
			{
				_byteProvider.DeleteBytes(_bytePos, _selectionLength);
			}
			byte[] array = null;
			IDataObject dataObject = Clipboard.GetDataObject();
			if (dataObject.GetDataPresent("BinaryData"))
			{
				MemoryStream memoryStream = (MemoryStream)dataObject.GetData("BinaryData");
				array = new byte[memoryStream.Length];
				memoryStream.Read(array, 0, array.Length);
			}
			else
			{
				if (!dataObject.GetDataPresent(typeof(string)))
				{
					return;
				}
				string s = (string)dataObject.GetData(typeof(string));
				array = Encoding.ASCII.GetBytes(s);
			}
			_byteProvider.InsertBytes(_bytePos, array);
			SetPosition(_bytePos + array.Length, 0);
			ReleaseSelection();
			ScrollByteIntoView();
			UpdateCaret();
			Invalidate();
		}

		public bool CanPaste()
		{
			if (ReadOnly || !base.Enabled)
			{
				return false;
			}
			if (_byteProvider == null || !_byteProvider.SupportsInsertBytes())
			{
				return false;
			}
			if (!_byteProvider.SupportsDeleteBytes() && _selectionLength > 0)
			{
				return false;
			}
			IDataObject dataObject = Clipboard.GetDataObject();
			if (dataObject.GetDataPresent("BinaryData"))
			{
				return true;
			}
			if (dataObject.GetDataPresent(typeof(string)))
			{
				return true;
			}
			return false;
		}

		protected override void OnPaintBackground(PaintEventArgs e)
		{
			switch (_borderStyle)
			{
			case BorderStyle.Fixed3D:
				if (TextBoxRenderer.IsSupported)
				{
					VisualStyleElement element = VisualStyleElement.TextBox.TextEdit.Normal;
					Color color = BackColor;
					if (base.Enabled)
					{
						if (ReadOnly)
						{
							element = VisualStyleElement.TextBox.TextEdit.ReadOnly;
						}
						else if (Focused)
						{
							element = VisualStyleElement.TextBox.TextEdit.Focused;
						}
					}
					else
					{
						element = VisualStyleElement.TextBox.TextEdit.Disabled;
						color = BackColorDisabled;
					}
					VisualStyleRenderer visualStyleRenderer = new VisualStyleRenderer(element);
					visualStyleRenderer.DrawBackground(e.Graphics, base.ClientRectangle);
					Rectangle backgroundContentRectangle = visualStyleRenderer.GetBackgroundContentRectangle(e.Graphics, base.ClientRectangle);
					e.Graphics.FillRectangle(new SolidBrush(color), backgroundContentRectangle);
				}
				else
				{
					e.Graphics.FillRectangle(new SolidBrush(BackColor), base.ClientRectangle);
					ControlPaint.DrawBorder3D(e.Graphics, base.ClientRectangle, Border3DStyle.Sunken);
				}
				break;
			case BorderStyle.FixedSingle:
				e.Graphics.FillRectangle(new SolidBrush(BackColor), base.ClientRectangle);
				ControlPaint.DrawBorder(e.Graphics, base.ClientRectangle, Color.Black, ButtonBorderStyle.Solid);
				break;
			case BorderStyle.None:
				e.Graphics.FillRectangle(new SolidBrush(BackColor), base.ClientRectangle);
				break;
			}
		}

		protected override void OnPaint(PaintEventArgs e)
		{
			base.OnPaint(e);
			if (_byteProvider != null)
			{
				Region region = new Region(base.ClientRectangle);
				region.Exclude(_recContent);
				e.Graphics.ExcludeClip(region);
				UpdateVisibilityBytes();
				if (_lineInfoVisible)
				{
					PaintLineInfo(e.Graphics, _startByte, _endByte);
				}
				if (!_stringViewVisible)
				{
					PaintHex(e.Graphics, _startByte, _endByte);
				}
				else
				{
					PaintHexAndStringView(e.Graphics, _startByte, _endByte);
				}
			}
		}

		private void PaintLineInfo(Graphics g, long startByte, long endByte)
		{
			endByte = Math.Min(_byteProvider.Length - 1, endByte);
			Color color = ((LineInfoForeColor != Color.Empty) ? LineInfoForeColor : ForeColor);
			Brush brush = new SolidBrush(color);
			int num = GetGridBytePoint(endByte - startByte).Y + 1;
			for (int i = 0; i < num; i++)
			{
				long num2 = startByte + _iHexMaxHBytes * i;
				PointF bytePointF = GetBytePointF(new Point(0, i));
				string text = num2.ToString(_hexStringFormat, Thread.CurrentThread.CurrentCulture);
				int num3 = 8 - text.Length;
				string s = ((num3 <= -1) ? new string('~', 8) : (new string('0', 8 - text.Length) + text));
				g.DrawString(s, Font, brush, new PointF(_recLineInfo.X, bytePointF.Y), _stringFormat);
			}
		}

		private void PaintHex(Graphics g, long startByte, long endByte)
		{
			Brush brush = new SolidBrush(GetDefaultForeColor());
			Brush brush2 = new SolidBrush(_selectionForeColor);
			Brush brushBack = new SolidBrush(_selectionBackColor);
			int num = -1;
			long num2 = Math.Min(_byteProvider.Length - 1, endByte + _iHexMaxHBytes);
			bool flag = _keyInterpreter == null || _keyInterpreter.GetType() == typeof(KeyInterpreter);
			for (long num3 = startByte; num3 < num2 + 1; num3++)
			{
				num++;
				Point gridBytePoint = GetGridBytePoint(num);
				byte b = _byteProvider.ReadByte(num3);
				if (num3 >= _bytePos && num3 <= _bytePos + _selectionLength - 1 && _selectionLength != 0 && flag)
				{
					PaintHexStringSelected(g, b, brush2, brushBack, gridBytePoint);
				}
				else
				{
					PaintHexString(g, b, brush, gridBytePoint);
				}
			}
		}

		private void PaintHexString(Graphics g, byte b, Brush brush, Point gridPoint)
		{
			PointF bytePointF = GetBytePointF(gridPoint);
			string text = b.ToString(_hexStringFormat, Thread.CurrentThread.CurrentCulture);
			if (text.Length == 1)
			{
				text = "0" + text;
			}
			g.DrawString(text.Substring(0, 1), Font, brush, bytePointF, _stringFormat);
			bytePointF.X += _charSize.Width;
			g.DrawString(text.Substring(1, 1), Font, brush, bytePointF, _stringFormat);
		}

		private void PaintHexStringSelected(Graphics g, byte b, Brush brush, Brush brushBack, Point gridPoint)
		{
			string text = b.ToString(_hexStringFormat, Thread.CurrentThread.CurrentCulture);
			if (text.Length == 1)
			{
				text = "0" + text;
			}
			PointF bytePointF = GetBytePointF(gridPoint);
			float num = ((gridPoint.X + 1 == _iHexMaxHBytes) ? (_charSize.Width * 2f) : (_charSize.Width * 3f));
			g.FillRectangle(brushBack, bytePointF.X, bytePointF.Y, num, _charSize.Height);
			g.DrawString(text.Substring(0, 1), Font, brush, bytePointF, _stringFormat);
			bytePointF.X += _charSize.Width;
			g.DrawString(text.Substring(1, 1), Font, brush, bytePointF, _stringFormat);
		}

		public int GetLineCount()
		{
			if (ByteProvider != null)
			{
				return (int)(ByteProvider.Length / BytesPerLine + 1);
			}
			return 1;
		}

		public int GetVisibleLineCount()
		{
			return (int)((_endByte - _startByte) / BytesPerLine + 1);
		}

		public int GetFirstVisibleLine()
		{
			return (int)(_startByte / BytesPerLine);
		}

		private void DrawRanges(Bitmap bmp, Graphics g, HexRanges ranges)
		{
			foreach (HexRange range in ranges.Ranges)
			{
				int num = (int)range.Start / BytesPerLine;
				int num2 = (int)range.Start % BytesPerLine;
				int num3 = (int)range.End % BytesPerLine;
				int num4 = (int)range.End / BytesPerLine;
				float num5 = 1f;
				if (ByteProvider != null)
				{
					num5 = (float)bmp.Height / (float)GetLineCount();
				}
				if (num == num4)
				{
					g.FillRectangle(range.BackgroundColor, num2, (float)num * num5, num3 + 1, 1f * num5);
					continue;
				}
				g.FillRectangle(range.BackgroundColor, num2, (float)num * num5, BytesPerLine - num2, 1f * num5);
				if (num + 1 < num4)
				{
					g.FillRectangle(range.BackgroundColor, 0f, (float)(num + 1) * num5, BytesPerLine, (float)(num4 - num + 1) * num5);
				}
				g.FillRectangle(range.BackgroundColor, 0f, (float)num4 * num5, num3 + 1, 1f * num5);
			}
		}

		public void DrawRanges(Bitmap bmp)
		{
			using Graphics graphics = Graphics.FromImage(bmp);
			graphics.Clear(Color.White);
			DrawRanges(bmp, graphics, Ranges);
			DrawRanges(bmp, graphics, HighlightRanges);
		}

		private void GetByteView(long index, ref ByteView view, bool stringView)
		{
			view.ForegroundColor = new SolidBrush(GetDefaultForeColor());
			view.BackgroundColor = new SolidBrush(Color.Transparent);
			Ranges.GetByteView(index, ref view);
			HighlightRanges.GetByteView(index, ref view);
			bool flag = _keyInterpreter == null || _keyInterpreter.GetType() == typeof(KeyInterpreter);
			bool flag2 = _keyInterpreter != null && _keyInterpreter.GetType() == typeof(StringKeyInterpreter);
			bool flag3 = (flag && !stringView) || (flag2 && stringView);
			if ((index >= _bytePos && index <= _bytePos + _selectionLength - 1 && _selectionLength != 0) || (!flag3 && index == _bytePos))
			{
				Color color = _selectionForeColor;
				Color color2 = _selectionBackColor;
				if (!flag3 && ShadowSelectionVisible)
				{
					color2 = _shadowSelectionColor;
					color = Color.Black;
				}
				view.ForegroundColor = new SolidBrush(color);
				view.BackgroundColor = new SolidBrush(color2);
			}
		}

		private void PaintHexAndStringView(Graphics g, long startByte, long endByte)
		{
			new SolidBrush(GetDefaultForeColor());
			new SolidBrush(_selectionForeColor);
			new SolidBrush(_selectionBackColor);
			int num = -1;
			long num2 = Math.Min(_byteProvider.Length - 1, endByte + _iHexMaxHBytes);
			for (long num3 = startByte; num3 < num2 + 1; num3++)
			{
				num++;
				Point gridBytePoint = GetGridBytePoint(num);
				PointF byteStringPointF = GetByteStringPointF(gridBytePoint);
				byte b = _byteProvider.ReadByte(num3);
				ByteView view = new ByteView();
				GetByteView(num3, ref view, stringView: false);
				PaintHexStringSelected(g, b, view.ForegroundColor, view.BackgroundColor, gridBytePoint);
				string s;
				if (b > 31 && (b <= 126 || b >= 160))
				{
					char c = (char)b;
					s = c.ToString();
				}
				else
				{
					s = ".";
				}
				GetByteView(num3, ref view, stringView: true);
				g.FillRectangle(view.BackgroundColor, byteStringPointF.X, byteStringPointF.Y, _charSize.Width, _charSize.Height);
				g.DrawString(s, Font, view.ForegroundColor, byteStringPointF, _stringFormat);
			}
		}

		private void PaintCurrentBytesSign(Graphics g)
		{
			if (_keyInterpreter == null || !Focused || _bytePos == -1 || !base.Enabled)
			{
				return;
			}
			if (_keyInterpreter.GetType() == typeof(KeyInterpreter))
			{
				if (_selectionLength == 0)
				{
					Point gridBytePoint = GetGridBytePoint(_bytePos - _startByte);
					PointF byteStringPointF = GetByteStringPointF(gridBytePoint);
					Size size = new Size((int)_charSize.Width, (int)_charSize.Height);
					Rectangle rec = new Rectangle((int)byteStringPointF.X, (int)byteStringPointF.Y, size.Width, size.Height);
					if (rec.IntersectsWith(_recStringView))
					{
						rec.Intersect(_recStringView);
						PaintCurrentByteSign(g, rec);
					}
					return;
				}
				int num = (int)((float)_recStringView.Width - _charSize.Width);
				Point gridBytePoint2 = GetGridBytePoint(_bytePos - _startByte);
				PointF byteStringPointF2 = GetByteStringPointF(gridBytePoint2);
				Point gridBytePoint3 = GetGridBytePoint(_bytePos - _startByte + _selectionLength - 1);
				PointF byteStringPointF3 = GetByteStringPointF(gridBytePoint3);
				int num2 = gridBytePoint3.Y - gridBytePoint2.Y;
				if (num2 == 0)
				{
					Rectangle rec2 = new Rectangle((int)byteStringPointF2.X, (int)byteStringPointF2.Y, (int)(byteStringPointF3.X - byteStringPointF2.X + _charSize.Width), (int)_charSize.Height);
					if (rec2.IntersectsWith(_recStringView))
					{
						rec2.Intersect(_recStringView);
						PaintCurrentByteSign(g, rec2);
					}
					return;
				}
				Rectangle rec3 = new Rectangle((int)byteStringPointF2.X, (int)byteStringPointF2.Y, (int)((float)(_recStringView.X + num) - byteStringPointF2.X + _charSize.Width), (int)_charSize.Height);
				if (rec3.IntersectsWith(_recStringView))
				{
					rec3.Intersect(_recStringView);
					PaintCurrentByteSign(g, rec3);
				}
				if (num2 > 1)
				{
					Rectangle rec4 = new Rectangle(_recStringView.X, (int)(byteStringPointF2.Y + _charSize.Height), _recStringView.Width, (int)(_charSize.Height * (float)(num2 - 1)));
					if (rec4.IntersectsWith(_recStringView))
					{
						rec4.Intersect(_recStringView);
						PaintCurrentByteSign(g, rec4);
					}
				}
				Rectangle rec5 = new Rectangle(_recStringView.X, (int)byteStringPointF3.Y, (int)(byteStringPointF3.X - (float)_recStringView.X + _charSize.Width), (int)_charSize.Height);
				if (rec5.IntersectsWith(_recStringView))
				{
					rec5.Intersect(_recStringView);
					PaintCurrentByteSign(g, rec5);
				}
				return;
			}
			if (_selectionLength == 0)
			{
				Point gridBytePoint4 = GetGridBytePoint(_bytePos - _startByte);
				PointF bytePointF = GetBytePointF(gridBytePoint4);
				Size size2 = new Size((int)_charSize.Width * 2, (int)_charSize.Height);
				Rectangle rec6 = new Rectangle((int)bytePointF.X, (int)bytePointF.Y, size2.Width, size2.Height);
				PaintCurrentByteSign(g, rec6);
				return;
			}
			int num3 = (int)((float)_recHex.Width - _charSize.Width * 5f);
			Point gridBytePoint5 = GetGridBytePoint(_bytePos - _startByte);
			PointF bytePointF2 = GetBytePointF(gridBytePoint5);
			Point gridBytePoint6 = GetGridBytePoint(_bytePos - _startByte + _selectionLength - 1);
			PointF bytePointF3 = GetBytePointF(gridBytePoint6);
			int num4 = gridBytePoint6.Y - gridBytePoint5.Y;
			if (num4 == 0)
			{
				Rectangle rec7 = new Rectangle((int)bytePointF2.X, (int)bytePointF2.Y, (int)(bytePointF3.X - bytePointF2.X + _charSize.Width * 2f), (int)_charSize.Height);
				if (rec7.IntersectsWith(_recHex))
				{
					rec7.Intersect(_recHex);
					PaintCurrentByteSign(g, rec7);
				}
				return;
			}
			Rectangle rec8 = new Rectangle((int)bytePointF2.X, (int)bytePointF2.Y, (int)((float)(_recHex.X + num3) - bytePointF2.X + _charSize.Width * 2f), (int)_charSize.Height);
			if (rec8.IntersectsWith(_recHex))
			{
				rec8.Intersect(_recHex);
				PaintCurrentByteSign(g, rec8);
			}
			if (num4 > 1)
			{
				Rectangle rec9 = new Rectangle(_recHex.X, (int)(bytePointF2.Y + _charSize.Height), (int)((float)num3 + _charSize.Width * 2f), (int)(_charSize.Height * (float)(num4 - 1)));
				if (rec9.IntersectsWith(_recHex))
				{
					rec9.Intersect(_recHex);
					PaintCurrentByteSign(g, rec9);
				}
			}
			Rectangle rec10 = new Rectangle(_recHex.X, (int)bytePointF3.Y, (int)(bytePointF3.X - (float)_recHex.X + _charSize.Width * 2f), (int)_charSize.Height);
			if (rec10.IntersectsWith(_recHex))
			{
				rec10.Intersect(_recHex);
				PaintCurrentByteSign(g, rec10);
			}
		}

		private void PaintCurrentByteSign(Graphics g, Rectangle rec)
		{
			if (rec.Top >= 0 && rec.Left >= 0 && rec.Width > 0 && rec.Height > 0)
			{
				Bitmap image = new Bitmap(rec.Width, rec.Height);
				Graphics graphics = Graphics.FromImage(image);
				SolidBrush brush = new SolidBrush(_shadowSelectionColor);
				graphics.FillRectangle(brush, 0, 0, rec.Width, rec.Height);
				g.CompositingQuality = CompositingQuality.GammaCorrected;
				g.DrawImage(image, rec.Left, rec.Top);
			}
		}

		private Color GetDefaultForeColor()
		{
			if (base.Enabled)
			{
				return ForeColor;
			}
			return Color.Gray;
		}

		private void UpdateVisibilityBytes()
		{
			if (_byteProvider != null && _byteProvider.Length != 0)
			{
				_startByte = (_scrollVpos + 1) * _iHexMaxHBytes - _iHexMaxHBytes;
				_endByte = Math.Min(_byteProvider.Length - 1, _startByte + _iHexMaxBytes);
				OnVisibleBytesChanged(new EventArgs());
			}
		}

		private void UpdateRectanglePositioning()
		{
			SizeF sizeF = CreateGraphics().MeasureString("A", Font, 100, _stringFormat);
			_charSize = new SizeF((float)Math.Ceiling(sizeF.Width), (float)Math.Ceiling(sizeF.Height));
			_recContent = base.ClientRectangle;
			_recContent.X += _recBorderLeft;
			_recContent.Y += _recBorderTop;
			_recContent.Width -= _recBorderRight + _recBorderLeft;
			_recContent.Height -= _recBorderBottom + _recBorderTop;
			if (_vScrollBarVisible)
			{
				_recContent.Width -= _vScrollBar.Width;
				_vScrollBar.Left = _recContent.X + _recContent.Width;
				_vScrollBar.Top = _recContent.Y;
				_vScrollBar.Height = _recContent.Height;
			}
			int num = 4;
			if (_lineInfoVisible)
			{
				_recLineInfo = new Rectangle(_recContent.X + num, _recContent.Y, (int)(_charSize.Width * 10f), _recContent.Height);
			}
			else
			{
				_recLineInfo = Rectangle.Empty;
				_recLineInfo.X = num;
			}
			_recHex = new Rectangle(_recLineInfo.X + _recLineInfo.Width, _recLineInfo.Y, _recContent.Width - _recLineInfo.Width, _recContent.Height);
			if (UseFixedBytesPerLine)
			{
				SetHorizontalByteCount(_bytesPerLine);
				_recHex.Width = (int)Math.Floor((double)_iHexMaxHBytes * (double)_charSize.Width * 3.0 + (double)(2f * _charSize.Width));
			}
			else
			{
				int num2 = (int)Math.Floor((double)_recHex.Width / (double)_charSize.Width);
				if (num2 > 1)
				{
					SetHorizontalByteCount((int)Math.Floor((double)num2 / 3.0));
				}
				else
				{
					SetHorizontalByteCount(num2);
				}
			}
			if (_stringViewVisible)
			{
				_recStringView = new Rectangle(_recHex.X + _recHex.Width, _recHex.Y, (int)(_charSize.Width * (float)_iHexMaxHBytes), _recHex.Height);
			}
			else
			{
				_recStringView = Rectangle.Empty;
			}
			int verticalByteCount = (int)Math.Floor((double)_recHex.Height / (double)_charSize.Height);
			SetVerticalByteCount(verticalByteCount);
			_iHexMaxBytes = _iHexMaxHBytes * _iHexMaxVBytes;
			UpdateScrollSize();
		}

		private PointF GetBytePointF(long byteIndex)
		{
			Point gridBytePoint = GetGridBytePoint(byteIndex);
			return GetBytePointF(gridBytePoint);
		}

		private PointF GetBytePointF(Point gp)
		{
			float num = 3f * _charSize.Width * (float)gp.X + (float)_recHex.X;
			float num2 = (float)(gp.Y + 1) * _charSize.Height - _charSize.Height + (float)_recHex.Y;
			return new PointF(num, num2);
		}

		private PointF GetByteStringPointF(Point gp)
		{
			float num = _charSize.Width * (float)gp.X + (float)_recStringView.X;
			float num2 = (float)(gp.Y + 1) * _charSize.Height - _charSize.Height + (float)_recStringView.Y;
			return new PointF(num, num2);
		}

		private Point GetGridBytePoint(long byteIndex)
		{
			int num = (int)Math.Floor((double)byteIndex / (double)_iHexMaxHBytes);
			int num2 = (int)(byteIndex + _iHexMaxHBytes - _iHexMaxHBytes * (num + 1));
			return new Point(num2, num);
		}

		private void SetPosition(long bytePos)
		{
			SetPosition(bytePos, _byteCharacterPos);
		}

		private void SetPosition(long bytePos, int byteCharacterPos)
		{
			if (_byteCharacterPos != byteCharacterPos)
			{
				_byteCharacterPos = byteCharacterPos;
			}
			if (bytePos != _bytePos)
			{
				_bytePos = bytePos;
				CheckCurrentLineChanged();
				CheckCurrentPositionInLineChanged();
				OnSelectionStartChanged(EventArgs.Empty);
			}
		}

		private void SetSelectionLength(long selectionLength)
		{
			if (selectionLength != _selectionLength)
			{
				_selectionLength = selectionLength;
				OnSelectionLengthChanged(EventArgs.Empty);
			}
		}

		private void SetHorizontalByteCount(int value)
		{
			if (_iHexMaxHBytes != value)
			{
				_iHexMaxHBytes = value;
				OnHorizontalByteCountChanged(EventArgs.Empty);
			}
		}

		private void SetVerticalByteCount(int value)
		{
			if (_iHexMaxVBytes != value)
			{
				_iHexMaxVBytes = value;
				OnVerticalByteCountChanged(EventArgs.Empty);
			}
		}

		private void CheckCurrentLineChanged()
		{
			long num = (long)Math.Floor((double)_bytePos / (double)_iHexMaxHBytes) + 1;
			if (_byteProvider == null && _currentLine != 0)
			{
				_currentLine = 0L;
				OnCurrentLineChanged(EventArgs.Empty);
			}
			else if (num != _currentLine)
			{
				_currentLine = num;
				OnCurrentLineChanged(EventArgs.Empty);
			}
		}

		private void CheckCurrentPositionInLineChanged()
		{
			int num = GetGridBytePoint(_bytePos).X + 1;
			if (_byteProvider == null && _currentPositionInLine != 0)
			{
				_currentPositionInLine = 0;
				OnCurrentPositionInLineChanged(EventArgs.Empty);
			}
			else if (num != _currentPositionInLine)
			{
				_currentPositionInLine = num;
				OnCurrentPositionInLineChanged(EventArgs.Empty);
			}
		}

		protected virtual void OnInsertActiveChanged(EventArgs e)
		{
			if (this.InsertActiveChanged != null)
			{
				this.InsertActiveChanged(this, e);
			}
		}

		protected virtual void OnReadOnlyChanged(EventArgs e)
		{
			if (this.ReadOnlyChanged != null)
			{
				this.ReadOnlyChanged(this, e);
			}
		}

		protected virtual void OnByteProviderChanged(EventArgs e)
		{
			if (this.ByteProviderChanged != null)
			{
				this.ByteProviderChanged(this, e);
			}
		}

		protected virtual void OnSelectionStartChanged(EventArgs e)
		{
			if (this.SelectionStartChanged != null)
			{
				this.SelectionStartChanged(this, e);
			}
		}

		protected virtual void OnSelectionLengthChanged(EventArgs e)
		{
			if (this.SelectionLengthChanged != null)
			{
				this.SelectionLengthChanged(this, e);
			}
		}

		protected virtual void OnLineInfoVisibleChanged(EventArgs e)
		{
			if (this.LineInfoVisibleChanged != null)
			{
				this.LineInfoVisibleChanged(this, e);
			}
		}

		protected virtual void OnStringViewVisibleChanged(EventArgs e)
		{
			if (this.StringViewVisibleChanged != null)
			{
				this.StringViewVisibleChanged(this, e);
			}
		}

		protected virtual void OnBorderStyleChanged(EventArgs e)
		{
			if (this.BorderStyleChanged != null)
			{
				this.BorderStyleChanged(this, e);
			}
		}

		protected virtual void OnUseFixedBytesPerLineChanged(EventArgs e)
		{
			if (this.UseFixedBytesPerLineChanged != null)
			{
				this.UseFixedBytesPerLineChanged(this, e);
			}
		}

		protected virtual void OnBytesPerLineChanged(EventArgs e)
		{
			if (this.BytesPerLineChanged != null)
			{
				this.BytesPerLineChanged(this, e);
			}
		}

		protected virtual void OnVScrollBarVisibleChanged(EventArgs e)
		{
			if (this.VScrollBarVisibleChanged != null)
			{
				this.VScrollBarVisibleChanged(this, e);
			}
		}

		protected virtual void OnHexCasingChanged(EventArgs e)
		{
			if (this.HexCasingChanged != null)
			{
				this.HexCasingChanged(this, e);
			}
		}

		protected virtual void OnHorizontalByteCountChanged(EventArgs e)
		{
			if (this.HorizontalByteCountChanged != null)
			{
				this.HorizontalByteCountChanged(this, e);
			}
		}

		protected virtual void OnVerticalByteCountChanged(EventArgs e)
		{
			if (this.VerticalByteCountChanged != null)
			{
				this.VerticalByteCountChanged(this, e);
			}
		}

		protected virtual void OnVisibleBytesChanged(EventArgs e)
		{
			if (this.VisibleBytesChanged != null)
			{
				this.VisibleBytesChanged(this, e);
			}
		}

		protected virtual void OnCurrentLineChanged(EventArgs e)
		{
			if (this.CurrentLineChanged != null)
			{
				this.CurrentLineChanged(this, e);
			}
		}

		protected virtual void OnCurrentPositionInLineChanged(EventArgs e)
		{
			if (this.CurrentPositionInLineChanged != null)
			{
				this.CurrentPositionInLineChanged(this, e);
			}
		}

		protected override void OnMouseDown(MouseEventArgs e)
		{
			if (!Focused)
			{
				Focus();
			}
			SetCaretPosition(new Point(e.X, e.Y));
			base.OnMouseDown(e);
		}

		protected override void OnMouseWheel(MouseEventArgs e)
		{
			int lines = -(e.Delta * SystemInformation.MouseWheelScrollLines / 120);
			PerformScrollLines(lines);
			base.OnMouseWheel(e);
		}

		protected override void OnResize(EventArgs e)
		{
			base.OnResize(e);
			UpdateRectanglePositioning();
		}

		protected override void OnGotFocus(EventArgs e)
		{
			base.OnGotFocus(e);
			CreateCaret();
		}

		protected override void OnLostFocus(EventArgs e)
		{
			base.OnLostFocus(e);
			DestroyCaret();
		}

		private void _byteProvider_LengthChanged(object sender, EventArgs e)
		{
			UpdateScrollSize();
		}
	}
	internal sealed class NativeMethods
	{
		public const int WM_KEYDOWN = 256;

		public const int WM_KEYUP = 257;

		public const int WM_CHAR = 258;

		static NativeMethods()
		{
		}

		[DllImport("user32.dll", SetLastError = true)]
		public static extern bool CreateCaret(IntPtr hWnd, IntPtr hBitmap, int nWidth, int nHeight);

		[DllImport("user32.dll", SetLastError = true)]
		public static extern bool ShowCaret(IntPtr hWnd);

		[DllImport("user32.dll", SetLastError = true)]
		public static extern bool DestroyCaret();

		[DllImport("user32.dll", SetLastError = true)]
		public static extern bool SetCaretPos(int X, int Y);
	}
	public sealed class DynamicFileByteProvider : IByteProvider, IDisposable
	{
		private const int COPY_BLOCK_SIZE = 4096;

		private string _fileName;

		private FileStream _fileStream;

		private DataMap _dataMap;

		private long _totalLength;

		private bool _readOnly;

		public long Length => _totalLength;

		public bool ReadOnly
		{
			get
			{
				return _readOnly;
			}
			set
			{
				_readOnly = value;
			}
		}

		public event EventHandler LengthChanged;

		public event EventHandler Changed;

		public DynamicFileByteProvider(string fileName)
			: this(fileName, readOnly: false)
		{
		}

		public DynamicFileByteProvider(string fileName, bool readOnly)
		{
			_fileName = fileName;
			if (!readOnly)
			{
				_fileStream = File.Open(fileName, FileMode.Open, FileAccess.ReadWrite, FileShare.Read);
			}
			else
			{
				_fileStream = File.Open(fileName, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
			}
			_readOnly = readOnly;
			ReInitialize();
		}

		public byte ReadByte(long index)
		{
			long blockOffset;
			DataBlock dataBlock = GetDataBlock(index, out blockOffset);
			if (dataBlock is FileDataBlock fileDataBlock)
			{
				return ReadByteFromFile(fileDataBlock.FileOffset + index - blockOffset);
			}
			MemoryDataBlock memoryDataBlock = (MemoryDataBlock)dataBlock;
			return memoryDataBlock.Data[index - blockOffset];
		}

		public void WriteByte(long index, byte value)
		{
			try
			{
				DataBlock dataBlock = GetDataBlock(index, out var blockOffset);
				if (dataBlock is MemoryDataBlock memoryDataBlock)
				{
					memoryDataBlock.Data[index - blockOffset] = value;
					return;
				}
				FileDataBlock fileDataBlock = (FileDataBlock)dataBlock;
				if (blockOffset == index && dataBlock.PreviousBlock != null && dataBlock.PreviousBlock is MemoryDataBlock memoryDataBlock2)
				{
					memoryDataBlock2.AddByteToEnd(value);
					fileDataBlock.RemoveBytesFromStart(1L);
					if (fileDataBlock.Length == 0)
					{
						_dataMap.Remove(fileDataBlock);
					}
					return;
				}
				if (blockOffset + fileDataBlock.Length - 1 == index && dataBlock.NextBlock != null && dataBlock.NextBlock is MemoryDataBlock memoryDataBlock3)
				{
					memoryDataBlock3.AddByteToStart(value);
					fileDataBlock.RemoveBytesFromEnd(1L);
					if (fileDataBlock.Length == 0)
					{
						_dataMap.Remove(fileDataBlock);
					}
					return;
				}
				FileDataBlock fileDataBlock2 = null;
				if (index > blockOffset)
				{
					fileDataBlock2 = new FileDataBlock(fileDataBlock.FileOffset, index - blockOffset);
				}
				FileDataBlock fileDataBlock3 = null;
				if (index < blockOffset + fileDataBlock.Length - 1)
				{
					fileDataBlock3 = new FileDataBlock(fileDataBlock.FileOffset + index - blockOffset + 1, fileDataBlock.Length - (index - blockOffset + 1));
				}
				dataBlock = _dataMap.Replace(dataBlock, new MemoryDataBlock(value));
				if (fileDataBlock2 != null)
				{
					_dataMap.AddBefore(dataBlock, fileDataBlock2);
				}
				if (fileDataBlock3 != null)
				{
					_dataMap.AddAfter(dataBlock, fileDataBlock3);
				}
			}
			finally
			{
				OnChanged(EventArgs.Empty);
			}
		}

		public void InsertBytes(long index, byte[] bs)
		{
			try
			{
				DataBlock dataBlock = GetDataBlock(index, out var blockOffset);
				if (dataBlock is MemoryDataBlock memoryDataBlock)
				{
					memoryDataBlock.InsertBytes(index - blockOffset, bs);
					return;
				}
				FileDataBlock fileDataBlock = (FileDataBlock)dataBlock;
				if (blockOffset == index && dataBlock.PreviousBlock != null && dataBlock.PreviousBlock is MemoryDataBlock memoryDataBlock2)
				{
					memoryDataBlock2.InsertBytes(memoryDataBlock2.Length, bs);
					return;
				}
				FileDataBlock fileDataBlock2 = null;
				if (index > blockOffset)
				{
					fileDataBlock2 = new FileDataBlock(fileDataBlock.FileOffset, index - blockOffset);
				}
				FileDataBlock fileDataBlock3 = null;
				if (index < blockOffset + fileDataBlock.Length)
				{
					fileDataBlock3 = new FileDataBlock(fileDataBlock.FileOffset + index - blockOffset, fileDataBlock.Length - (index - blockOffset));
				}
				dataBlock = _dataMap.Replace(dataBlock, new MemoryDataBlock(bs));
				if (fileDataBlock2 != null)
				{
					_dataMap.AddBefore(dataBlock, fileDataBlock2);
				}
				if (fileDataBlock3 != null)
				{
					_dataMap.AddAfter(dataBlock, fileDataBlock3);
				}
			}
			finally
			{
				_totalLength += bs.Length;
				OnLengthChanged(EventArgs.Empty);
				OnChanged(EventArgs.Empty);
			}
		}

		public void DeleteBytes(long index, long length)
		{
			try
			{
				long num = length;
				long blockOffset;
				DataBlock dataBlock = GetDataBlock(index, out blockOffset);
				while (num > 0)
				{
					long length2 = dataBlock.Length;
					DataBlock nextBlock = dataBlock.NextBlock;
					long num2 = Math.Min(num, length2 - (index - blockOffset));
					dataBlock.RemoveBytes(index - blockOffset, num2);
					if (dataBlock.Length == 0)
					{
						_dataMap.Remove(dataBlock);
						if (_dataMap.FirstBlock == null)
						{
							_dataMap.AddFirst(new MemoryDataBlock(new byte[0]));
						}
					}
					num -= num2;
					blockOffset += dataBlock.Length;
					dataBlock = ((num > 0) ? nextBlock : null);
				}
			}
			finally
			{
				_totalLength -= length;
				OnLengthChanged(EventArgs.Empty);
				OnChanged(EventArgs.Empty);
			}
		}

		public bool HasChanges()
		{
			if (_readOnly)
			{
				return false;
			}
			if (_totalLength != _fileStream.Length)
			{
				return true;
			}
			long num = 0L;
			for (DataBlock dataBlock = _dataMap.FirstBlock; dataBlock != null; dataBlock = dataBlock.NextBlock)
			{
				if (!(dataBlock is FileDataBlock fileDataBlock))
				{
					return true;
				}
				if (fileDataBlock.FileOffset != num)
				{
					return true;
				}
				num += fileDataBlock.Length;
			}
			return num != _fileStream.Length;
		}

		public void ApplyChanges()
		{
			if (_readOnly)
			{
				throw new OperationCanceledException("File is in read-only mode");
			}
			if (_totalLength > _fileStream.Length)
			{
				_fileStream.SetLength(_totalLength);
			}
			long num = 0L;
			for (DataBlock dataBlock = _dataMap.FirstBlock; dataBlock != null; dataBlock = dataBlock.NextBlock)
			{
				if (dataBlock is FileDataBlock fileDataBlock && fileDataBlock.FileOffset != num)
				{
					MoveFileBlock(fileDataBlock, num);
				}
				num += dataBlock.Length;
			}
			num = 0L;
			for (DataBlock dataBlock2 = _dataMap.FirstBlock; dataBlock2 != null; dataBlock2 = dataBlock2.NextBlock)
			{
				if (dataBlock2 is MemoryDataBlock memoryDataBlock)
				{
					_fileStream.Position = num;
					for (int i = 0; i < memoryDataBlock.Length; i += 4096)
					{
						_fileStream.Write(memoryDataBlock.Data, i, (int)Math.Min(4096L, memoryDataBlock.Length - i));
					}
				}
				num += dataBlock2.Length;
			}
			_fileStream.SetLength(_totalLength);
			ReInitialize();
		}

		public bool SupportsWriteByte()
		{
			return !_readOnly;
		}

		public bool SupportsInsertBytes()
		{
			return !_readOnly;
		}

		public bool SupportsDeleteBytes()
		{
			return !_readOnly;
		}

		~DynamicFileByteProvider()
		{
			Dispose();
		}

		public void Dispose()
		{
			if (_fileStream != null)
			{
				_fileStream.Close();
				_fileStream = null;
			}
			_fileName = null;
			_dataMap = null;
			GC.SuppressFinalize(this);
		}

		private void OnLengthChanged(EventArgs e)
		{
			if (this.LengthChanged != null)
			{
				this.LengthChanged(this, e);
			}
		}

		private void OnChanged(EventArgs e)
		{
			if (this.Changed != null)
			{
				this.Changed(this, e);
			}
		}

		private DataBlock GetDataBlock(long findOffset, out long blockOffset)
		{
			if (findOffset < 0 || findOffset > _totalLength)
			{
				throw new ArgumentOutOfRangeException("index");
			}
			blockOffset = 0L;
			for (DataBlock dataBlock = _dataMap.FirstBlock; dataBlock != null; dataBlock = dataBlock.NextBlock)
			{
				if ((blockOffset <= findOffset && blockOffset + dataBlock.Length > findOffset) || dataBlock.NextBlock == null)
				{
					return dataBlock;
				}
				blockOffset += dataBlock.Length;
			}
			return null;
		}

		private FileDataBlock GetNextFileDataBlock(DataBlock block, long dataOffset, out long nextDataOffset)
		{
			nextDataOffset = dataOffset + block.Length;
			for (block = block.NextBlock; block != null; block = block.NextBlock)
			{
				if (block is FileDataBlock result)
				{
					return result;
				}
				nextDataOffset += block.Length;
			}
			return null;
		}

		private byte ReadByteFromFile(long fileOffset)
		{
			if (_fileStream.Position != fileOffset)
			{
				_fileStream.Position = fileOffset;
			}
			return (byte)_fileStream.ReadByte();
		}

		private void MoveFileBlock(FileDataBlock fileBlock, long dataOffset)
		{
			long nextDataOffset;
			FileDataBlock nextFileDataBlock = GetNextFileDataBlock(fileBlock, dataOffset, out nextDataOffset);
			if (nextFileDataBlock != null && dataOffset + fileBlock.Length > nextFileDataBlock.FileOffset)
			{
				MoveFileBlock(nextFileDataBlock, nextDataOffset);
			}
			if (fileBlock.FileOffset > dataOffset)
			{
				byte[] array = new byte[4096];
				for (long num = 0L; num < fileBlock.Length; num += array.Length)
				{
					long position = fileBlock.FileOffset + num;
					int count = (int)Math.Min(array.Length, fileBlock.Length - num);
					_fileStream.Position = position;
					_fileStream.Read(array, 0, count);
					long position2 = dataOffset + num;
					_fileStream.Position = position2;
					_fileStream.Write(array, 0, count);
				}
			}
			else
			{
				byte[] array2 = new byte[4096];
				for (long num2 = 0L; num2 < fileBlock.Length; num2 += array2.Length)
				{
					int num3 = (int)Math.Min(array2.Length, fileBlock.Length - num2);
					long position3 = fileBlock.FileOffset + fileBlock.Length - num2 - num3;
					_fileStream.Position = position3;
					_fileStream.Read(array2, 0, num3);
					long position4 = dataOffset + fileBlock.Length - num2 - num3;
					_fileStream.Position = position4;
					_fileStream.Write(array2, 0, num3);
				}
			}
			fileBlock.SetFileOffset(dataOffset);
		}

		private void ReInitialize()
		{
			_dataMap = new DataMap();
			_dataMap.AddFirst(new FileDataBlock(0L, _fileStream.Length));
			_totalLength = _fileStream.Length;
		}
	}
}
namespace Be.Windows.Forms.Design
{
	internal class HexFontEditor : FontEditor
	{
		private object value;

		public override object EditValue(ITypeDescriptorContext context, IServiceProvider provider, object value)
		{
			this.value = value;
			if (provider != null)
			{
				IWindowsFormsEditorService windowsFormsEditorService = (IWindowsFormsEditorService)provider.GetService(typeof(IWindowsFormsEditorService));
				if (windowsFormsEditorService != null)
				{
					FontDialog fontDialog = new FontDialog();
					fontDialog.ShowApply = false;
					fontDialog.ShowColor = false;
					fontDialog.AllowVerticalFonts = false;
					fontDialog.AllowScriptChange = false;
					fontDialog.FixedPitchOnly = true;
					fontDialog.ShowEffects = false;
					fontDialog.ShowHelp = false;
					if (value is Font font)
					{
						fontDialog.Font = font;
					}
					if (fontDialog.ShowDialog() == DialogResult.OK)
					{
						this.value = fontDialog.Font;
					}
					fontDialog.Dispose();
				}
			}
			value = this.value;
			this.value = null;
			return value;
		}

		public override UITypeEditorEditStyle GetEditStyle(ITypeDescriptorContext context)
		{
			return UITypeEditorEditStyle.Modal;
		}
	}
}
