using System;
using System.CodeDom.Compiler;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Configuration;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Reflection;
using System.Resources;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using System.Xml;
using Be.Windows.Forms;
using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraTreeList;
using DevExpress.XtraTreeList.Columns;
using DevExpress.XtraTreeList.Nodes;
using DevExpress.XtraTreeList.ViewInfo;
using Microsoft.Win32;
using OLESS;

[assembly: AssemblyDescription("")]
[assembly: AssemblyConfiguration("")]
[assembly: RuntimeCompatibility(WrapNonExceptionThrows = true)]
[assembly: AssemblyCopyright("Copyright © Microsoft 2009")]
[assembly: AssemblyTrademark("")]
[assembly: CompilationRelaxations(8)]
[assembly: AssemblyCompany("Microsoft")]
[assembly: AssemblyProduct("OffVis")]
[assembly: ComVisible(false)]
[assembly: AssemblyTitle("OffVis")]
[assembly: Guid("984f8f11-0eb1-4464-b95a-f58d304356e3")]
[assembly: AssemblyFileVersion("*******")]
[assembly: Debuggable(DebuggableAttribute.DebuggingModes.IgnoreSymbolStoreSequencePoints)]
[assembly: AssemblyVersion("*******")]
namespace GUT.Tools.FormatBrowser
{
	public enum FindDirection
	{
		Next,
		Previous
	}
	public delegate void StartFindHandler(FindDirection Direction, byte[] SearchData, bool CaseSensitive);
	public class FindDialog : Form
	{
		private byte[] _BytesToFind;

		private IContainer components;

		private Label label1;

		private TextBox What;

		private GroupBox groupBox1;

		private RadioButton FormatHex;

		private RadioButton FormatDecimal;

		private RadioButton FormatASCII;

		private Label label2;

		private RadioButton EndianLittle;

		private RadioButton EndianBig;

		private Label label3;

		private TextBox BytesToSearchFor;

		private Button FindNext;

		private Button FindPrevious;

		private Button Cancel;

		private Label label4;

		private System.Windows.Forms.ComboBox NumberType;

		public event StartFindHandler StartFind;

		private void OnStartFind(FindDirection Direction, bool CaseSensitive)
		{
			if (this.StartFind != null)
			{
				this.StartFind(Direction, _BytesToFind, CaseSensitive);
			}
		}

		public FindDialog()
		{
			InitializeComponent();
			NumberType.SelectedIndex = 6;
		}

		private void What_TextChanged(object sender, EventArgs e)
		{
			CalculateBytesToFind();
		}

		private void CalculateBytesToFind()
		{
			_BytesToFind = null;
			if (FormatASCII.Checked)
			{
				ASCIIEncoding aSCIIEncoding = new ASCIIEncoding();
				_BytesToFind = aSCIIEncoding.GetBytes(What.Text);
			}
			else if (FormatDecimal.Checked || FormatHex.Checked)
			{
				NumberStyles style = ((!FormatDecimal.Checked) ? NumberStyles.HexNumber : NumberStyles.None);
				switch (NumberType.SelectedItem.ToString())
				{
				case "UInt8":
				{
					byte result8 = 0;
					if (byte.TryParse(What.Text.Replace("0x", ""), style, null, out result8))
					{
						_BytesToFind = new byte[1] { BitConverter.GetBytes(result8)[0] };
					}
					break;
				}
				case "UInt16":
				{
					ushort result4 = 0;
					if (ushort.TryParse(What.Text.Replace("0x", ""), style, null, out result4))
					{
						_BytesToFind = BitConverter.GetBytes(result4);
					}
					break;
				}
				case "UInt32":
				{
					uint result6 = 0u;
					if (uint.TryParse(What.Text.Replace("0x", ""), style, null, out result6))
					{
						_BytesToFind = BitConverter.GetBytes(result6);
					}
					break;
				}
				case "UInt64":
				{
					ulong result2 = 0uL;
					if (ulong.TryParse(What.Text.Replace("0x", ""), style, null, out result2))
					{
						_BytesToFind = BitConverter.GetBytes(result2);
					}
					break;
				}
				case "Int8":
				{
					sbyte result7 = 0;
					if (sbyte.TryParse(What.Text.Replace("0x", ""), style, null, out result7))
					{
						_BytesToFind = new byte[1] { BitConverter.GetBytes(result7)[0] };
					}
					break;
				}
				case "Int16":
				{
					short result5 = 0;
					if (short.TryParse(What.Text.Replace("0x", ""), style, null, out result5))
					{
						_BytesToFind = BitConverter.GetBytes(result5);
					}
					break;
				}
				case "Int32":
				{
					int result3 = 0;
					if (int.TryParse(What.Text.Replace("0x", ""), style, null, out result3))
					{
						_BytesToFind = BitConverter.GetBytes(result3);
					}
					break;
				}
				case "Int64":
				{
					long result = 0L;
					if (long.TryParse(What.Text.Replace("0x", ""), style, null, out result))
					{
						_BytesToFind = BitConverter.GetBytes(result);
					}
					break;
				}
				}
			}
			if (EndianBig.Checked)
			{
				_BytesToFind = SwapByteOrder(_BytesToFind);
			}
			DisplayBytesToFind();
		}

		private void DisplayBytesToFind()
		{
			BytesToSearchFor.Text = "";
			if (_BytesToFind != null)
			{
				for (int i = 0; i < _BytesToFind.Length; i++)
				{
					TextBox bytesToSearchFor = BytesToSearchFor;
					bytesToSearchFor.Text = bytesToSearchFor.Text + _BytesToFind[i].ToString("X2") + " ";
				}
			}
		}

		private byte[] SwapByteOrder(byte[] OriginalData)
		{
			if (OriginalData == null)
			{
				return null;
			}
			byte[] array = new byte[OriginalData.Length];
			for (int i = 0; i < array.Length; i++)
			{
				array[array.Length - 1 - i] = OriginalData[i];
			}
			return array;
		}

		private void OptionsChanged(object sender, EventArgs e)
		{
			CalculateBytesToFind();
		}

		private void FindNext_Click(object sender, EventArgs e)
		{
			OnStartFind(FindDirection.Next, CaseSensitive: true);
		}

		private void FindPrevious_Click(object sender, EventArgs e)
		{
			OnStartFind(FindDirection.Previous, CaseSensitive: true);
		}

		private void Cancel_Click(object sender, EventArgs e)
		{
		}

		protected override void Dispose(bool disposing)
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
			this.label1 = new System.Windows.Forms.Label();
			this.What = new System.Windows.Forms.TextBox();
			this.groupBox1 = new System.Windows.Forms.GroupBox();
			this.FormatHex = new System.Windows.Forms.RadioButton();
			this.FormatDecimal = new System.Windows.Forms.RadioButton();
			this.FormatASCII = new System.Windows.Forms.RadioButton();
			this.label2 = new System.Windows.Forms.Label();
			this.EndianLittle = new System.Windows.Forms.RadioButton();
			this.EndianBig = new System.Windows.Forms.RadioButton();
			this.label3 = new System.Windows.Forms.Label();
			this.BytesToSearchFor = new System.Windows.Forms.TextBox();
			this.FindNext = new System.Windows.Forms.Button();
			this.FindPrevious = new System.Windows.Forms.Button();
			this.Cancel = new System.Windows.Forms.Button();
			this.label4 = new System.Windows.Forms.Label();
			this.NumberType = new System.Windows.Forms.ComboBox();
			this.groupBox1.SuspendLayout();
			base.SuspendLayout();
			this.label1.AutoSize = true;
			this.label1.Location = new System.Drawing.Point(12, 9);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(36, 13);
			this.label1.TabIndex = 0;
			this.label1.Text = "What:";
			this.What.Location = new System.Drawing.Point(54, 6);
			this.What.Name = "What";
			this.What.Size = new System.Drawing.Size(368, 20);
			this.What.TabIndex = 1;
			this.What.TextChanged += new System.EventHandler(What_TextChanged);
			this.groupBox1.Controls.Add(this.FormatHex);
			this.groupBox1.Controls.Add(this.FormatDecimal);
			this.groupBox1.Controls.Add(this.FormatASCII);
			this.groupBox1.Location = new System.Drawing.Point(54, 32);
			this.groupBox1.Name = "groupBox1";
			this.groupBox1.Size = new System.Drawing.Size(368, 50);
			this.groupBox1.TabIndex = 2;
			this.groupBox1.TabStop = false;
			this.groupBox1.Text = "Format";
			this.FormatHex.AutoSize = true;
			this.FormatHex.Location = new System.Drawing.Point(157, 19);
			this.FormatHex.Name = "FormatHex";
			this.FormatHex.Size = new System.Drawing.Size(44, 17);
			this.FormatHex.TabIndex = 2;
			this.FormatHex.Text = "Hex";
			this.FormatHex.UseVisualStyleBackColor = true;
			this.FormatHex.CheckedChanged += new System.EventHandler(OptionsChanged);
			this.FormatDecimal.AutoSize = true;
			this.FormatDecimal.Location = new System.Drawing.Point(76, 19);
			this.FormatDecimal.Name = "FormatDecimal";
			this.FormatDecimal.Size = new System.Drawing.Size(63, 17);
			this.FormatDecimal.TabIndex = 1;
			this.FormatDecimal.Text = "Decimal";
			this.FormatDecimal.UseVisualStyleBackColor = true;
			this.FormatDecimal.CheckedChanged += new System.EventHandler(OptionsChanged);
			this.FormatASCII.AutoSize = true;
			this.FormatASCII.Checked = true;
			this.FormatASCII.Location = new System.Drawing.Point(6, 19);
			this.FormatASCII.Name = "FormatASCII";
			this.FormatASCII.Size = new System.Drawing.Size(52, 17);
			this.FormatASCII.TabIndex = 0;
			this.FormatASCII.TabStop = true;
			this.FormatASCII.Text = "ASCII";
			this.FormatASCII.UseVisualStyleBackColor = true;
			this.FormatASCII.CheckedChanged += new System.EventHandler(OptionsChanged);
			this.label2.AutoSize = true;
			this.label2.Location = new System.Drawing.Point(12, 119);
			this.label2.Name = "label2";
			this.label2.Size = new System.Drawing.Size(43, 13);
			this.label2.TabIndex = 3;
			this.label2.Text = "Endian:";
			this.EndianLittle.AutoSize = true;
			this.EndianLittle.Checked = true;
			this.EndianLittle.Location = new System.Drawing.Point(60, 117);
			this.EndianLittle.Name = "EndianLittle";
			this.EndianLittle.Size = new System.Drawing.Size(47, 17);
			this.EndianLittle.TabIndex = 4;
			this.EndianLittle.TabStop = true;
			this.EndianLittle.Text = "Little";
			this.EndianLittle.UseVisualStyleBackColor = true;
			this.EndianLittle.CheckedChanged += new System.EventHandler(OptionsChanged);
			this.EndianBig.AutoSize = true;
			this.EndianBig.Location = new System.Drawing.Point(130, 117);
			this.EndianBig.Name = "EndianBig";
			this.EndianBig.Size = new System.Drawing.Size(40, 17);
			this.EndianBig.TabIndex = 5;
			this.EndianBig.Text = "Big";
			this.EndianBig.UseVisualStyleBackColor = true;
			this.EndianBig.CheckedChanged += new System.EventHandler(OptionsChanged);
			this.label3.AutoSize = true;
			this.label3.Location = new System.Drawing.Point(11, 143);
			this.label3.Name = "label3";
			this.label3.Size = new System.Drawing.Size(36, 13);
			this.label3.TabIndex = 6;
			this.label3.Text = "Bytes:";
			this.BytesToSearchFor.Enabled = false;
			this.BytesToSearchFor.Location = new System.Drawing.Point(53, 140);
			this.BytesToSearchFor.Name = "BytesToSearchFor";
			this.BytesToSearchFor.ReadOnly = true;
			this.BytesToSearchFor.Size = new System.Drawing.Size(368, 20);
			this.BytesToSearchFor.TabIndex = 7;
			this.FindNext.DialogResult = System.Windows.Forms.DialogResult.OK;
			this.FindNext.Location = new System.Drawing.Point(99, 177);
			this.FindNext.Name = "FindNext";
			this.FindNext.Size = new System.Drawing.Size(75, 23);
			this.FindNext.TabIndex = 8;
			this.FindNext.Text = "Find Next";
			this.FindNext.UseVisualStyleBackColor = true;
			this.FindNext.Click += new System.EventHandler(FindNext_Click);
			this.FindPrevious.DialogResult = System.Windows.Forms.DialogResult.OK;
			this.FindPrevious.Location = new System.Drawing.Point(180, 177);
			this.FindPrevious.Name = "FindPrevious";
			this.FindPrevious.Size = new System.Drawing.Size(75, 23);
			this.FindPrevious.TabIndex = 9;
			this.FindPrevious.Text = "Find Prev";
			this.FindPrevious.UseVisualStyleBackColor = true;
			this.FindPrevious.Click += new System.EventHandler(FindPrevious_Click);
			this.Cancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.Cancel.Location = new System.Drawing.Point(261, 177);
			this.Cancel.Name = "Cancel";
			this.Cancel.Size = new System.Drawing.Size(75, 23);
			this.Cancel.TabIndex = 10;
			this.Cancel.Text = "Cancel";
			this.Cancel.UseVisualStyleBackColor = true;
			this.Cancel.Click += new System.EventHandler(Cancel_Click);
			this.label4.AutoSize = true;
			this.label4.Location = new System.Drawing.Point(12, 91);
			this.label4.Name = "label4";
			this.label4.Size = new System.Drawing.Size(34, 13);
			this.label4.TabIndex = 11;
			this.label4.Text = "Type:";
			this.NumberType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.NumberType.FormattingEnabled = true;
			this.NumberType.Items.AddRange(new object[8] { "UInt8", "UInt16", "UInt32", "UInt64", "Int8", "Int16", "Int32", "Int64" });
			this.NumberType.Location = new System.Drawing.Point(53, 88);
			this.NumberType.Name = "NumberType";
			this.NumberType.Size = new System.Drawing.Size(121, 21);
			this.NumberType.TabIndex = 12;
			this.NumberType.SelectedIndexChanged += new System.EventHandler(OptionsChanged);
			base.AcceptButton = this.FindNext;
			base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
			base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
			base.CancelButton = this.Cancel;
			base.ClientSize = new System.Drawing.Size(434, 215);
			base.Controls.Add(this.NumberType);
			base.Controls.Add(this.label4);
			base.Controls.Add(this.Cancel);
			base.Controls.Add(this.FindPrevious);
			base.Controls.Add(this.FindNext);
			base.Controls.Add(this.BytesToSearchFor);
			base.Controls.Add(this.label3);
			base.Controls.Add(this.EndianBig);
			base.Controls.Add(this.EndianLittle);
			base.Controls.Add(this.label2);
			base.Controls.Add(this.groupBox1);
			base.Controls.Add(this.What);
			base.Controls.Add(this.label1);
			base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = "FindDialog";
			base.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
			this.Text = "Find";
			base.TopMost = true;
			this.groupBox1.ResumeLayout(false);
			this.groupBox1.PerformLayout();
			base.ResumeLayout(false);
			base.PerformLayout();
		}
	}
	internal class Utils
	{
		private const string _RegistryKeyPath = "Software\\Microsoft\\GUT";

		public string AssemblyTitle
		{
			get
			{
				object[] customAttributes = Assembly.GetExecutingAssembly().GetCustomAttributes(typeof(AssemblyTitleAttribute), inherit: false);
				if (customAttributes.Length > 0)
				{
					AssemblyTitleAttribute assemblyTitleAttribute = (AssemblyTitleAttribute)customAttributes[0];
					if (assemblyTitleAttribute.Title != "")
					{
						return assemblyTitleAttribute.Title;
					}
				}
				return Path.GetFileNameWithoutExtension(Assembly.GetExecutingAssembly().CodeBase);
			}
		}

		public static string AssemblyVersion => Assembly.GetExecutingAssembly().GetName().Version.ToString();

		public string AssemblyDescription
		{
			get
			{
				object[] customAttributes = Assembly.GetExecutingAssembly().GetCustomAttributes(typeof(AssemblyDescriptionAttribute), inherit: false);
				if (customAttributes.Length == 0)
				{
					return "";
				}
				return ((AssemblyDescriptionAttribute)customAttributes[0]).Description;
			}
		}

		public static string AssemblyProduct
		{
			get
			{
				object[] customAttributes = Assembly.GetExecutingAssembly().GetCustomAttributes(typeof(AssemblyProductAttribute), inherit: false);
				if (customAttributes.Length == 0)
				{
					return "";
				}
				return ((AssemblyProductAttribute)customAttributes[0]).Product;
			}
		}

		public string AssemblyCopyright
		{
			get
			{
				object[] customAttributes = Assembly.GetExecutingAssembly().GetCustomAttributes(typeof(AssemblyCopyrightAttribute), inherit: false);
				if (customAttributes.Length == 0)
				{
					return "";
				}
				return ((AssemblyCopyrightAttribute)customAttributes[0]).Copyright;
			}
		}

		public string AssemblyCompany
		{
			get
			{
				object[] customAttributes = Assembly.GetExecutingAssembly().GetCustomAttributes(typeof(AssemblyCompanyAttribute), inherit: false);
				if (customAttributes.Length == 0)
				{
					return "";
				}
				return ((AssemblyCompanyAttribute)customAttributes[0]).Company;
			}
		}

		public static bool HasOption(string name)
		{
			RegistryKey registryKey = Registry.CurrentUser.CreateSubKey("Software\\Microsoft\\GUT");
			object value = registryKey.GetValue(name, null);
			registryKey.Close();
			return value != null;
		}

		public static void SetStringOption(string name, string value)
		{
			RegistryKey registryKey = Registry.CurrentUser.CreateSubKey("Software\\Microsoft\\GUT");
			registryKey.SetValue(name, value);
			registryKey.Close();
		}

		public static string GetStringOption(string name, string defaultValue)
		{
			RegistryKey registryKey = Registry.CurrentUser.CreateSubKey("Software\\Microsoft\\GUT");
			string result = registryKey.GetValue(name, defaultValue).ToString();
			registryKey.Close();
			return result;
		}

		public static void SetIntOption(string name, int value)
		{
			RegistryKey registryKey = Registry.CurrentUser.CreateSubKey("Software\\Microsoft\\GUT");
			registryKey.SetValue(name, value);
			registryKey.Close();
		}

		public static int GetIntOption(string name, int defaultValue)
		{
			RegistryKey registryKey = Registry.CurrentUser.CreateSubKey("Software\\Microsoft\\GUT");
			object value = registryKey.GetValue(name, defaultValue);
			registryKey.Close();
			return int.Parse(value.ToString());
		}

		public static void SetBoolOption(string name, bool value)
		{
			RegistryKey registryKey = Registry.CurrentUser.CreateSubKey("Software\\Microsoft\\GUT");
			registryKey.SetValue(name, value ? 1 : 0);
			registryKey.Close();
		}

		public static bool GetBoolOption(string name, bool defaultValue)
		{
			RegistryKey registryKey = Registry.CurrentUser.CreateSubKey("Software\\Microsoft\\GUT");
			object value = registryKey.GetValue(name, defaultValue ? 1 : 0);
			registryKey.Close();
			if (int.TryParse(value.ToString(), out var result))
			{
				return result != 0;
			}
			return false;
		}
	}
	public class OptionsDialog : Form
	{
		private class Editor
		{
			public TreeListNode Node;

			public object GetValue()
			{
				return Node[1];
			}
		}

		private class BoolEditor : Editor
		{
		}

		private class DirEditor : Editor
		{
		}

		private IContainer components;

		private TreeList OptionsTL;

		private TreeListColumn treeListColumn1;

		private TreeListColumn ValueColumn;

		private RepositoryItemCheckEdit BoolEdit;

		private Panel panel1;

		private Button button2;

		private Button button1;

		private RepositoryItemButtonEdit DirEditorRI;

		private FolderBrowserDialog folderBrowserDialog;

		private SortedList<string, Editor> Options = new SortedList<string, Editor>();

		protected override void Dispose(bool disposing)
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
			System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(GUT.Tools.FormatBrowser.OptionsDialog));
			this.OptionsTL = new DevExpress.XtraTreeList.TreeList();
			this.treeListColumn1 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
			this.ValueColumn = new DevExpress.XtraTreeList.Columns.TreeListColumn();
			this.BoolEdit = new DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit();
			this.DirEditorRI = new DevExpress.XtraEditors.Repository.RepositoryItemButtonEdit();
			this.panel1 = new System.Windows.Forms.Panel();
			this.button2 = new System.Windows.Forms.Button();
			this.button1 = new System.Windows.Forms.Button();
			this.folderBrowserDialog = new System.Windows.Forms.FolderBrowserDialog();
			((System.ComponentModel.ISupportInitialize)this.OptionsTL).BeginInit();
			((System.ComponentModel.ISupportInitialize)this.BoolEdit).BeginInit();
			((System.ComponentModel.ISupportInitialize)this.DirEditorRI).BeginInit();
			this.panel1.SuspendLayout();
			base.SuspendLayout();
			this.OptionsTL.Appearance.FocusedRow.BackColor = System.Drawing.Color.White;
			this.OptionsTL.Appearance.FocusedRow.ForeColor = System.Drawing.Color.Black;
			this.OptionsTL.Appearance.FocusedRow.Options.UseBackColor = true;
			this.OptionsTL.Appearance.FocusedRow.Options.UseForeColor = true;
			this.OptionsTL.Appearance.HideSelectionRow.BackColor = System.Drawing.Color.White;
			this.OptionsTL.Appearance.HideSelectionRow.ForeColor = System.Drawing.Color.Black;
			this.OptionsTL.Appearance.HideSelectionRow.Options.UseBackColor = true;
			this.OptionsTL.Appearance.HideSelectionRow.Options.UseForeColor = true;
			this.OptionsTL.Appearance.SelectedRow.BackColor = System.Drawing.Color.White;
			this.OptionsTL.Appearance.SelectedRow.ForeColor = System.Drawing.Color.Black;
			this.OptionsTL.Appearance.SelectedRow.Options.UseBackColor = true;
			this.OptionsTL.Appearance.SelectedRow.Options.UseForeColor = true;
			this.OptionsTL.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[2] { this.treeListColumn1, this.ValueColumn });
			this.OptionsTL.Dock = System.Windows.Forms.DockStyle.Fill;
			this.OptionsTL.Location = new System.Drawing.Point(0, 0);
			this.OptionsTL.Name = "OptionsTL";
			this.OptionsTL.OptionsView.ShowFocusedFrame = false;
			this.OptionsTL.OptionsView.ShowIndicator = false;
			this.OptionsTL.OptionsView.ShowRoot = false;
			this.OptionsTL.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[2] { this.BoolEdit, this.DirEditorRI });
			this.OptionsTL.ShowButtonMode = DevExpress.XtraTreeList.ShowButtonModeEnum.ShowAlways;
			this.OptionsTL.Size = new System.Drawing.Size(673, 498);
			this.OptionsTL.TabIndex = 0;
			this.OptionsTL.TreeLineStyle = DevExpress.XtraTreeList.LineStyle.None;
			this.OptionsTL.CustomNodeCellEdit += new DevExpress.XtraTreeList.GetCustomNodeCellEditEventHandler(OptionsTL_CustomNodeCellEdit);
			this.OptionsTL.ShowingEditor += new System.ComponentModel.CancelEventHandler(OptionsTL_ShowingEditor);
			this.treeListColumn1.Caption = "Option";
			this.treeListColumn1.FieldName = "Option";
			this.treeListColumn1.Name = "treeListColumn1";
			this.treeListColumn1.OptionsColumn.AllowEdit = false;
			this.treeListColumn1.OptionsColumn.AllowFocus = false;
			this.treeListColumn1.Visible = true;
			this.treeListColumn1.VisibleIndex = 0;
			this.treeListColumn1.Width = 221;
			this.ValueColumn.Caption = "Value";
			this.ValueColumn.FieldName = "Value";
			this.ValueColumn.Name = "ValueColumn";
			this.ValueColumn.Visible = true;
			this.ValueColumn.VisibleIndex = 1;
			this.ValueColumn.Width = 448;
			this.BoolEdit.AutoHeight = false;
			this.BoolEdit.Name = "BoolEdit";
			this.DirEditorRI.AutoHeight = false;
			this.DirEditorRI.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[1]
			{
				new DevExpress.XtraEditors.Controls.EditorButton()
			});
			this.DirEditorRI.Name = "DirEditorRI";
			this.DirEditorRI.ButtonClick += new DevExpress.XtraEditors.Controls.ButtonPressedEventHandler(DirEditorRI_ButtonClick);
			this.panel1.Controls.Add(this.button2);
			this.panel1.Controls.Add(this.button1);
			this.panel1.Dock = System.Windows.Forms.DockStyle.Bottom;
			this.panel1.Location = new System.Drawing.Point(0, 470);
			this.panel1.Name = "panel1";
			this.panel1.Size = new System.Drawing.Size(673, 28);
			this.panel1.TabIndex = 1;
			this.button2.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.button2.Location = new System.Drawing.Point(84, 3);
			this.button2.Name = "button2";
			this.button2.Size = new System.Drawing.Size(75, 23);
			this.button2.TabIndex = 1;
			this.button2.Text = "&Cancel";
			this.button2.UseVisualStyleBackColor = true;
			this.button1.DialogResult = System.Windows.Forms.DialogResult.OK;
			this.button1.Location = new System.Drawing.Point(3, 3);
			this.button1.Name = "button1";
			this.button1.Size = new System.Drawing.Size(75, 23);
			this.button1.TabIndex = 0;
			this.button1.Text = "&OK";
			this.button1.UseVisualStyleBackColor = true;
			base.AcceptButton = this.button1;
			base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
			base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
			base.CancelButton = this.button2;
			base.ClientSize = new System.Drawing.Size(673, 498);
			base.Controls.Add(this.panel1);
			base.Controls.Add(this.OptionsTL);
			base.Icon = (System.Drawing.Icon)resources.GetObject("$this.Icon");
			base.Name = "OptionsDialog";
			base.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
			this.Text = "Options";
			((System.ComponentModel.ISupportInitialize)this.OptionsTL).EndInit();
			((System.ComponentModel.ISupportInitialize)this.BoolEdit).EndInit();
			((System.ComponentModel.ISupportInitialize)this.DirEditorRI).EndInit();
			this.panel1.ResumeLayout(false);
			base.ResumeLayout(false);
		}

		public OptionsDialog()
		{
			InitializeComponent();
		}

		private TreeListNode GetCategoryNode(string category)
		{
			foreach (TreeListNode node in OptionsTL.Nodes)
			{
				if (node.Tag == null && category.Equals((string)node[0], StringComparison.InvariantCultureIgnoreCase))
				{
					return node;
				}
			}
			return OptionsTL.AppendNode(new object[2] { category, null }, null);
		}

		public void AddBoolOption(string category, string name, bool value)
		{
			BoolEditor boolEditor = new BoolEditor();
			TreeListNode categoryNode = GetCategoryNode(category);
			boolEditor.Node = OptionsTL.AppendNode(new object[2] { name, value }, categoryNode, boolEditor);
			categoryNode.Expanded = true;
			Options.Add(name, boolEditor);
		}

		public void AddDirOption(string category, string name, string value)
		{
			DirEditor dirEditor = new DirEditor();
			TreeListNode categoryNode = GetCategoryNode(category);
			dirEditor.Node = OptionsTL.AppendNode(new object[2] { name, value }, categoryNode, dirEditor);
			categoryNode.Expanded = true;
			Options.Add(name, dirEditor);
		}

		public object GetOption(string name)
		{
			if (Options.ContainsKey(name))
			{
				return Options[name].GetValue();
			}
			return null;
		}

		public bool GetBoolOption(string name)
		{
			return (bool)GetOption(name);
		}

		public string GetStringOption(string name)
		{
			return (string)GetOption(name);
		}

		private void OptionsTL_CustomNodeCellEdit(object sender, GetCustomNodeCellEditEventArgs e)
		{
			if (e.Column == ValueColumn)
			{
				if (e.Node.Tag is BoolEditor)
				{
					e.RepositoryItem = BoolEdit;
				}
				else if (e.Node.Tag is DirEditor)
				{
					e.RepositoryItem = DirEditorRI;
				}
				else
				{
					e.RepositoryItem = null;
				}
			}
		}

		private void OptionsTL_ShowingEditor(object sender, CancelEventArgs e)
		{
			if (OptionsTL.FocusedNode != null && OptionsTL.FocusedNode.Tag != null)
			{
				e.Cancel = false;
			}
			else
			{
				e.Cancel = true;
			}
		}

		private void DirEditorRI_ButtonClick(object sender, ButtonPressedEventArgs e)
		{
			ButtonEdit buttonEdit = (ButtonEdit)sender;
			folderBrowserDialog.SelectedPath = (string)buttonEdit.EditValue;
			if (folderBrowserDialog.ShowDialog() == DialogResult.OK)
			{
				OptionsTL.FocusedNode[1] = folderBrowserDialog.SelectedPath;
			}
		}
	}
	public class SplashScreen : Form
	{
		private IContainer components;

		private Timer CloseTimer;

		private Panel panel1;

		private RichTextBox richTextBox1;

		private CheckBox HideCheckBox;

		private Label labelProductName;

		private Label labelVersion;

		private Button OkButton;

		protected override void Dispose(bool disposing)
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
			this.components = new System.ComponentModel.Container();
			System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(GUT.Tools.FormatBrowser.SplashScreen));
			this.CloseTimer = new System.Windows.Forms.Timer(this.components);
			this.panel1 = new System.Windows.Forms.Panel();
			this.OkButton = new System.Windows.Forms.Button();
			this.richTextBox1 = new System.Windows.Forms.RichTextBox();
			this.HideCheckBox = new System.Windows.Forms.CheckBox();
			this.labelProductName = new System.Windows.Forms.Label();
			this.labelVersion = new System.Windows.Forms.Label();
			this.panel1.SuspendLayout();
			base.SuspendLayout();
			this.CloseTimer.Interval = 5000;
			this.CloseTimer.Tick += new System.EventHandler(CloseTimer_Tick);
			this.panel1.BorderStyle = System.Windows.Forms.BorderStyle.FixedSingle;
			this.panel1.Controls.Add(this.OkButton);
			this.panel1.Controls.Add(this.richTextBox1);
			this.panel1.Controls.Add(this.HideCheckBox);
			this.panel1.Controls.Add(this.labelProductName);
			this.panel1.Controls.Add(this.labelVersion);
			this.panel1.Dock = System.Windows.Forms.DockStyle.Fill;
			this.panel1.Location = new System.Drawing.Point(0, 0);
			this.panel1.Name = "panel1";
			this.panel1.Size = new System.Drawing.Size(401, 261);
			this.panel1.TabIndex = 30;
			this.OkButton.DialogResult = System.Windows.Forms.DialogResult.OK;
			this.OkButton.Location = new System.Drawing.Point(312, 226);
			this.OkButton.Name = "OkButton";
			this.OkButton.Size = new System.Drawing.Size(75, 23);
			this.OkButton.TabIndex = 35;
			this.OkButton.Text = "&OK";
			this.OkButton.UseVisualStyleBackColor = true;
			this.OkButton.Click += new System.EventHandler(OkButton_Click);
			this.richTextBox1.Location = new System.Drawing.Point(10, 42);
			this.richTextBox1.Name = "richTextBox1";
			this.richTextBox1.Size = new System.Drawing.Size(377, 175);
			this.richTextBox1.TabIndex = 33;
			this.richTextBox1.Text = resources.GetString("richTextBox1.Text");
			this.HideCheckBox.AutoSize = true;
			this.HideCheckBox.Location = new System.Drawing.Point(10, 226);
			this.HideCheckBox.Name = "HideCheckBox";
			this.HideCheckBox.Size = new System.Drawing.Size(98, 17);
			this.HideCheckBox.TabIndex = 32;
			this.HideCheckBox.Text = "Hide on startup";
			this.HideCheckBox.UseVisualStyleBackColor = true;
			this.HideCheckBox.CheckedChanged += new System.EventHandler(HideCheckBox_CheckedChanged);
			this.labelProductName.AutoSize = true;
			this.labelProductName.Location = new System.Drawing.Point(13, 9);
			this.labelProductName.Margin = new System.Windows.Forms.Padding(6, 0, 3, 0);
			this.labelProductName.MaximumSize = new System.Drawing.Size(0, 17);
			this.labelProductName.Name = "labelProductName";
			this.labelProductName.Size = new System.Drawing.Size(72, 13);
			this.labelProductName.TabIndex = 31;
			this.labelProductName.Text = "ProductName";
			this.labelProductName.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			this.labelVersion.AutoSize = true;
			this.labelVersion.Location = new System.Drawing.Point(13, 26);
			this.labelVersion.Margin = new System.Windows.Forms.Padding(6, 0, 3, 0);
			this.labelVersion.MaximumSize = new System.Drawing.Size(0, 17);
			this.labelVersion.Name = "labelVersion";
			this.labelVersion.Size = new System.Drawing.Size(42, 13);
			this.labelVersion.TabIndex = 30;
			this.labelVersion.Text = "Version";
			this.labelVersion.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			base.AcceptButton = this.OkButton;
			base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
			base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
			base.ClientSize = new System.Drawing.Size(401, 261);
			base.ControlBox = false;
			base.Controls.Add(this.panel1);
			base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
			base.Name = "SplashScreen";
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
			this.Text = "SplashScreen";
			base.TopMost = true;
			this.panel1.ResumeLayout(false);
			this.panel1.PerformLayout();
			base.ResumeLayout(false);
		}

		public SplashScreen()
		{
			InitializeComponent();
			labelProductName.Text = Utils.AssemblyProduct;
			labelVersion.Text = $"Version {Utils.AssemblyVersion}";
			richTextBox1.Text = Utils.AssemblyProduct + " contains code from Be.HexEditor covered by this Shared Source license:" + Environment.NewLine + richTextBox1.Text;
			HideCheckBox.Checked = Utils.GetIntOption("Hide SplashScreen", 0) == 1;
		}

		public void CloseAfter(int milliseconds)
		{
			CloseTimer.Interval = milliseconds;
			CloseTimer.Enabled = true;
		}

		private void CloseTimer_Tick(object sender, EventArgs e)
		{
			Close();
		}

		private void OkButton_Click(object sender, EventArgs e)
		{
			Close();
		}

		private void HideCheckBox_CheckedChanged(object sender, EventArgs e)
		{
			Utils.SetIntOption("Hide SplashScreen", HideCheckBox.Checked ? 1 : 0);
		}
	}
	public class Browser : Form
	{
		private class LoadedDataFormat
		{
			public Type Type;

			public string Path;

			public string ClassName;

			public string WhatItCanDetect;

			public override string ToString()
			{
				if (WhatItCanDetect != null && WhatItCanDetect.Length > 0)
				{
					return Path + " : " + ClassName + "(" + WhatItCanDetect + ")";
				}
				return Path + " : " + ClassName;
			}
		}

		private bool _DataFileLoaded;

		private bool _ParserSelected;

		private bool _FileDataChanged;

		private Bitmap ParseImage;

		private byte[] _SearchData;

		private FindDialog FindDialogBox;

		private bool _IgnoreNextVerticalSplitterMove;

		private bool _IgnoreNextHorizontalSplitterMove;

		private int? _SetVerticalSplitterToThisValue = null;

		private int? _SetHorizontalSplitterToThisValue = null;

		private string _ParserNameToAutoSelect = "";

		private string _ParserPath = "";

		private FileInfo _DataFileInfo;

		private DynamicByteProvider ByteProvider;

		private AppData App = new AppData();

		private IContainer components;

		private SplitContainer VerticalSplitContainer;

		private GroupBox groupBox1;

		private MenuStrip BrowserMainMenuStrip;

		private ToolStripMenuItem fileToolStripMenuItem;

		private ToolStripMenuItem OpenDataFile;

		private ToolStripMenuItem Exit;

		private StatusStrip StatusBar;

		private OpenFileDialog OpenDataFileDialog;

		private ToolStripMenuItem SaveDataFile;

		private ToolStripMenuItem SaveDataFileAs;

		private GroupBox FileScanningGroupBox;

		private ToolStripSeparator toolStripMenuItem2;

		private FolderBrowserDialog ParserFolderBrowser;

		private ToolStripStatusLabel TemporaryStatusMessage;

		private Timer StatusBarClearingTimer;

		private ToolStripMenuItem optionsToolStripMenuItem;

		private ToolStripMenuItem Option_DisplayValuesInHex;

		private ToolStripMenuItem Option_TryToDisplayNumbersAsStrings;

		private Splitter HorizontalSplitter;

		private ContextMenuStrip ObjectTreeContextMenu;

		private ToolStripMenuItem ExpandAllNodesInObjectTree;

		private ToolStripMenuItem CollapseAllNodesInObjectTree;

		private ToolStripMenuItem editToolStripMenuItem;

		private ToolStripMenuItem Find;

		private ToolStripMenuItem FindNext;

		private ToolStripMenuItem FindPrevious;

		private ToolStripStatusLabel CurrentPosition;

		private ToolStripStatusLabel ParseTime;

		private ToolStripStatusLabel DetectionTime;

		private ToolStripMenuItem helpToolStripMenuItem;

		private ToolStripMenuItem Help_About;

		private ToolStripStatusLabel DetectionLoadedForLabel;

		private HexBox HexBox;

		private SaveFileDialog SaveDataFileDialog;

		private TreeList DataTL;

		private TreeListColumn DataTLNameColumn;

		private TreeListColumn DataTLValueColumn;

		private TreeListColumn DataTLOffsetColumn;

		private TreeListColumn DataTLSizeColumn;

		private RepositoryItemTextEdit repositoryItemTextEdit1;

		private TreeList ParsingNotesTL;

		private TreeListColumn treeListColumn1;

		private TreeListColumn ParsingNotesOffsetColumn;

		private TreeListColumn ParsingNotesLengthColumn;

		private TreeListColumn treeListColumn4;

		private TreeListColumn treeListColumn5;

		private DefaultToolTipController defaultToolTipController1;

		private ToolStripStatusLabel SelectionLength;

		private ToolStripSeparator toolStripSeparator1;

		private ToolStripMenuItem expandNodeToolStripMenuItem;

		private ToolStripMenuItem collapseNodeToolStripMenuItem;

		private ToolStripSeparator toolStripSeparator2;

		private ToolStripMenuItem gotoOffsetToolStripMenuItem;

		private TreeListColumn DataTLTypeColumn;

		private Panel panel1;

		private Button ParseButton;

		private Label label1;

		private System.Windows.Forms.ComboBox ParserList;

		private ToolStripSeparator toolStripSeparator3;

		private ToolStripMenuItem ExportToXML;

		private ToolStripMenuItem parseDataToXMLToolStripMenuItem;

		private SaveFileDialog ExportFileDialog;

		private Panel ParseImagePanel;

		private ToolStripMenuItem debugToolStripMenuItem;

		private ToolStripMenuItem addParsingNotesToolStripMenuItem;

		private ToolStripMenuItem toolsToolStripMenuItem;

		private ToolStripMenuItem defragToolStripMenuItem;

		private TabControl BottomTC;

		private TabPage ParsingNotesTP;

		private ToolStripMenuItem toolStripMenuItem5;

		private ToolStripMenuItem asdfToolStripMenuItem;

		private ToolStripMenuItem matchingTypesToolStripMenuItem;

		private ToolStripMenuItem matchingValuesToolStripMenuItem;

		private ToolStripMenuItem clearToolStripMenuItem;

		private ToolStripSeparator toolStripSeparator4;

		private ToolStripMenuItem reallocateToolStripMenuItem;

		private ToolStripSeparator toolStripMenuItem3;

		private ToolStripMenuItem optionsToolStripMenuItem1;

		private ToolTipController HexToolTipController;

		public Browser()
		{
			InitializeComponent();
			debugToolStripMenuItem.Visible = false;
			App.DataTL = DataTL;
			App.BottomTC = BottomTC;
			UpdateGUIBasedOnState();
		}

		private void Browser_Load(object sender, EventArgs e)
		{
			if (LoadConfiguration())
			{
				ScanForParsers();
				App.DoInitialized(this);
				UpdateLocation();
			}
		}

		private void OpenDataFile_Click(object sender, EventArgs e)
		{
			if (OpenDataFileDialog.ShowDialog() == DialogResult.OK)
			{
				PerformDataFileOpen(OpenDataFileDialog.FileName);
				Utils.SetStringOption("Last File Opened", _DataFileInfo.FullName);
			}
		}

		private void Exit_Click(object sender, EventArgs e)
		{
			Application.Exit();
		}

		private void ParserList_SelectedIndexChanged(object sender, EventArgs e)
		{
			string value = "";
			if (ParserList.SelectedIndex != -1)
			{
				_ParserSelected = true;
				App.FileParsed = false;
				App.Parser = Activator.CreateInstance(((LoadedDataFormat)ParserList.SelectedItem).Type);
				value = ParserList.SelectedItem.ToString();
				string canDetectString = GetCanDetectString(App.Parser.GetType());
				DetectionLoadedForLabel.Text = "Detection loaded: " + canDetectString;
				DetectionLoadedForLabel.ToolTipText = canDetectString;
			}
			else
			{
				_ParserSelected = false;
				App.Parser = null;
			}
			Utils.SetStringOption("Last Parser Opened", value);
			UpdateGUIBasedOnState();
		}

		private void AddTreeNodes(ParseData data, TreeListNode parentNode)
		{
			parentNode = DataTL.AppendNode(new object[5]
			{
				data.Name,
				data.Value,
				data.Offset,
				data.Length,
				data.GetTypeDisplayName()
			}, parentNode);
			parentNode.Tag = data;
			if (data.HasChildren())
			{
				foreach (ParseData child in data.Children)
				{
					AddTreeNodes(child, parentNode);
				}
			}
			parentNode.Expanded = data.Expanded;
		}

		private void UpdateDataTL()
		{
			DataTL.BeginUpdate();
			DataTL.ClearNodes();
			if (App.ParsedData.HasChildren())
			{
				foreach (ParseData child in App.ParsedData.Children)
				{
					AddTreeNodes(child, null);
				}
			}
			DataTL.EndUpdate();
		}

		private void ParseButton_Click(object sender, EventArgs e)
		{
			if (!_DataFileLoaded || App.Parser == null)
			{
				return;
			}
			MethodInfo method = App.Parser.GetType().GetMethod("Parse");
			DateTime now = DateTime.Now;
			method.Invoke(App.Parser, new object[1] { ByteProvider.Bytes.GetBytes() });
			DateTime now2 = DateTime.Now;
			TimeSpan timeSpan = now2 - now;
			ParseTime.Text = timeSpan.TotalMilliseconds + "ms";
			App.ParsedData = new ParseData();
			ExamineDataStructure(App.Parser, App.ParsedData);
			App.ParsedData.SetDepths(0);
			if (App.ParsedData.HasChildren())
			{
				foreach (ParseData child in App.ParsedData.Children)
				{
					child.Expanded = true;
				}
			}
			UpdateDataTL();
			App.FileParsed = true;
			MethodInfo method2 = App.Parser.GetType().GetMethod("Detect");
			if (method2 != null)
			{
				now = DateTime.Now;
				method2.Invoke(App.Parser, null);
				now2 = DateTime.Now;
				timeSpan = now2 - now;
				DetectionTime.Text = timeSpan.TotalMilliseconds + "ms";
			}
			ClearHighlighting();
			DisplayParsingNotes();
			UpdateNoteRanges();
			UpdateGUIBasedOnState();
			DisplayStatus("Ran parser successfully");
			UpdateLocation();
		}

		private void AddDataRanges(ParseData data)
		{
			int num = 255 - data.Depth * 4;
			int num2 = 255 - data.Depth * 2;
			int num3 = 255 - data.Depth;
			Color bg = Color.FromArgb(255, (num > 0) ? num : 0, (num2 > 0) ? num2 : 0, (num3 > 0) ? num3 : 0);
			HexBox.Ranges.Add((long)data.Offset, (long)data.Length, Color.Black, bg);
			if (data.Children == null)
			{
				return;
			}
			foreach (ParseData child in data.Children)
			{
				AddDataRanges(child);
			}
		}

		private void UpdateNoteRanges()
		{
			HexBox.Ranges.Clear();
			HexBox.Ranges.Add(0L, long.MaxValue, Color.LightGray, Color.DarkGray);
			AddDataRanges(App.ParsedData);
			foreach (TreeListNode node in ParsingNotesTL.Nodes)
			{
				if (node[ParsingNotesOffsetColumn] != null && node[ParsingNotesLengthColumn] != null)
				{
					ulong start = (ulong)node[ParsingNotesOffsetColumn];
					ulong length = (ulong)node[ParsingNotesLengthColumn];
					HexBox.Ranges.Add((long)start, (long)length, Color.White, Color.Red);
				}
			}
			if (!HexBox.Ranges.StrictValidate())
			{
				throw new Exception("highlight range validation error");
			}
			ParseImage = null;
			ParseImagePanel.Invalidate();
			HexBox.Invalidate();
		}

		private void UpdateLocation()
		{
			DisplayCurrentOffset((ulong)((HexBox.SelectionStart >= 0) ? HexBox.SelectionStart : 0), (ulong)HexBox.SelectionLength);
		}

		private void ChangeParserFolder_Click(object sender, EventArgs e)
		{
			ParserFolderBrowser.SelectedPath = _ParserPath;
			if (ParserFolderBrowser.ShowDialog() == DialogResult.OK)
			{
				_ParserPath = ParserFolderBrowser.SelectedPath;
				Utils.SetStringOption("Parser Path", _ParserPath);
				ScanForParsers();
				UpdateGUIBasedOnState();
				DisplayStatus("Using parser path: " + _ParserPath);
			}
		}

		private void StatusBarClearingTimer_Tick(object sender, EventArgs e)
		{
			TemporaryStatusMessage.Text = "";
			StatusBarClearingTimer.Enabled = false;
		}

		private void HexBox_DataChanged(object sender, EventArgs e)
		{
			if (!_FileDataChanged)
			{
				_FileDataChanged = true;
				UpdateGUIBasedOnState();
			}
		}

		private void HexBox_DoubleClick(object sender, EventArgs e)
		{
			HighlightOffsetInObjectTree((ulong)HexBox.SelectionStart, 0uL);
		}

		private void HexBox_SelectionStartChanged(object sender, EventArgs e)
		{
			UpdateLocation();
		}

		private void HexBox_SelectionLengthChanged(object sender, EventArgs e)
		{
			UpdateLocation();
		}

		private void DisplayCurrentOffset(ulong Offset, ulong Length)
		{
			if (Option_DisplayValuesInHex.Checked)
			{
				CurrentPosition.Text = "Offset: 0x" + Offset.ToString("X8");
				SelectionLength.Text = "Length: 0x" + Length.ToString("X8");
				CurrentPosition.ToolTipText = "Offset: " + Offset;
				SelectionLength.ToolTipText = "Length: " + Length;
			}
			else
			{
				CurrentPosition.Text = "Offset: " + Offset;
				SelectionLength.Text = "Length: " + Length;
				CurrentPosition.ToolTipText = "Offset: 0x" + Offset.ToString("X8");
				SelectionLength.ToolTipText = "Length: 0x" + Length.ToString("X8");
			}
		}

		private void Option_DisplayValuesInHex_Click(object sender, EventArgs e)
		{
			Option_DisplayValuesInHex.Checked = !Option_DisplayValuesInHex.Checked;
			Utils.SetIntOption("Display Values In Hex", Option_DisplayValuesInHex.Checked ? 1 : 0);
			UpdateLocation();
		}

		private void Option_TryToDisplayNumbersAsStrings_Click(object sender, EventArgs e)
		{
			Option_TryToDisplayNumbersAsStrings.Checked = !Option_TryToDisplayNumbersAsStrings.Checked;
			Utils.SetIntOption("Try To Display Numbers As Strings", Option_TryToDisplayNumbersAsStrings.Checked ? 1 : 0);
		}

		private void VerticalSplitContainer_SplitterMoved(object sender, SplitterEventArgs e)
		{
			if (_IgnoreNextVerticalSplitterMove)
			{
				_IgnoreNextVerticalSplitterMove = false;
			}
			else
			{
				Utils.SetIntOption("Last Vertical Splitter Position", VerticalSplitContainer.SplitterDistance);
			}
		}

		private void HorizontalSplitter_SplitterMoved(object sender, SplitterEventArgs e)
		{
			if (_IgnoreNextHorizontalSplitterMove)
			{
				_IgnoreNextHorizontalSplitterMove = false;
			}
			else
			{
				Utils.SetIntOption("Last Horizontal Splitter Position", HorizontalSplitter.SplitPosition);
			}
		}

		private void Browser_ResizeEnd(object sender, EventArgs e)
		{
			Utils.SetIntOption("Last Form Height", base.Height);
			Utils.SetIntOption("Last Form Width", base.Width);
		}

		private void Browser_FormClosing(object sender, FormClosingEventArgs e)
		{
			if (_FileDataChanged)
			{
				switch (MessageBox.Show("Save changes to '" + _DataFileInfo.Name + "' before closing?", "Save Changes?", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question))
				{
				case DialogResult.Yes:
					PerformDataFileSave(_DataFileInfo.FullName);
					break;
				case DialogResult.Cancel:
					e.Cancel = true;
					return;
				}
			}
			App.Closing = true;
			Utils.SetIntOption("Last Form Top", base.Top);
			Utils.SetIntOption("Last Form Left", base.Left);
			Utils.SetIntOption("Maximized", (base.WindowState == FormWindowState.Maximized) ? 1 : 0);
		}

		private void ExpandAllNodesInObjectTree_Click(object sender, EventArgs e)
		{
			DataTL.ExpandAll();
		}

		private void CollapseAllNodesInObjectTree_Click(object sender, EventArgs e)
		{
			DataTL.CollapseAll();
		}

		private void expandNodeToolStripMenuItem_Click(object sender, EventArgs e)
		{
			if (DataTL.FocusedNode != null)
			{
				DataTL.FocusedNode.ExpandAll();
			}
			else
			{
				DataTL.ExpandAll();
			}
		}

		private void collapseNodeToolStripMenuItem_Click(object sender, EventArgs e)
		{
			if (DataTL.FocusedNode != null)
			{
				DataTL.FocusedNode.Expanded = false;
			}
			else
			{
				DataTL.CollapseAll();
			}
		}

		private void Find_Click(object sender, EventArgs e)
		{
			if (FindDialogBox == null)
			{
				FindDialogBox = new FindDialog();
				FindDialogBox.StartFind += FindDialogBox_StartFind;
			}
			FindDialogBox.ShowDialog();
		}

		private void FindDialogBox_StartFind(FindDirection Direction, byte[] SearchData, bool CaseSensitive)
		{
			_SearchData = SearchData;
			DoFind(Direction);
		}

		private void FindNext_Click(object sender, EventArgs e)
		{
			DoFind(FindDirection.Next);
		}

		private void FindPrevious_Click(object sender, EventArgs e)
		{
			DoFind(FindDirection.Previous);
		}

		private void Browser_Shown(object sender, EventArgs e)
		{
			if (_SetHorizontalSplitterToThisValue.HasValue)
			{
				HorizontalSplitter.SplitPosition = _SetHorizontalSplitterToThisValue.Value;
			}
			if (_SetVerticalSplitterToThisValue.HasValue)
			{
				VerticalSplitContainer.SplitterDistance = _SetVerticalSplitterToThisValue.Value;
			}
			_SetVerticalSplitterToThisValue = null;
			_SetHorizontalSplitterToThisValue = null;
		}

		private void Help_About_Click(object sender, EventArgs e)
		{
			new AboutBox().ShowDialog();
		}

		private void SaveDataFile_Click(object sender, EventArgs e)
		{
			PerformDataFileSave(_DataFileInfo.FullName);
		}

		private void SaveDataFileAs_Click(object sender, EventArgs e)
		{
			PerformDataFileSaveAs();
		}

		private void Option_DisplayValuesInHex_CheckedChanged(object sender, EventArgs e)
		{
			if (Option_DisplayValuesInHex.Checked)
			{
				DataTLOffsetColumn.Format.FormatString = "0x{0:x8}";
				DataTLSizeColumn.Format.FormatString = "0x{0:x8}";
				ParsingNotesOffsetColumn.Format.FormatString = "0x{0:x8}";
				ParsingNotesLengthColumn.Format.FormatString = "0x{0:x8}";
			}
			else
			{
				DataTLOffsetColumn.Format.FormatString = "";
				DataTLSizeColumn.Format.FormatString = "";
				ParsingNotesOffsetColumn.Format.FormatString = "";
				ParsingNotesLengthColumn.Format.FormatString = "";
			}
		}

		private void DataTL_MouseDoubleClick(object sender, MouseEventArgs e)
		{
			if (DataTL.FocusedNode == null)
			{
				return;
			}
			object value = DataTL.FocusedNode.GetValue(DataTLOffsetColumn);
			object value2 = DataTL.FocusedNode.GetValue(DataTLSizeColumn);
			if (value != null && value2 != null)
			{
				bool flag = HexBox.IsRangeVisible((long)(ulong)value, (long)(ulong)value2);
				HexBox.SelectionStart = (long)(ulong)value;
				HexBox.SelectionLength = (long)(ulong)value2;
				if (!flag)
				{
					HexBox.ScrollByteIntoCenter(HexBox.SelectionStart);
				}
			}
		}

		private void ParsingNotesTL_MouseDoubleClick(object sender, MouseEventArgs e)
		{
			if (ParsingNotesTL.FocusedNode != null && ParsingNotesTL.FocusedNode[ParsingNotesOffsetColumn] != null)
			{
				ulong num = Convert.ToUInt64(ParsingNotesTL.FocusedNode[ParsingNotesOffsetColumn]);
				ulong num2 = Convert.ToUInt64(ParsingNotesTL.FocusedNode[ParsingNotesLengthColumn]);
				bool flag = HexBox.IsRangeVisible((long)num, (long)num2);
				HexBox.SelectionStart = (long)num;
				HexBox.SelectionLength = (long)num2;
				HighlightOffsetInObjectTree(num, num2);
				if (!flag)
				{
					HexBox.ScrollByteIntoCenter(HexBox.SelectionStart);
				}
			}
		}

		private void DataTL_MouseUp(object sender, MouseEventArgs e)
		{
			TreeList treeList = sender as TreeList;
			if (e.Button == MouseButtons.Right && Control.ModifierKeys == Keys.None && treeList.State == TreeListState.Regular)
			{
				Point pt = treeList.PointToClient(Control.MousePosition);
				TreeListHitInfo treeListHitInfo = treeList.CalcHitInfo(pt);
				if (treeListHitInfo.HitInfoType == HitInfoType.Cell)
				{
					treeList.FocusedNode = treeListHitInfo.Node;
				}
			}
		}

		private bool LoadConfiguration()
		{
			_ParserPath = Utils.GetStringOption("Parser Path", "");
			Option_DisplayValuesInHex.Checked = Utils.GetIntOption("Display Values In Hex", 0) == 1;
			Option_TryToDisplayNumbersAsStrings.Checked = Utils.GetIntOption("Try To Display Numbers As Strings", 0) == 1;
			string[] commandLineArgs = Environment.GetCommandLineArgs();
			if (Utils.GetBoolOption("Reopen Last File On Start", defaultValue: false) || commandLineArgs.Length > 1)
			{
				string text = ((commandLineArgs.Length <= 1) ? Utils.GetStringOption("Last File Opened", "") : commandLineArgs[1]);
				if (text != "" && System.IO.File.Exists(text))
				{
					PerformDataFileOpen(text);
				}
			}
			_ParserNameToAutoSelect = Utils.GetStringOption("Last Parser Opened", "");
			Rectangle b = new Rectangle(Utils.GetIntOption("Last Form Left", base.Left), Utils.GetIntOption("Last Form Top", base.Top), Utils.GetIntOption("Last Form Width", base.Width), Utils.GetIntOption("Last Form Height", base.Height));
			int num = 0;
			Screen[] allScreens = Screen.AllScreens;
			foreach (Screen screen in allScreens)
			{
				Rectangle rectangle = Rectangle.Intersect(screen.WorkingArea, b);
				num += rectangle.Width * rectangle.Height;
			}
			if ((double)num > (double)(b.Width * b.Height) * 0.5)
			{
				base.Top = b.Top;
				base.Left = b.Left;
				base.Height = b.Height;
				if (b.Width != base.Width)
				{
					_IgnoreNextVerticalSplitterMove = true;
					base.Width = b.Width;
				}
			}
			if (Utils.GetIntOption("Maximized", 0) == 1)
			{
				base.WindowState = FormWindowState.Maximized;
				Refresh();
			}
			if (Utils.HasOption("Last Vertical Splitter Position"))
			{
				_SetVerticalSplitterToThisValue = Utils.GetIntOption("Last Vertical Splitter Position", 100);
			}
			if (Utils.HasOption("Last Horizontal Splitter Position"))
			{
				_SetHorizontalSplitterToThisValue = Utils.GetIntOption("Last Horizontal Splitter Position", 100);
			}
			if (_ParserPath == "" || !Directory.Exists(_ParserPath))
			{
				_ParserPath = Path.GetDirectoryName(Application.ExecutablePath);
			}
			App.DoConfigurationLoaded(this);
			return true;
		}

		private void ScanForParsers()
		{
			if (!Directory.Exists(_ParserPath))
			{
				ChangeParserFolder_Click(null, null);
				return;
			}
			_ParserSelected = false;
			App.FileParsed = false;
			ParserList.Items.Clear();
			ScanDirectoryForParsers(_ParserPath);
			_ParserNameToAutoSelect = "";
		}

		private void ScanDirectoryForParsers(string path)
		{
			string[] files = Directory.GetFiles(path, "*.dll", SearchOption.AllDirectories);
			string[] array = files;
			foreach (string text in array)
			{
				if (text.Substring(path.Length).ToUpper().Contains("\\OBJ\\DEBUG\\") || text.Substring(path.Length).ToUpper().Contains("\\OBJ\\RELEASE\\"))
				{
					continue;
				}
				try
				{
					Assembly assembly = Assembly.LoadFrom(text);
					Type[] types = assembly.GetTypes();
					Type[] array2 = types;
					foreach (Type type in array2)
					{
						if (IsSubClassOfGUTDataFormat(type))
						{
							string canDetectString = GetCanDetectString(type);
							LoadedDataFormat loadedDataFormat = new LoadedDataFormat();
							loadedDataFormat.Type = type;
							loadedDataFormat.ClassName = type.Name;
							loadedDataFormat.WhatItCanDetect = canDetectString;
							loadedDataFormat.Path = text.Substring(path.Length + 1);
							if (loadedDataFormat.Path.ToUpper().Contains("\\DETECTION\\MSRC"))
							{
								loadedDataFormat.Path = loadedDataFormat.Path.Substring(loadedDataFormat.Path.ToUpper().IndexOf("\\DETECTION\\MSRC") + 11);
							}
							if (loadedDataFormat.Path.ToUpper().Contains("\\DETECTION\\CVE"))
							{
								loadedDataFormat.Path = loadedDataFormat.Path.Substring(loadedDataFormat.Path.ToUpper().IndexOf("\\DETECTION\\CVE") + 11);
							}
							ParserList.Items.Add(loadedDataFormat);
						}
					}
				}
				catch
				{
				}
			}
			for (int k = 0; k < ParserList.Items.Count; k++)
			{
				LoadedDataFormat loadedDataFormat2 = (LoadedDataFormat)ParserList.Items[k];
				for (int l = 0; l < ParserList.Items.Count; l++)
				{
					LoadedDataFormat loadedDataFormat3 = (LoadedDataFormat)ParserList.Items[l];
					if (l != k && loadedDataFormat3.WhatItCanDetect == "" && loadedDataFormat3.Type.IsAssignableFrom(loadedDataFormat2.Type))
					{
						ParserList.Items.RemoveAt(l);
						l--;
						k--;
					}
				}
			}
			foreach (LoadedDataFormat item in ParserList.Items)
			{
				if (item.ToString() == _ParserNameToAutoSelect)
				{
					ParserList.SelectedItem = item;
					ParseButton_Click(this, null);
				}
			}
		}

		private void ExamineDataStructure(object Structure, ParseData treeData)
		{
			if (Structure == null)
			{
				return;
			}
			ulong offset = (ulong)Structure.GetType().GetProperty("DataStructureOffset").GetValue(Structure, null);
			ulong length = (ulong)Structure.GetType().GetProperty("DataStructureLength").GetValue(Structure, null);
			treeData.Structure = Structure;
			treeData.Value = (string)Structure.GetType().InvokeMember("ToString", BindingFlags.InvokeMethod, null, Structure, null);
			treeData.Offset = offset;
			treeData.Length = length;
			FieldInfo[] fieldsInTheCorrectOrder = GetFieldsInTheCorrectOrder(Structure.GetType());
			if (fieldsInTheCorrectOrder == null)
			{
				return;
			}
			int num = 0;
			FieldInfo[] array = fieldsInTheCorrectOrder;
			foreach (FieldInfo fieldInfo in array)
			{
				if (fieldInfo == null)
				{
					continue;
				}
				if (IsSubClassOfGUTDataItem(fieldInfo.FieldType))
				{
					object value = fieldInfo.GetValue(Structure);
					if (value == null)
					{
						continue;
					}
					PropertyInfo property = fieldInfo.FieldType.GetProperty("Value");
					object value2 = property.GetValue(value, null);
					PropertyInfo property2 = fieldInfo.FieldType.GetProperty("Offset");
					ulong offset2 = (ulong)property2.GetValue(value, null);
					PropertyInfo property3 = fieldInfo.FieldType.GetProperty("Length");
					ulong length2 = (ulong)property3.GetValue(value, null);
					ParseData parseData = new ParseData();
					parseData.Structure = value;
					parseData.Name = fieldInfo.Name;
					parseData.Value = value2;
					parseData.Offset = offset2;
					parseData.Length = length2;
					treeData.AddChild(parseData);
					object[] customAttributes = fieldInfo.GetCustomAttributes(inherit: true);
					foreach (object obj in customAttributes)
					{
						if (obj.GetType().FullName == "GUT.Architecture.BitFieldSize")
						{
							parseData.BitFieldShift = num;
							parseData.BitFieldSize = (int)(uint)obj.GetType().GetField("NumberOfBits").GetValue(obj);
							num += parseData.BitFieldSize;
						}
						else if (obj.GetType().FullName == "GUT.Architecture.BeginBitField")
						{
							parseData.BitFieldShift = 0;
							num = 0;
							num += parseData.BitFieldSize;
						}
					}
				}
				else if (IsSubClassOfGUTDataStructure(fieldInfo.FieldType) || fieldInfo.FieldType.FullName == "GUT.Architecture.DataStructure")
				{
					object value3 = fieldInfo.GetValue(Structure);
					if (value3 != null)
					{
						ParseData parseData2 = new ParseData();
						parseData2.Name = fieldInfo.Name;
						ExamineDataStructure(value3, parseData2);
						treeData.AddChild(parseData2);
					}
				}
				else
				{
					if (!(fieldInfo.FieldType.Name == "List`1") || !fieldInfo.FieldType.IsGenericType)
					{
						continue;
					}
					Type[] genericArguments = fieldInfo.FieldType.GetGenericArguments();
					if (genericArguments.Length != 1)
					{
						continue;
					}
					if (genericArguments[0].FullName == "GUT.Architecture.DataStructure" || IsSubClassOfGUTDataStructure(genericArguments[0]))
					{
						IEnumerable enumerable = (IEnumerable)fieldInfo.GetValue(Structure);
						if (enumerable != null)
						{
							ParseData parseData3 = new ParseData();
							parseData3.Name = fieldInfo.Name;
							parseData3.Structure = enumerable;
							int num2 = 0;
							foreach (object item in enumerable)
							{
								if (item != null)
								{
									ParseData parseData4 = new ParseData();
									parseData4.Name = item.GetType().Name + "[" + num2 + "]";
									ExamineDataStructure(item, parseData4);
									parseData3.AddChild(parseData4);
									num2++;
								}
							}
							parseData3.Name = fieldInfo.Name + "[" + num2 + "]";
							parseData3.CalculateSizeFromChildren();
							treeData.AddChild(parseData3);
						}
					}
					if (!IsSubClassOfGUTDataItem(genericArguments[0]))
					{
						continue;
					}
					IEnumerable enumerable2 = (IEnumerable)fieldInfo.GetValue(Structure);
					if (enumerable2 == null)
					{
						continue;
					}
					ParseData parseData5 = new ParseData();
					parseData5.Name = fieldInfo.Name;
					parseData5.Structure = enumerable2;
					int num3 = 0;
					foreach (object item2 in enumerable2)
					{
						ParseData parseData6 = new ParseData();
						parseData6.Name = fieldInfo.Name + "[" + num3 + "]";
						parseData6.Structure = item2;
						parseData6.Value = item2.GetType().GetProperty("Value").GetValue(item2, null);
						parseData6.Offset = (ulong)item2.GetType().GetProperty("Offset").GetValue(item2, null);
						parseData6.Length = (ulong)item2.GetType().GetProperty("Length").GetValue(item2, null);
						parseData5.AddChild(parseData6);
						num3++;
					}
					parseData5.Name = fieldInfo.Name + "[" + num3 + "]";
					parseData5.CalculateSizeFromChildren();
					treeData.AddChild(parseData5);
				}
			}
		}

		private void UpdateGUIBasedOnState()
		{
			ParseButton.Enabled = _ParserSelected && _DataFileLoaded;
			SaveDataFile.Enabled = _DataFileLoaded;
			SaveDataFileAs.Enabled = _DataFileLoaded;
			ExportToXML.Enabled = _DataFileLoaded && App.FileParsed;
			Text = (_FileDataChanged ? (Utils.AssemblyProduct + ": *") : (Utils.AssemblyProduct + ": "));
			if (_DataFileLoaded && _DataFileInfo != null)
			{
				Text += _DataFileInfo.Name;
			}
			if (!App.FileParsed)
			{
				DataTL.ClearNodes();
				ParsingNotesTL.ClearNodes();
				ClearHighlighting();
			}
			App.DoStateChanged(this);
		}

		private void DisplayParsingNotes()
		{
			MethodInfo method = App.Parser.GetType().GetMethod("GetParsingNotes");
			IEnumerable enumerable = (IEnumerable)method.Invoke(App.Parser, null);
			ParsingNotesTL.ClearNodes();
			Dictionary<string, bool> dictionary = new Dictionary<string, bool>();
			foreach (object item in enumerable)
			{
				string text = (string)item.GetType().GetProperty("VulnerabilityID").GetValue(item, null);
				string text2 = (string)item.GetType().GetProperty("Text").GetValue(item, null);
				string text3 = item.GetType().GetProperty("Type").GetValue(item, null)
					.ToString();
				object value = item.GetType().GetProperty("Offset").GetValue(item, null);
				object value2 = item.GetType().GetProperty("Length").GetValue(item, null);
				string key = $"{text3}{value}{value2}{text}{text2}";
				if (!dictionary.ContainsKey(key))
				{
					dictionary[key] = true;
					ParsingNotesTL.AppendNode(new object[5] { text3, value, value2, text, text2 }, null);
				}
			}
		}

		private void DisplayStatus(string Status)
		{
			StatusBarClearingTimer.Enabled = false;
			TemporaryStatusMessage.Text = Status;
			StatusBarClearingTimer.Enabled = true;
		}

		private string GetCanDetectString(Type TheType)
		{
			List<string> list = new List<string>();
			list.AddRange(GetCanDetectList(TheType));
			MethodInfo[] methods = TheType.GetMethods();
			MethodInfo[] array = methods;
			foreach (MethodInfo theMethod in array)
			{
				list.AddRange(GetCanDetectList(theMethod));
			}
			string text = "";
			foreach (string item in list)
			{
				text = text + item + ", ";
			}
			if (text.Length >= 2)
			{
				text = text.Substring(0, text.Length - 2);
			}
			return text;
		}

		private List<string> GetCanDetectList(Type TheType)
		{
			return GetCanDetectList(Attribute.GetCustomAttributes(TheType));
		}

		private List<string> GetCanDetectList(MethodInfo TheMethod)
		{
			return GetCanDetectList(Attribute.GetCustomAttributes(TheMethod));
		}

		private List<string> GetCanDetectList(Attribute[] Attributes)
		{
			List<string> list = new List<string>();
			foreach (Attribute attribute in Attributes)
			{
				if (attribute.GetType().Name == "CanDetect")
				{
					FieldInfo field = attribute.GetType().GetField("VulnerabilityID");
					list.Add((string)field.GetValue(attribute));
				}
			}
			return list;
		}

		private bool IsSubClassOfClassByName(Type TheType, string BaseClassName)
		{
			if (TheType.BaseType == null)
			{
				return false;
			}
			for (TheType = TheType.BaseType; TheType != null; TheType = TheType.BaseType)
			{
				if (TheType.FullName == BaseClassName)
				{
					return true;
				}
			}
			return false;
		}

		private bool IsSubClassOfGUTDataFormat(Type TheType)
		{
			return IsSubClassOfClassByName(TheType, "GUT.Architecture.DataFormat");
		}

		private bool IsSubClassOfGUTDataStructure(Type TheType)
		{
			return IsSubClassOfClassByName(TheType, "GUT.Architecture.DataStructure");
		}

		private bool IsSubClassOfGUTDataItem(Type TheType)
		{
			return IsSubClassOfClassByName(TheType, "GUT.Architecture.DataItem");
		}

		private TreeListNode FindNodeAtOffset(ulong offset, TreeListNodes nodes)
		{
			TreeListNode treeListNode = null;
			foreach (TreeListNode node in nodes)
			{
				if (node[DataTLOffsetColumn] == null || node[DataTLSizeColumn] == null)
				{
					continue;
				}
				ulong num = (ulong)node[DataTLOffsetColumn];
				ulong num2 = (ulong)node[DataTLSizeColumn];
				if (offset >= num && offset < num + num2)
				{
					TreeListNode treeListNode3 = FindNodeAtOffset(offset, node.Nodes);
					if (treeListNode3 == null)
					{
						treeListNode3 = node;
					}
					if (treeListNode == null)
					{
						treeListNode = treeListNode3;
					}
					else if (treeListNode3.Level > treeListNode.Level)
					{
						treeListNode = treeListNode3;
					}
				}
			}
			return treeListNode;
		}

		private int NodeFit(TreeListNode node, ulong targetOffset, ulong targetLength)
		{
			if (node != null && node[DataTLOffsetColumn] != null && node[DataTLSizeColumn] != null)
			{
				ulong num = (ulong)node[DataTLOffsetColumn];
				ulong num2 = (ulong)node[DataTLSizeColumn];
				if (targetOffset >= num && targetOffset + targetLength <= num + num2 && targetOffset < num + num2)
				{
					return int.MaxValue - (int)(num - targetOffset) - (int)(num2 - targetLength);
				}
			}
			return -2147483647;
		}

		private TreeListNode FindNodeAtOffset2(ulong offset, ulong length, TreeListNode n, TreeListNodes nodes)
		{
			TreeListNode result = n;
			int num = NodeFit(n, offset, length);
			foreach (TreeListNode node in nodes)
			{
				TreeListNode treeListNode2 = FindNodeAtOffset2(offset, length, node, node.Nodes);
				if (treeListNode2 != null)
				{
					int num2 = NodeFit(treeListNode2, offset, length);
					if (num2 >= num)
					{
						num = num2;
						result = treeListNode2;
					}
				}
			}
			if (num >= 0)
			{
				return result;
			}
			return null;
		}

		private void HighlightOffsetInObjectTree(ulong Offset, ulong Length)
		{
			TreeListNode treeListNode = FindNodeAtOffset2(Offset, Length, null, DataTL.Nodes);
			DataTL.Selection.Clear();
			if (treeListNode != null)
			{
				DataTL.MakeNodeVisible(treeListNode);
				DataTL.FocusedNode = treeListNode;
			}
		}

		private FieldInfo[] GetFieldsInTheCorrectOrder(Type TheType)
		{
			FieldInfo[] fields = TheType.GetFields();
			FieldInfo[] array = new FieldInfo[fields.Length];
			List<FieldInfo> list = new List<FieldInfo>();
			int num = -1;
			FieldInfo[] array2 = fields;
			foreach (FieldInfo fieldInfo in array2)
			{
				object[] customAttributes = fieldInfo.GetCustomAttributes(inherit: false);
				int? num2 = null;
				bool flag = false;
				object[] array3 = customAttributes;
				foreach (object obj in array3)
				{
					if (obj.GetType().Name == "Order")
					{
						FieldInfo field = obj.GetType().GetField("OrderIndex");
						num2 = (int)(ulong)field.GetValue(obj);
					}
					if (obj.GetType().Name == "SuppressDisplay")
					{
						flag = true;
					}
				}
				if (flag)
				{
					continue;
				}
				if (num2.HasValue)
				{
					if (num2 >= array.Length)
					{
						MessageBox.Show("The field " + fieldInfo.Name + " in " + TheType.Name + " contained an invalid [Order()] value!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Hand);
						return null;
					}
					if (array[num2.Value] != null)
					{
						MessageBox.Show("The field " + fieldInfo.Name + " in " + TheType.Name + " contained a duplicate [Order()] value!", "Error", MessageBoxButtons.OK, MessageBoxIcon.Hand);
						return null;
					}
					array[num2.Value] = fieldInfo;
					if (num2 > num)
					{
						num = num2.Value;
					}
				}
				else
				{
					list.Add(fieldInfo);
				}
			}
			int num3 = num;
			foreach (FieldInfo item in list)
			{
				array[++num3] = item;
			}
			return array;
		}

		public byte[] ReadAllBytes(string path)
		{
			using FileStream fileStream = new FileStream(path, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
			int num = 0;
			long length = fileStream.Length;
			if (length > int.MaxValue)
			{
				throw new IOException("File too long");
			}
			int num2 = (int)length;
			byte[] array = new byte[num2];
			while (num2 > 0)
			{
				int num3 = fileStream.Read(array, num, num2);
				if (num3 == 0)
				{
					throw new InvalidOperationException("End of file reached before expected");
				}
				num += num3;
				num2 -= num3;
			}
			return array;
		}

		private void PerformDataFileOpen(string FileName)
		{
			if (_FileDataChanged)
			{
				switch (MessageBox.Show("Save changes to " + _DataFileInfo.Name + "?", "Save Changes?", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question))
				{
				case DialogResult.Yes:
					PerformDataFileSave(_DataFileInfo.FullName);
					break;
				case DialogResult.Cancel:
					return;
				}
			}
			if (System.IO.File.Exists(FileName))
			{
				try
				{
					_FileDataChanged = false;
					_DataFileInfo = new FileInfo(FileName);
					ByteProvider = new DynamicByteProvider(ReadAllBytes(_DataFileInfo.FullName));
					ByteProvider.Changed += HexBox_DataChanged;
					HexBox.ByteProvider = ByteProvider;
					GC.Collect();
					_DataFileLoaded = true;
					App.FileParsed = false;
				}
				catch (IOException ex)
				{
					MessageBox.Show(ex.Message, "Error opening file", MessageBoxButtons.OK, MessageBoxIcon.Hand);
					_FileDataChanged = false;
					_DataFileLoaded = false;
				}
			}
			else
			{
				_FileDataChanged = false;
				_DataFileLoaded = false;
			}
			UpdateGUIBasedOnState();
		}

		private void PerformDataFileSave(string FileName)
		{
			try
			{
				System.IO.File.WriteAllBytes(FileName, ByteProvider.Bytes.GetBytes());
				_FileDataChanged = false;
				_DataFileInfo = new FileInfo(FileName);
				UpdateGUIBasedOnState();
			}
			catch (IOException ex)
			{
				MessageBox.Show(ex.Message, "Error saving file", MessageBoxButtons.OK, MessageBoxIcon.Hand);
			}
		}

		private void PerformDataFileSaveAs()
		{
			if (SaveDataFileDialog.ShowDialog() == DialogResult.OK)
			{
				PerformDataFileSave(SaveDataFileDialog.FileName);
			}
		}

		private void DoFind(FindDirection Direction)
		{
			if (_SearchData == null || _SearchData.Length <= 0)
			{
				return;
			}
			ulong num = 0uL;
			switch (Direction)
			{
			case FindDirection.Next:
				num = (ulong)(HexBox.SelectionStart + 1);
				break;
			case FindDirection.Previous:
				num = (ulong)((HexBox.SelectionStart > 0) ? (HexBox.SelectionStart - 1) : 0);
				break;
			}
			if (num < 0 || num >= (ulong)((long)ByteProvider.Bytes.Count - 1L))
			{
				return;
			}
			long num2 = ((Direction == FindDirection.Next) ? 1 : (-1));
			for (long num3 = (long)num; num3 >= 0 && num3 < (long)ByteProvider.Bytes.Count - (long)_SearchData.Length - 1; num3 += num2)
			{
				if (_SearchData[0] != ByteProvider.Bytes[(int)num3])
				{
					continue;
				}
				bool flag = true;
				for (ulong num4 = 1uL; num4 < (ulong)_SearchData.Length; num4++)
				{
					if (_SearchData[num4] != ByteProvider.Bytes[(int)num3 + (int)num4])
					{
						flag = false;
						break;
					}
				}
				if (flag)
				{
					HighlightOffsetInObjectTree((ulong)num3, (ulong)_SearchData.Length);
					HexBox.SelectionStart = num3;
					HexBox.SelectionLength = _SearchData.Length;
					break;
				}
			}
		}

		private void gotoOffsetToolStripMenuItem_Click(object sender, EventArgs e)
		{
			GotoDialog gotoDialog = new GotoDialog();
			if (gotoDialog.ShowDialog() == DialogResult.OK)
			{
				ulong offset = gotoDialog.GetOffset((ulong)HexBox.SelectionStart);
				HighlightOffsetInObjectTree(offset, 0uL);
				HexBox.SelectionStart = (long)offset;
				HexBox.SelectionLength = 0L;
			}
		}

		private void DataTL_ShowingEditor(object sender, CancelEventArgs e)
		{
			e.Cancel = true;
			if (DataTL.FocusedNode == null || DataTL.FocusedNode.Tag == null)
			{
				return;
			}
			ParseData parseData = (ParseData)DataTL.FocusedNode.Tag;
			if (parseData.Structure != null)
			{
				switch (parseData.Structure.GetType().Name)
				{
				case "DataItem_UInt8":
				case "DataItem_UInt16":
				case "DataItem_UInt32":
				case "DataItem_UInt64":
				case "DataItem_Int8":
				case "DataItem_Int16":
				case "DataItem_Int32":
				case "DataItem_Int64":
					e.Cancel = false;
					break;
				}
			}
		}

		private long ParseSigned(string value)
		{
			try
			{
				if (value.StartsWith("0x", StringComparison.InvariantCultureIgnoreCase))
				{
					return long.Parse(value.Substring(2), NumberStyles.HexNumber);
				}
				return long.Parse(value);
			}
			catch (Exception)
			{
				throw new Exception("Input is not a decimal number.  Prefix with 0x for hexidecimal");
			}
		}

		private ulong ParseUnsigned(string value)
		{
			try
			{
				if (value.StartsWith("0x", StringComparison.InvariantCultureIgnoreCase))
				{
					return ulong.Parse(value.Substring(2), NumberStyles.HexNumber);
				}
				return ulong.Parse(value);
			}
			catch (Exception)
			{
				throw new Exception("Input is not a decimal number.  Prefix with 0x for hexidecimal");
			}
		}

		private void DataTL_CellValueChanged(object sender, CellValueChangedEventArgs e)
		{
			if (e.Node != null && e.Node.Tag != null)
			{
				ParseData parseData = (ParseData)e.Node.Tag;
				object structure = parseData.Structure;
				if (structure != null)
				{
					MemoryStream memoryStream = new MemoryStream();
					BinaryWriter binaryWriter = new BinaryWriter(memoryStream);
					MemoryStream memoryStream2 = new MemoryStream();
					BinaryWriter binaryWriter2 = new BinaryWriter(memoryStream2);
					object obj = null;
					bool flag = false;
					switch (structure.GetType().Name)
					{
					case "DataItem_Int8":
					case "DataItem_BEInt8":
					{
						sbyte b3 = (sbyte)parseData.GetUnshiftedBitFieldMask();
						sbyte b4 = (sbyte)ParseSigned(e.Value.ToString());
						b4 &= b3;
						obj = b4;
						b4 = (sbyte)(b4 << parseData.BitFieldShift);
						b3 = (sbyte)(b3 << parseData.BitFieldShift);
						binaryWriter.Write(b4);
						binaryWriter2.Write(b3);
						break;
					}
					case "DataItem_Int16":
					{
						short num19 = (short)parseData.GetUnshiftedBitFieldMask();
						short num20 = (short)ParseSigned(e.Value.ToString());
						num20 &= num19;
						obj = num20;
						num20 = (short)(num20 << parseData.BitFieldShift);
						num19 = (short)(num19 << parseData.BitFieldShift);
						binaryWriter.Write(num20);
						binaryWriter2.Write(num19);
						break;
					}
					case "DataItem_Int32":
					{
						int num17 = (int)parseData.GetUnshiftedBitFieldMask();
						int num18 = (int)ParseSigned(e.Value.ToString());
						num18 &= num17;
						obj = num18;
						num18 <<= parseData.BitFieldShift;
						num17 <<= parseData.BitFieldShift;
						binaryWriter.Write(num18);
						binaryWriter2.Write(num17);
						break;
					}
					case "DataItem_Int64":
					{
						long unshiftedBitFieldMask4 = (long)parseData.GetUnshiftedBitFieldMask();
						long num16 = ParseSigned(e.Value.ToString());
						num16 &= unshiftedBitFieldMask4;
						obj = num16;
						num16 <<= parseData.BitFieldShift;
						unshiftedBitFieldMask4 <<= parseData.BitFieldShift;
						binaryWriter.Write(num16);
						binaryWriter2.Write(unshiftedBitFieldMask4);
						break;
					}
					case "DataItem_UInt8":
					case "DataItem_BEUInt8":
					{
						byte b = (byte)parseData.GetUnshiftedBitFieldMask();
						byte b2 = (byte)ParseUnsigned(e.Value.ToString());
						b2 &= b;
						obj = b2;
						b2 = (byte)(b2 << parseData.BitFieldShift);
						b = (byte)(b << parseData.BitFieldShift);
						binaryWriter.Write(b2);
						binaryWriter2.Write(b);
						break;
					}
					case "DataItem_UInt16":
					{
						ushort num14 = (ushort)parseData.GetUnshiftedBitFieldMask();
						ushort num15 = (ushort)ParseUnsigned(e.Value.ToString());
						num15 &= num14;
						obj = num15;
						num15 = (ushort)(num15 << parseData.BitFieldShift);
						num14 = (ushort)(num14 << parseData.BitFieldShift);
						binaryWriter.Write(num15);
						binaryWriter2.Write(num14);
						break;
					}
					case "DataItem_UInt32":
					{
						uint num12 = (uint)parseData.GetUnshiftedBitFieldMask();
						uint num13 = (uint)ParseUnsigned(e.Value.ToString());
						num13 &= num12;
						obj = num13;
						num13 <<= parseData.BitFieldShift;
						num12 <<= parseData.BitFieldShift;
						binaryWriter.Write(num13);
						binaryWriter2.Write(num12);
						break;
					}
					case "DataItem_UInt64":
					{
						ulong unshiftedBitFieldMask3 = parseData.GetUnshiftedBitFieldMask();
						ulong num11 = ParseUnsigned(e.Value.ToString());
						num11 &= unshiftedBitFieldMask3;
						obj = num11;
						num11 <<= parseData.BitFieldShift;
						unshiftedBitFieldMask3 <<= parseData.BitFieldShift;
						binaryWriter.Write(num11);
						binaryWriter2.Write(unshiftedBitFieldMask3);
						break;
					}
					case "DataItem_BEInt16":
					{
						short num9 = (short)parseData.GetUnshiftedBitFieldMask();
						short num10 = (short)ParseSigned(e.Value.ToString());
						num10 &= num9;
						obj = num10;
						num10 = (short)(num10 << parseData.BitFieldShift);
						num9 = (short)(num9 << parseData.BitFieldShift);
						binaryWriter.Write(num10);
						binaryWriter2.Write(num9);
						flag = true;
						break;
					}
					case "DataItem_BEInt32":
					{
						int num7 = (int)parseData.GetUnshiftedBitFieldMask();
						int num8 = (int)ParseSigned(e.Value.ToString());
						num8 &= num7;
						obj = num8;
						num8 <<= parseData.BitFieldShift;
						num7 <<= parseData.BitFieldShift;
						binaryWriter.Write(num8);
						binaryWriter2.Write(num7);
						flag = true;
						break;
					}
					case "DataItem_BEInt64":
					{
						long unshiftedBitFieldMask2 = (long)parseData.GetUnshiftedBitFieldMask();
						long num6 = ParseSigned(e.Value.ToString());
						num6 &= unshiftedBitFieldMask2;
						obj = num6;
						num6 <<= parseData.BitFieldShift;
						unshiftedBitFieldMask2 <<= parseData.BitFieldShift;
						binaryWriter.Write(num6);
						binaryWriter2.Write(unshiftedBitFieldMask2);
						flag = true;
						break;
					}
					case "DataItem_BEUInt16":
					{
						ushort num4 = (ushort)parseData.GetUnshiftedBitFieldMask();
						ushort num5 = (ushort)ParseUnsigned(e.Value.ToString());
						num5 &= num4;
						obj = num5;
						num5 = (ushort)(num5 << parseData.BitFieldShift);
						num4 = (ushort)(num4 << parseData.BitFieldShift);
						binaryWriter.Write(num5);
						binaryWriter2.Write(num4);
						flag = true;
						break;
					}
					case "DataItem_BEUInt32":
					{
						uint num2 = (uint)parseData.GetUnshiftedBitFieldMask();
						uint num3 = (uint)ParseUnsigned(e.Value.ToString());
						num3 &= num2;
						obj = num3;
						num3 <<= parseData.BitFieldShift;
						num2 <<= parseData.BitFieldShift;
						binaryWriter.Write(num3);
						binaryWriter2.Write(num2);
						flag = true;
						break;
					}
					case "DataItem_BEUInt64":
					{
						ulong unshiftedBitFieldMask = parseData.GetUnshiftedBitFieldMask();
						ulong num = ParseUnsigned(e.Value.ToString());
						num &= unshiftedBitFieldMask;
						obj = num;
						num <<= parseData.BitFieldShift;
						unshiftedBitFieldMask <<= parseData.BitFieldShift;
						binaryWriter.Write(num);
						binaryWriter2.Write(unshiftedBitFieldMask);
						flag = true;
						break;
					}
					}
					if (obj != null)
					{
						structure.GetType().GetProperty("Value").SetValue(structure, obj, null);
						parseData.Value = obj;
						e.Node[DataTLValueColumn] = parseData.Value;
						ulong offset = parseData.Offset;
						byte[] buffer = memoryStream.GetBuffer();
						long position = memoryStream.Position;
						byte[] buffer2 = memoryStream2.GetBuffer();
						if (flag)
						{
							for (int i = 0; i < position; i++)
							{
								ByteProvider.Bytes[(int)offset + i] = (byte)((ByteProvider.Bytes[(int)offset + i] & ~buffer2[position - 1 - 1]) | buffer[position - i - 1]);
							}
						}
						else
						{
							for (int j = 0; j < position; j++)
							{
								ByteProvider.Bytes[(int)offset + j] = (byte)((ByteProvider.Bytes[(int)offset + j] & ~buffer2[j]) | buffer[j]);
							}
						}
						HexBox.Refresh();
					}
				}
			}
			if (!_FileDataChanged)
			{
				_FileDataChanged = true;
				UpdateGUIBasedOnState();
			}
		}

		private void DataTL_GetNodeDisplayValue(object sender, GetNodeDisplayValueEventArgs e)
		{
			if (e.Column == DataTLValueColumn && e.Node != null && e.Node.Tag != null)
			{
				ParseData parseData = (ParseData)e.Node.Tag;
				if (parseData.Value != null)
				{
					e.Value = parseData.GetValueString(Option_DisplayValuesInHex.Checked, Option_TryToDisplayNumbersAsStrings.Checked);
				}
			}
		}

		private void WriteXMLData(XmlWriter w, ParseData data)
		{
			w.WriteStartElement("item");
			w.WriteAttributeString("name", data.Name);
			w.WriteAttributeString("type", data.GetTypeDisplayName());
			w.WriteAttributeString("offset", data.Offset.ToString());
			w.WriteAttributeString("size", data.Length.ToString());
			w.WriteAttributeString("value", data.GetValueString(Option_DisplayValuesInHex.Checked, Option_TryToDisplayNumbersAsStrings.Checked));
			if (data.Children != null)
			{
				foreach (ParseData child in data.Children)
				{
					WriteXMLData(w, child);
				}
			}
			w.WriteEndElement();
		}

		private void parseDataToXMLToolStripMenuItem_Click(object sender, EventArgs e)
		{
			if (App.ParsedData != null && ExportFileDialog.ShowDialog() == DialogResult.OK)
			{
				XmlTextWriter xmlTextWriter = new XmlTextWriter(ExportFileDialog.FileName, Encoding.UTF8);
				xmlTextWriter.WriteStartDocument();
				WriteXMLData(xmlTextWriter, App.ParsedData);
				xmlTextWriter.WriteEndDocument();
				xmlTextWriter.Close();
			}
		}

		private void highlightToolStripMenuItem_Click(object sender, EventArgs e)
		{
			if (DataTL.FocusedNode != null && DataTL.FocusedNode.Tag != null)
			{
				ParseData parseData = (ParseData)DataTL.FocusedNode.Tag;
				HexBox.HighlightRanges.Add((long)parseData.Offset, (long)parseData.Length, Color.Black, Color.Yellow);
				HexBox.Invalidate();
				ParseImage = null;
				ParseImagePanel.Invalidate();
			}
		}

		private void ClearHighlighting()
		{
			HexBox.Ranges.Clear();
			HexBox.HighlightRanges.Clear();
			HexBox.Invalidate();
			ParseImage = null;
			ParseImagePanel.Invalidate();
		}

		private void clearHighlightingToolStripMenuItem_Click(object sender, EventArgs e)
		{
			HexBox.HighlightRanges.Clear();
			HexBox.Invalidate();
			ParseImage = null;
			ParseImagePanel.Invalidate();
		}

		private void ParseImagePanel_Paint(object sender, PaintEventArgs e)
		{
			if (ParseImage == null)
			{
				ParseImage = new Bitmap(16, ParseImagePanel.Height);
				HexBox.DrawRanges(ParseImage);
			}
			e.Graphics.DrawImage(ParseImage, 0, 0);
			float num = (float)HexBox.GetFirstVisibleLine() * (float)ParseImage.Height / (float)HexBox.GetLineCount();
			float num2 = (float)HexBox.GetVisibleLineCount() * (float)ParseImage.Height / (float)HexBox.GetLineCount();
			e.Graphics.DrawRectangle(new Pen(Color.Blue), 0f, num, HexBox.BytesPerLine - 1, num2 - 1f);
		}

		private void ParseImagePanel_MouseDown(object sender, MouseEventArgs e)
		{
			if (ParseImage != null)
			{
				long index = (long)((float)e.Y / (float)ParseImage.Height * (float)HexBox.GetLineCount() * (float)HexBox.BytesPerLine);
				HexBox.ScrollByteIntoCenter(index);
				ParseImagePanel.Refresh();
			}
		}

		private void highlightTypeToolStripMenuItem_Click(object sender, EventArgs e)
		{
			if (DataTL.FocusedNode == null || DataTL.FocusedNode.Tag == null)
			{
				return;
			}
			ParseData parseData = (ParseData)DataTL.FocusedNode.Tag;
			List<ParseData> allChildren = App.ParsedData.GetAllChildren();
			string typeDisplayName = parseData.GetTypeDisplayName();
			foreach (ParseData item in allChildren)
			{
				if (item.GetTypeDisplayName() == typeDisplayName)
				{
					HexBox.HighlightRanges.Add((long)item.Offset, (long)item.Length, Color.Black, Color.Yellow);
				}
			}
			if (!HexBox.HighlightRanges.Validate())
			{
				throw new Exception("Error generating highlight ranges");
			}
			HexBox.Invalidate();
			ParseImage = null;
			ParseImagePanel.Invalidate();
		}

		private void HexBox_VisibleBytesChanged(object sender, EventArgs e)
		{
			ParseImagePanel.Invalidate();
		}

		private void ParseImagePanel_MouseMove(object sender, MouseEventArgs e)
		{
			if (e.Button == MouseButtons.Left)
			{
				ParseImagePanel_MouseDown(sender, e);
			}
		}

		private void highlightMatchingValuesToolStripMenuItem_Click(object sender, EventArgs e)
		{
			if (DataTL.FocusedNode == null || DataTL.FocusedNode.Tag == null)
			{
				return;
			}
			ParseData parseData = (ParseData)DataTL.FocusedNode.Tag;
			List<ParseData> allChildren = App.ParsedData.GetAllChildren();
			string valueString = parseData.GetValueString(hex: false, numbersAsStrings: false);
			foreach (ParseData item in allChildren)
			{
				if (item.GetValueString(hex: false, numbersAsStrings: false) == valueString)
				{
					HexBox.HighlightRanges.Add((long)item.Offset, (long)item.Length, Color.Black, Color.Yellow);
				}
			}
			if (!HexBox.HighlightRanges.Validate())
			{
				throw new Exception("Error generating highlight ranges");
			}
			HexBox.Invalidate();
			ParseImage = null;
			ParseImagePanel.Invalidate();
		}

		private void addParsingNotesToolStripMenuItem_Click(object sender, EventArgs e)
		{
			ParsingNotesTL.AppendNode(new object[5] { "sometype", 1024, null, "some vuln id", "some message" }, null);
		}

		private void defragToolStripMenuItem_Click(object sender, EventArgs e)
		{
			OLESS.File file = new OLESS.File();
			MemoryStream memoryStream = new MemoryStream(ByteProvider.Bytes.GetBytes());
			if (file.IsOLESSFile(memoryStream))
			{
				memoryStream.Position = 0L;
				file.LoadFromStream(memoryStream);
				file.Defrag(randomize: false, validate: false);
				memoryStream = new MemoryStream();
				file.SaveToStream(memoryStream);
				ByteProvider.DeleteBytes(0L, ByteProvider.Length);
				ByteProvider.InsertBytes(0L, memoryStream.GetBuffer());
			}
			else
			{
				MessageBox.Show("File does not appear to be an OLESS file", "Error", MessageBoxButtons.OK, MessageBoxIcon.Hand);
			}
		}

		private void reallocateToolStripMenuItem_Click(object sender, EventArgs e)
		{
			OLESS.File file = new OLESS.File();
			MemoryStream memoryStream = new MemoryStream(ByteProvider.Bytes.GetBytes());
			if (file.IsOLESSFile(memoryStream))
			{
				memoryStream.Position = 0L;
				file.LoadFromStream(memoryStream);
				file.Reallocate(randomize: false, pad: false);
				file.Defrag(randomize: false, validate: false);
				memoryStream = new MemoryStream();
				file.SaveToStream(memoryStream);
				ByteProvider.DeleteBytes(0L, ByteProvider.Length);
				ByteProvider.InsertBytes(0L, memoryStream.GetBuffer());
			}
			else
			{
				MessageBox.Show("File does not appear to be an OLESS file", "Error", MessageBoxButtons.OK, MessageBoxIcon.Hand);
			}
		}

		private void DataTL_KeyDown(object sender, KeyEventArgs e)
		{
			if (e.KeyCode == Keys.Right)
			{
				if (DataTL.FocusedNode != null)
				{
					DataTL.FocusedNode.Expanded = true;
				}
			}
			else if (e.KeyCode == Keys.Left && DataTL.FocusedNode != null)
			{
				DataTL.FocusedNode.Expanded = false;
			}
		}

		private void DataTL_DragEnter(object sender, DragEventArgs e)
		{
			e.Effect = DragDropEffects.None;
		}

		private void DataTL_DragOver(object sender, DragEventArgs e)
		{
			e.Effect = DragDropEffects.None;
		}

		private void optionsToolStripMenuItem1_Click(object sender, EventArgs e)
		{
			OptionsDialog optionsDialog = new OptionsDialog();
			optionsDialog.AddDirOption("Parsing", "Parser Folder", _ParserPath);
			optionsDialog.AddBoolOption("Misc", "Reopen Last File On Start", Utils.GetBoolOption("Reopen Last File On Start", defaultValue: false));
			if (optionsDialog.ShowDialog() == DialogResult.OK)
			{
				Utils.SetBoolOption("Reopen Last File On Start", optionsDialog.GetBoolOption("Reopen Last File On Start"));
				string stringOption = optionsDialog.GetStringOption("Parser Folder");
				if (!_ParserPath.Equals(stringOption))
				{
					_ParserPath = stringOption;
					Utils.SetStringOption("Parser Path", _ParserPath);
					ScanForParsers();
					UpdateGUIBasedOnState();
				}
			}
		}

		private void toolTipController1_GetActiveObjectInfo(object sender, ToolTipControllerGetActiveObjectInfoEventArgs e)
		{
			if (!(e.SelectedControl is TreeList))
			{
				return;
			}
			TreeList treeList = (TreeList)e.SelectedControl;
			TreeListHitInfo treeListHitInfo = treeList.CalcHitInfo(e.ControlMousePosition);
			if (treeListHitInfo.HitInfoType != HitInfoType.Cell)
			{
				return;
			}
			object obj = new TreeListCellToolTipInfo(treeListHitInfo.Node, treeListHitInfo.Column, null);
			object obj2 = treeListHitInfo.Node[treeListHitInfo.Column];
			if (obj2 == null)
			{
				return;
			}
			string text = obj2.ToString();
			Regex regex = new Regex("^-?[0-9]+$");
			if (!regex.IsMatch(text) || !(text != "0"))
			{
				return;
			}
			try
			{
				if (!Option_DisplayValuesInHex.Checked)
				{
					text = $"0x{obj2:X}";
				}
			}
			catch (Exception)
			{
			}
			e.Info = new ToolTipControlInfo(obj, text);
		}

		protected override void Dispose(bool disposing)
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
			this.components = new System.ComponentModel.Container();
			System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(GUT.Tools.FormatBrowser.Browser));
			this.VerticalSplitContainer = new System.Windows.Forms.SplitContainer();
			this.groupBox1 = new System.Windows.Forms.GroupBox();
			this.HexBox = new Be.Windows.Forms.HexBox();
			this.ParseImagePanel = new System.Windows.Forms.Panel();
			this.FileScanningGroupBox = new System.Windows.Forms.GroupBox();
			this.DataTL = new DevExpress.XtraTreeList.TreeList();
			this.DataTLNameColumn = new DevExpress.XtraTreeList.Columns.TreeListColumn();
			this.DataTLValueColumn = new DevExpress.XtraTreeList.Columns.TreeListColumn();
			this.DataTLOffsetColumn = new DevExpress.XtraTreeList.Columns.TreeListColumn();
			this.DataTLSizeColumn = new DevExpress.XtraTreeList.Columns.TreeListColumn();
			this.DataTLTypeColumn = new DevExpress.XtraTreeList.Columns.TreeListColumn();
			this.ObjectTreeContextMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
			this.expandNodeToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.collapseNodeToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
			this.ExpandAllNodesInObjectTree = new System.Windows.Forms.ToolStripMenuItem();
			this.CollapseAllNodesInObjectTree = new System.Windows.Forms.ToolStripMenuItem();
			this.toolStripSeparator4 = new System.Windows.Forms.ToolStripSeparator();
			this.toolStripMenuItem5 = new System.Windows.Forms.ToolStripMenuItem();
			this.asdfToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.matchingTypesToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.matchingValuesToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.clearToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
			this.HorizontalSplitter = new System.Windows.Forms.Splitter();
			this.BottomTC = new System.Windows.Forms.TabControl();
			this.ParsingNotesTP = new System.Windows.Forms.TabPage();
			this.ParsingNotesTL = new DevExpress.XtraTreeList.TreeList();
			this.treeListColumn1 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
			this.ParsingNotesOffsetColumn = new DevExpress.XtraTreeList.Columns.TreeListColumn();
			this.ParsingNotesLengthColumn = new DevExpress.XtraTreeList.Columns.TreeListColumn();
			this.treeListColumn4 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
			this.treeListColumn5 = new DevExpress.XtraTreeList.Columns.TreeListColumn();
			this.BrowserMainMenuStrip = new System.Windows.Forms.MenuStrip();
			this.fileToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.OpenDataFile = new System.Windows.Forms.ToolStripMenuItem();
			this.SaveDataFile = new System.Windows.Forms.ToolStripMenuItem();
			this.SaveDataFileAs = new System.Windows.Forms.ToolStripMenuItem();
			this.toolStripSeparator3 = new System.Windows.Forms.ToolStripSeparator();
			this.ExportToXML = new System.Windows.Forms.ToolStripMenuItem();
			this.parseDataToXMLToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripSeparator();
			this.Exit = new System.Windows.Forms.ToolStripMenuItem();
			this.editToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.Find = new System.Windows.Forms.ToolStripMenuItem();
			this.FindNext = new System.Windows.Forms.ToolStripMenuItem();
			this.FindPrevious = new System.Windows.Forms.ToolStripMenuItem();
			this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
			this.gotoOffsetToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.optionsToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.Option_DisplayValuesInHex = new System.Windows.Forms.ToolStripMenuItem();
			this.Option_TryToDisplayNumbersAsStrings = new System.Windows.Forms.ToolStripMenuItem();
			this.toolsToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.defragToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.reallocateToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.toolStripMenuItem3 = new System.Windows.Forms.ToolStripSeparator();
			this.optionsToolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
			this.helpToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.Help_About = new System.Windows.Forms.ToolStripMenuItem();
			this.debugToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.addParsingNotesToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
			this.StatusBar = new System.Windows.Forms.StatusStrip();
			this.TemporaryStatusMessage = new System.Windows.Forms.ToolStripStatusLabel();
			this.CurrentPosition = new System.Windows.Forms.ToolStripStatusLabel();
			this.SelectionLength = new System.Windows.Forms.ToolStripStatusLabel();
			this.ParseTime = new System.Windows.Forms.ToolStripStatusLabel();
			this.DetectionTime = new System.Windows.Forms.ToolStripStatusLabel();
			this.DetectionLoadedForLabel = new System.Windows.Forms.ToolStripStatusLabel();
			this.OpenDataFileDialog = new System.Windows.Forms.OpenFileDialog();
			this.ParserFolderBrowser = new System.Windows.Forms.FolderBrowserDialog();
			this.StatusBarClearingTimer = new System.Windows.Forms.Timer(this.components);
			this.SaveDataFileDialog = new System.Windows.Forms.SaveFileDialog();
			this.defaultToolTipController1 = new DevExpress.Utils.DefaultToolTipController(this.components);
			this.panel1 = new System.Windows.Forms.Panel();
			this.ParseButton = new System.Windows.Forms.Button();
			this.label1 = new System.Windows.Forms.Label();
			this.ParserList = new System.Windows.Forms.ComboBox();
			this.ExportFileDialog = new System.Windows.Forms.SaveFileDialog();
			this.HexToolTipController = new DevExpress.Utils.ToolTipController(this.components);
			this.VerticalSplitContainer.Panel1.SuspendLayout();
			this.VerticalSplitContainer.Panel2.SuspendLayout();
			this.VerticalSplitContainer.SuspendLayout();
			this.groupBox1.SuspendLayout();
			this.FileScanningGroupBox.SuspendLayout();
			((System.ComponentModel.ISupportInitialize)this.DataTL).BeginInit();
			this.ObjectTreeContextMenu.SuspendLayout();
			((System.ComponentModel.ISupportInitialize)this.repositoryItemTextEdit1).BeginInit();
			this.BottomTC.SuspendLayout();
			this.ParsingNotesTP.SuspendLayout();
			((System.ComponentModel.ISupportInitialize)this.ParsingNotesTL).BeginInit();
			this.BrowserMainMenuStrip.SuspendLayout();
			this.StatusBar.SuspendLayout();
			this.panel1.SuspendLayout();
			base.SuspendLayout();
			this.defaultToolTipController1.SetAllowHtmlText(this.VerticalSplitContainer, DevExpress.Utils.DefaultBoolean.Default);
			this.VerticalSplitContainer.Dock = System.Windows.Forms.DockStyle.Fill;
			this.VerticalSplitContainer.FixedPanel = System.Windows.Forms.FixedPanel.Panel1;
			this.VerticalSplitContainer.Location = new System.Drawing.Point(0, 58);
			this.VerticalSplitContainer.Name = "VerticalSplitContainer";
			this.defaultToolTipController1.SetAllowHtmlText(this.VerticalSplitContainer.Panel1, DevExpress.Utils.DefaultBoolean.Default);
			this.VerticalSplitContainer.Panel1.Controls.Add(this.groupBox1);
			this.defaultToolTipController1.SetAllowHtmlText(this.VerticalSplitContainer.Panel2, DevExpress.Utils.DefaultBoolean.Default);
			this.VerticalSplitContainer.Panel2.Controls.Add(this.FileScanningGroupBox);
			this.VerticalSplitContainer.Size = new System.Drawing.Size(926, 493);
			this.VerticalSplitContainer.SplitterDistance = 429;
			this.VerticalSplitContainer.TabIndex = 4;
			this.VerticalSplitContainer.SplitterMoved += new System.Windows.Forms.SplitterEventHandler(VerticalSplitContainer_SplitterMoved);
			this.defaultToolTipController1.SetAllowHtmlText(this.groupBox1, DevExpress.Utils.DefaultBoolean.Default);
			this.groupBox1.Controls.Add(this.HexBox);
			this.groupBox1.Controls.Add(this.ParseImagePanel);
			this.groupBox1.Dock = System.Windows.Forms.DockStyle.Fill;
			this.groupBox1.Location = new System.Drawing.Point(0, 0);
			this.groupBox1.Name = "groupBox1";
			this.groupBox1.Size = new System.Drawing.Size(429, 493);
			this.groupBox1.TabIndex = 3;
			this.groupBox1.TabStop = false;
			this.groupBox1.Text = "Raw File Contents";
			this.defaultToolTipController1.SetAllowHtmlText(this.HexBox, DevExpress.Utils.DefaultBoolean.Default);
			this.HexBox.BorderStyle = System.Windows.Forms.BorderStyle.None;
			this.HexBox.Cursor = System.Windows.Forms.Cursors.Default;
			this.HexBox.Dock = System.Windows.Forms.DockStyle.Fill;
			this.HexBox.Font = new System.Drawing.Font("Courier New", 9f, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, 0);
			this.HexBox.ForeColor = System.Drawing.Color.Black;
			this.HexBox.LineInfoForeColor = System.Drawing.Color.Empty;
			this.HexBox.LineInfoVisible = true;
			this.HexBox.Location = new System.Drawing.Point(19, 16);
			this.HexBox.Name = "HexBox";
			this.HexBox.ShadowSelectionColor = System.Drawing.Color.FromArgb(100, 60, 188, 255);
			this.HexBox.Size = new System.Drawing.Size(407, 474);
			this.HexBox.StringViewVisible = true;
			this.HexBox.TabIndex = 1;
			this.HexBox.UseFixedBytesPerLine = true;
			this.HexBox.VScrollBarVisible = true;
			this.HexBox.SelectionStartChanged += new System.EventHandler(HexBox_SelectionStartChanged);
			this.HexBox.DoubleClick += new System.EventHandler(HexBox_DoubleClick);
			this.HexBox.VisibleBytesChanged += new System.EventHandler(HexBox_VisibleBytesChanged);
			this.HexBox.SelectionLengthChanged += new System.EventHandler(HexBox_SelectionLengthChanged);
			this.defaultToolTipController1.SetAllowHtmlText(this.ParseImagePanel, DevExpress.Utils.DefaultBoolean.Default);
			this.ParseImagePanel.Dock = System.Windows.Forms.DockStyle.Left;
			this.ParseImagePanel.Location = new System.Drawing.Point(3, 16);
			this.ParseImagePanel.Name = "ParseImagePanel";
			this.ParseImagePanel.Size = new System.Drawing.Size(16, 474);
			this.ParseImagePanel.TabIndex = 2;
			this.ParseImagePanel.Paint += new System.Windows.Forms.PaintEventHandler(ParseImagePanel_Paint);
			this.ParseImagePanel.MouseMove += new System.Windows.Forms.MouseEventHandler(ParseImagePanel_MouseMove);
			this.ParseImagePanel.MouseDown += new System.Windows.Forms.MouseEventHandler(ParseImagePanel_MouseDown);
			this.defaultToolTipController1.SetAllowHtmlText(this.FileScanningGroupBox, DevExpress.Utils.DefaultBoolean.Default);
			this.FileScanningGroupBox.Controls.Add(this.DataTL);
			this.FileScanningGroupBox.Controls.Add(this.HorizontalSplitter);
			this.FileScanningGroupBox.Controls.Add(this.BottomTC);
			this.FileScanningGroupBox.Dock = System.Windows.Forms.DockStyle.Fill;
			this.FileScanningGroupBox.Location = new System.Drawing.Point(0, 0);
			this.FileScanningGroupBox.Name = "FileScanningGroupBox";
			this.FileScanningGroupBox.Size = new System.Drawing.Size(493, 493);
			this.FileScanningGroupBox.TabIndex = 5;
			this.FileScanningGroupBox.TabStop = false;
			this.FileScanningGroupBox.Text = "Parsing Results";
			this.DataTL.Appearance.EvenRow.BackColor = System.Drawing.Color.White;
			this.DataTL.Appearance.EvenRow.BackColor2 = System.Drawing.Color.White;
			this.DataTL.Appearance.EvenRow.Options.UseBackColor = true;
			this.DataTL.Appearance.FocusedCell.BackColor = System.Drawing.Color.White;
			this.DataTL.Appearance.FocusedCell.ForeColor = System.Drawing.Color.Black;
			this.DataTL.Appearance.FocusedCell.Options.UseBackColor = true;
			this.DataTL.Appearance.FocusedCell.Options.UseForeColor = true;
			this.DataTL.Appearance.FocusedRow.BackColor = System.Drawing.Color.RoyalBlue;
			this.DataTL.Appearance.FocusedRow.Options.UseBackColor = true;
			this.DataTL.Appearance.HideSelectionRow.BackColor = System.Drawing.Color.LightSteelBlue;
			this.DataTL.Appearance.HideSelectionRow.Options.UseBackColor = true;
			this.DataTL.Appearance.OddRow.BackColor = System.Drawing.Color.WhiteSmoke;
			this.DataTL.Appearance.OddRow.BackColor2 = System.Drawing.Color.WhiteSmoke;
			this.DataTL.Appearance.OddRow.Options.UseBackColor = true;
			this.DataTL.Appearance.SelectedRow.BackColor = System.Drawing.Color.RoyalBlue;
			this.DataTL.Appearance.SelectedRow.Options.UseBackColor = true;
			this.DataTL.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
			this.DataTL.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[5] { this.DataTLNameColumn, this.DataTLValueColumn, this.DataTLOffsetColumn, this.DataTLSizeColumn, this.DataTLTypeColumn });
			this.DataTL.ContextMenuStrip = this.ObjectTreeContextMenu;
			this.DataTL.Dock = System.Windows.Forms.DockStyle.Fill;
			this.DataTL.Location = new System.Drawing.Point(3, 16);
			this.DataTL.Name = "DataTL";
			this.DataTL.OptionsBehavior.AllowExpandOnDblClick = false;
			this.DataTL.OptionsBehavior.AllowIncrementalSearch = true;
			this.DataTL.OptionsBehavior.DragNodes = true;
			this.DataTL.OptionsBehavior.EnableFiltering = true;
			this.DataTL.OptionsBehavior.ExpandNodesOnIncrementalSearch = true;
			this.DataTL.OptionsBehavior.ImmediateEditor = false;
			this.DataTL.OptionsSelection.EnableAppearanceFocusedCell = false;
			this.DataTL.OptionsView.EnableAppearanceEvenRow = true;
			this.DataTL.OptionsView.EnableAppearanceOddRow = true;
			this.DataTL.OptionsView.ShowFocusedFrame = false;
			this.DataTL.OptionsView.ShowIndentAsRowStyle = true;
			this.DataTL.OptionsView.ShowIndicator = false;
			this.DataTL.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[1] { this.repositoryItemTextEdit1 });
			this.DataTL.Size = new System.Drawing.Size(487, 323);
			this.DataTL.TabIndex = 4;
			this.DataTL.ToolTipController = this.HexToolTipController;
			this.DataTL.MouseUp += new System.Windows.Forms.MouseEventHandler(DataTL_MouseUp);
			this.DataTL.KeyDown += new System.Windows.Forms.KeyEventHandler(DataTL_KeyDown);
			this.DataTL.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(DataTL_MouseDoubleClick);
			this.DataTL.DragOver += new System.Windows.Forms.DragEventHandler(DataTL_DragOver);
			this.DataTL.GetNodeDisplayValue += new DevExpress.XtraTreeList.GetNodeDisplayValueEventHandler(DataTL_GetNodeDisplayValue);
			this.DataTL.CellValueChanged += new DevExpress.XtraTreeList.CellValueChangedEventHandler(DataTL_CellValueChanged);
			this.DataTL.ShowingEditor += new System.ComponentModel.CancelEventHandler(DataTL_ShowingEditor);
			this.DataTL.DragEnter += new System.Windows.Forms.DragEventHandler(DataTL_DragEnter);
			this.DataTLNameColumn.Caption = "Name";
			this.DataTLNameColumn.FieldName = "Name";
			this.DataTLNameColumn.Name = "DataTLNameColumn";
			this.DataTLNameColumn.OptionsColumn.AllowEdit = false;
			this.DataTLNameColumn.OptionsColumn.AllowSort = false;
			this.DataTLNameColumn.Visible = true;
			this.DataTLNameColumn.VisibleIndex = 0;
			this.DataTLNameColumn.Width = 252;
			this.DataTLValueColumn.Caption = "Value";
			this.DataTLValueColumn.FieldName = "Value";
			this.DataTLValueColumn.Name = "DataTLValueColumn";
			this.DataTLValueColumn.OptionsColumn.AllowSort = false;
			this.DataTLValueColumn.Visible = true;
			this.DataTLValueColumn.VisibleIndex = 1;
			this.DataTLValueColumn.Width = 150;
			this.DataTLOffsetColumn.Caption = "Offset";
			this.DataTLOffsetColumn.FieldName = "Offset";
			this.DataTLOffsetColumn.Format.FormatType = DevExpress.Utils.FormatType.Numeric;
			this.DataTLOffsetColumn.Name = "DataTLOffsetColumn";
			this.DataTLOffsetColumn.OptionsColumn.AllowEdit = false;
			this.DataTLOffsetColumn.OptionsColumn.AllowSort = false;
			this.DataTLOffsetColumn.OptionsColumn.FixedWidth = true;
			this.DataTLOffsetColumn.Visible = true;
			this.DataTLOffsetColumn.VisibleIndex = 2;
			this.DataTLOffsetColumn.Width = 80;
			this.DataTLSizeColumn.Caption = "Size";
			this.DataTLSizeColumn.FieldName = "Size";
			this.DataTLSizeColumn.Format.FormatType = DevExpress.Utils.FormatType.Numeric;
			this.DataTLSizeColumn.Name = "DataTLSizeColumn";
			this.DataTLSizeColumn.OptionsColumn.AllowEdit = false;
			this.DataTLSizeColumn.OptionsColumn.AllowSort = false;
			this.DataTLSizeColumn.OptionsColumn.FixedWidth = true;
			this.DataTLSizeColumn.Visible = true;
			this.DataTLSizeColumn.VisibleIndex = 3;
			this.DataTLSizeColumn.Width = 80;
			this.DataTLTypeColumn.Caption = "Type";
			this.DataTLTypeColumn.FieldName = "treeListColumn2";
			this.DataTLTypeColumn.Name = "DataTLTypeColumn";
			this.DataTLTypeColumn.OptionsColumn.AllowEdit = false;
			this.DataTLTypeColumn.OptionsColumn.AllowSort = false;
			this.DataTLTypeColumn.Visible = true;
			this.DataTLTypeColumn.VisibleIndex = 4;
			this.defaultToolTipController1.SetAllowHtmlText(this.ObjectTreeContextMenu, DevExpress.Utils.DefaultBoolean.Default);
			this.ObjectTreeContextMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[7] { this.expandNodeToolStripMenuItem, this.collapseNodeToolStripMenuItem, this.toolStripSeparator1, this.ExpandAllNodesInObjectTree, this.CollapseAllNodesInObjectTree, this.toolStripSeparator4, this.toolStripMenuItem5 });
			this.ObjectTreeContextMenu.Name = "ObjectTreeContextMenu";
			this.ObjectTreeContextMenu.Size = new System.Drawing.Size(154, 126);
			this.expandNodeToolStripMenuItem.Name = "expandNodeToolStripMenuItem";
			this.expandNodeToolStripMenuItem.Size = new System.Drawing.Size(153, 22);
			this.expandNodeToolStripMenuItem.Text = "Expand Node";
			this.expandNodeToolStripMenuItem.Click += new System.EventHandler(expandNodeToolStripMenuItem_Click);
			this.collapseNodeToolStripMenuItem.Name = "collapseNodeToolStripMenuItem";
			this.collapseNodeToolStripMenuItem.Size = new System.Drawing.Size(153, 22);
			this.collapseNodeToolStripMenuItem.Text = "Collapse Node";
			this.collapseNodeToolStripMenuItem.Click += new System.EventHandler(collapseNodeToolStripMenuItem_Click);
			this.toolStripSeparator1.Name = "toolStripSeparator1";
			this.toolStripSeparator1.Size = new System.Drawing.Size(150, 6);
			this.ExpandAllNodesInObjectTree.Name = "ExpandAllNodesInObjectTree";
			this.ExpandAllNodesInObjectTree.Size = new System.Drawing.Size(153, 22);
			this.ExpandAllNodesInObjectTree.Text = "&Expand All";
			this.ExpandAllNodesInObjectTree.Click += new System.EventHandler(ExpandAllNodesInObjectTree_Click);
			this.CollapseAllNodesInObjectTree.Name = "CollapseAllNodesInObjectTree";
			this.CollapseAllNodesInObjectTree.Size = new System.Drawing.Size(153, 22);
			this.CollapseAllNodesInObjectTree.Text = "&Collapse All";
			this.CollapseAllNodesInObjectTree.Click += new System.EventHandler(CollapseAllNodesInObjectTree_Click);
			this.toolStripSeparator4.Name = "toolStripSeparator4";
			this.toolStripSeparator4.Size = new System.Drawing.Size(150, 6);
			this.toolStripMenuItem5.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[4] { this.asdfToolStripMenuItem, this.matchingTypesToolStripMenuItem, this.matchingValuesToolStripMenuItem, this.clearToolStripMenuItem });
			this.toolStripMenuItem5.Name = "toolStripMenuItem5";
			this.toolStripMenuItem5.Size = new System.Drawing.Size(153, 22);
			this.toolStripMenuItem5.Text = "Highlight";
			this.asdfToolStripMenuItem.Name = "asdfToolStripMenuItem";
			this.asdfToolStripMenuItem.Size = new System.Drawing.Size(162, 22);
			this.asdfToolStripMenuItem.Text = "This Structure";
			this.asdfToolStripMenuItem.Click += new System.EventHandler(highlightToolStripMenuItem_Click);
			this.matchingTypesToolStripMenuItem.Name = "matchingTypesToolStripMenuItem";
			this.matchingTypesToolStripMenuItem.Size = new System.Drawing.Size(162, 22);
			this.matchingTypesToolStripMenuItem.Text = "Matching Types";
			this.matchingTypesToolStripMenuItem.Click += new System.EventHandler(highlightTypeToolStripMenuItem_Click);
			this.matchingValuesToolStripMenuItem.Name = "matchingValuesToolStripMenuItem";
			this.matchingValuesToolStripMenuItem.Size = new System.Drawing.Size(162, 22);
			this.matchingValuesToolStripMenuItem.Text = "Matching Values";
			this.matchingValuesToolStripMenuItem.Click += new System.EventHandler(highlightMatchingValuesToolStripMenuItem_Click);
			this.clearToolStripMenuItem.Name = "clearToolStripMenuItem";
			this.clearToolStripMenuItem.Size = new System.Drawing.Size(162, 22);
			this.clearToolStripMenuItem.Text = "Clear";
			this.clearToolStripMenuItem.Click += new System.EventHandler(clearHighlightingToolStripMenuItem_Click);
			this.repositoryItemTextEdit1.AutoHeight = false;
			this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
			this.defaultToolTipController1.SetAllowHtmlText(this.HorizontalSplitter, DevExpress.Utils.DefaultBoolean.Default);
			this.HorizontalSplitter.Cursor = System.Windows.Forms.Cursors.HSplit;
			this.HorizontalSplitter.Dock = System.Windows.Forms.DockStyle.Bottom;
			this.HorizontalSplitter.Location = new System.Drawing.Point(3, 339);
			this.HorizontalSplitter.Name = "HorizontalSplitter";
			this.HorizontalSplitter.Size = new System.Drawing.Size(487, 5);
			this.HorizontalSplitter.TabIndex = 1;
			this.HorizontalSplitter.TabStop = false;
			this.HorizontalSplitter.SplitterMoved += new System.Windows.Forms.SplitterEventHandler(HorizontalSplitter_SplitterMoved);
			this.defaultToolTipController1.SetAllowHtmlText(this.BottomTC, DevExpress.Utils.DefaultBoolean.Default);
			this.BottomTC.Controls.Add(this.ParsingNotesTP);
			this.BottomTC.Dock = System.Windows.Forms.DockStyle.Bottom;
			this.BottomTC.Location = new System.Drawing.Point(3, 344);
			this.BottomTC.Name = "BottomTC";
			this.BottomTC.SelectedIndex = 0;
			this.BottomTC.Size = new System.Drawing.Size(487, 146);
			this.BottomTC.TabIndex = 6;
			this.defaultToolTipController1.SetAllowHtmlText(this.ParsingNotesTP, DevExpress.Utils.DefaultBoolean.Default);
			this.ParsingNotesTP.Controls.Add(this.ParsingNotesTL);
			this.ParsingNotesTP.Location = new System.Drawing.Point(4, 22);
			this.ParsingNotesTP.Name = "ParsingNotesTP";
			this.ParsingNotesTP.Padding = new System.Windows.Forms.Padding(3);
			this.ParsingNotesTP.Size = new System.Drawing.Size(479, 120);
			this.ParsingNotesTP.TabIndex = 0;
			this.ParsingNotesTP.Text = "Parsing Notes";
			this.ParsingNotesTP.UseVisualStyleBackColor = true;
			this.ParsingNotesTL.Appearance.FocusedCell.BackColor = System.Drawing.Color.RoyalBlue;
			this.ParsingNotesTL.Appearance.FocusedCell.ForeColor = System.Drawing.Color.White;
			this.ParsingNotesTL.Appearance.FocusedCell.Options.UseBackColor = true;
			this.ParsingNotesTL.Appearance.FocusedCell.Options.UseForeColor = true;
			this.ParsingNotesTL.Appearance.FocusedRow.BackColor = System.Drawing.Color.RoyalBlue;
			this.ParsingNotesTL.Appearance.FocusedRow.Options.UseBackColor = true;
			this.ParsingNotesTL.Appearance.HideSelectionRow.BackColor = System.Drawing.Color.White;
			this.ParsingNotesTL.Appearance.HideSelectionRow.ForeColor = System.Drawing.Color.Black;
			this.ParsingNotesTL.Appearance.HideSelectionRow.Options.UseBackColor = true;
			this.ParsingNotesTL.Appearance.HideSelectionRow.Options.UseForeColor = true;
			this.ParsingNotesTL.Appearance.SelectedRow.BackColor = System.Drawing.Color.RoyalBlue;
			this.ParsingNotesTL.Appearance.SelectedRow.Options.UseBackColor = true;
			this.ParsingNotesTL.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
			this.ParsingNotesTL.Columns.AddRange(new DevExpress.XtraTreeList.Columns.TreeListColumn[5] { this.treeListColumn1, this.ParsingNotesOffsetColumn, this.ParsingNotesLengthColumn, this.treeListColumn4, this.treeListColumn5 });
			this.ParsingNotesTL.Dock = System.Windows.Forms.DockStyle.Fill;
			this.ParsingNotesTL.Location = new System.Drawing.Point(3, 3);
			this.ParsingNotesTL.Name = "ParsingNotesTL";
			this.ParsingNotesTL.OptionsBehavior.Editable = false;
			this.ParsingNotesTL.OptionsSelection.EnableAppearanceFocusedCell = false;
			this.ParsingNotesTL.OptionsView.ShowFocusedFrame = false;
			this.ParsingNotesTL.OptionsView.ShowIndicator = false;
			this.ParsingNotesTL.OptionsView.ShowRoot = false;
			this.ParsingNotesTL.Size = new System.Drawing.Size(473, 114);
			this.ParsingNotesTL.TabIndex = 5;
			this.ParsingNotesTL.ToolTipController = this.HexToolTipController;
			this.ParsingNotesTL.MouseDoubleClick += new System.Windows.Forms.MouseEventHandler(ParsingNotesTL_MouseDoubleClick);
			this.treeListColumn1.Caption = "Type";
			this.treeListColumn1.FieldName = "Type";
			this.treeListColumn1.Name = "treeListColumn1";
			this.treeListColumn1.OptionsColumn.FixedWidth = true;
			this.treeListColumn1.Visible = true;
			this.treeListColumn1.VisibleIndex = 0;
			this.treeListColumn1.Width = 100;
			this.ParsingNotesOffsetColumn.Caption = "Offset";
			this.ParsingNotesOffsetColumn.FieldName = "Offset";
			this.ParsingNotesOffsetColumn.Format.FormatType = DevExpress.Utils.FormatType.Numeric;
			this.ParsingNotesOffsetColumn.Name = "ParsingNotesOffsetColumn";
			this.ParsingNotesOffsetColumn.OptionsColumn.FixedWidth = true;
			this.ParsingNotesOffsetColumn.Visible = true;
			this.ParsingNotesOffsetColumn.VisibleIndex = 2;
			this.ParsingNotesLengthColumn.Caption = "Length";
			this.ParsingNotesLengthColumn.FieldName = "Length";
			this.ParsingNotesLengthColumn.Format.FormatType = DevExpress.Utils.FormatType.Numeric;
			this.ParsingNotesLengthColumn.Name = "ParsingNotesLengthColumn";
			this.ParsingNotesLengthColumn.OptionsColumn.FixedWidth = true;
			this.ParsingNotesLengthColumn.Visible = true;
			this.ParsingNotesLengthColumn.VisibleIndex = 3;
			this.treeListColumn4.Caption = "Vuln ID";
			this.treeListColumn4.FieldName = "Vuln ID";
			this.treeListColumn4.Name = "treeListColumn4";
			this.treeListColumn4.OptionsColumn.FixedWidth = true;
			this.treeListColumn4.Visible = true;
			this.treeListColumn4.VisibleIndex = 4;
			this.treeListColumn4.Width = 100;
			this.treeListColumn5.Caption = "Notes";
			this.treeListColumn5.FieldName = "Notes";
			this.treeListColumn5.Name = "treeListColumn5";
			this.treeListColumn5.Visible = true;
			this.treeListColumn5.VisibleIndex = 1;
			this.defaultToolTipController1.SetAllowHtmlText(this.BrowserMainMenuStrip, DevExpress.Utils.DefaultBoolean.Default);
			this.BrowserMainMenuStrip.Items.AddRange(new System.Windows.Forms.ToolStripItem[6] { this.fileToolStripMenuItem, this.editToolStripMenuItem, this.optionsToolStripMenuItem, this.toolsToolStripMenuItem, this.helpToolStripMenuItem, this.debugToolStripMenuItem });
			this.BrowserMainMenuStrip.Location = new System.Drawing.Point(0, 0);
			this.BrowserMainMenuStrip.Name = "BrowserMainMenuStrip";
			this.BrowserMainMenuStrip.Size = new System.Drawing.Size(926, 24);
			this.BrowserMainMenuStrip.TabIndex = 5;
			this.BrowserMainMenuStrip.Text = "menuStrip1";
			this.fileToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[7] { this.OpenDataFile, this.SaveDataFile, this.SaveDataFileAs, this.toolStripSeparator3, this.ExportToXML, this.toolStripMenuItem2, this.Exit });
			this.fileToolStripMenuItem.Name = "fileToolStripMenuItem";
			this.fileToolStripMenuItem.Size = new System.Drawing.Size(35, 20);
			this.fileToolStripMenuItem.Text = "&File";
			this.OpenDataFile.Name = "OpenDataFile";
			this.OpenDataFile.Size = new System.Drawing.Size(181, 22);
			this.OpenDataFile.Text = "&Open Data File...";
			this.OpenDataFile.Click += new System.EventHandler(OpenDataFile_Click);
			this.SaveDataFile.Enabled = false;
			this.SaveDataFile.Name = "SaveDataFile";
			this.SaveDataFile.Size = new System.Drawing.Size(181, 22);
			this.SaveDataFile.Text = "&Save Data File";
			this.SaveDataFile.Click += new System.EventHandler(SaveDataFile_Click);
			this.SaveDataFileAs.Enabled = false;
			this.SaveDataFileAs.Name = "SaveDataFileAs";
			this.SaveDataFileAs.Size = new System.Drawing.Size(181, 22);
			this.SaveDataFileAs.Text = "Save Data File &As...";
			this.SaveDataFileAs.Click += new System.EventHandler(SaveDataFileAs_Click);
			this.toolStripSeparator3.Name = "toolStripSeparator3";
			this.toolStripSeparator3.Size = new System.Drawing.Size(178, 6);
			this.ExportToXML.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[1] { this.parseDataToXMLToolStripMenuItem });
			this.ExportToXML.Name = "ExportToXML";
			this.ExportToXML.Size = new System.Drawing.Size(181, 22);
			this.ExportToXML.Text = "Export";
			this.parseDataToXMLToolStripMenuItem.Name = "parseDataToXMLToolStripMenuItem";
			this.parseDataToXMLToolStripMenuItem.Size = new System.Drawing.Size(207, 22);
			this.parseDataToXMLToolStripMenuItem.Text = "Parsing Results To XML...";
			this.parseDataToXMLToolStripMenuItem.Click += new System.EventHandler(parseDataToXMLToolStripMenuItem_Click);
			this.toolStripMenuItem2.Name = "toolStripMenuItem2";
			this.toolStripMenuItem2.Size = new System.Drawing.Size(178, 6);
			this.Exit.Name = "Exit";
			this.Exit.Size = new System.Drawing.Size(181, 22);
			this.Exit.Text = "E&xit";
			this.Exit.Click += new System.EventHandler(Exit_Click);
			this.editToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[5] { this.Find, this.FindNext, this.FindPrevious, this.toolStripSeparator2, this.gotoOffsetToolStripMenuItem });
			this.editToolStripMenuItem.Name = "editToolStripMenuItem";
			this.editToolStripMenuItem.ShortcutKeys = System.Windows.Forms.Keys.G | System.Windows.Forms.Keys.Control;
			this.editToolStripMenuItem.Size = new System.Drawing.Size(37, 20);
			this.editToolStripMenuItem.Text = "&Edit";
			this.Find.Name = "Find";
			this.Find.ShortcutKeyDisplayString = "CTRL-F";
			this.Find.ShortcutKeys = System.Windows.Forms.Keys.F | System.Windows.Forms.Keys.Control;
			this.Find.Size = new System.Drawing.Size(197, 22);
			this.Find.Text = "&Find...";
			this.Find.Click += new System.EventHandler(Find_Click);
			this.FindNext.Name = "FindNext";
			this.FindNext.ShortcutKeyDisplayString = "F3";
			this.FindNext.ShortcutKeys = System.Windows.Forms.Keys.F3;
			this.FindNext.Size = new System.Drawing.Size(197, 22);
			this.FindNext.Text = "Find &Next";
			this.FindNext.Click += new System.EventHandler(FindNext_Click);
			this.FindPrevious.Name = "FindPrevious";
			this.FindPrevious.ShortcutKeyDisplayString = "CTRL-F3";
			this.FindPrevious.ShortcutKeys = System.Windows.Forms.Keys.F3 | System.Windows.Forms.Keys.Control;
			this.FindPrevious.Size = new System.Drawing.Size(197, 22);
			this.FindPrevious.Text = "Find &Previous";
			this.FindPrevious.Click += new System.EventHandler(FindPrevious_Click);
			this.toolStripSeparator2.Name = "toolStripSeparator2";
			this.toolStripSeparator2.Size = new System.Drawing.Size(194, 6);
			this.gotoOffsetToolStripMenuItem.Name = "gotoOffsetToolStripMenuItem";
			this.gotoOffsetToolStripMenuItem.ShortcutKeys = System.Windows.Forms.Keys.G | System.Windows.Forms.Keys.Control;
			this.gotoOffsetToolStripMenuItem.Size = new System.Drawing.Size(197, 22);
			this.gotoOffsetToolStripMenuItem.Text = "&Goto Offset...";
			this.gotoOffsetToolStripMenuItem.Click += new System.EventHandler(gotoOffsetToolStripMenuItem_Click);
			this.optionsToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[2] { this.Option_DisplayValuesInHex, this.Option_TryToDisplayNumbersAsStrings });
			this.optionsToolStripMenuItem.Name = "optionsToolStripMenuItem";
			this.optionsToolStripMenuItem.Size = new System.Drawing.Size(41, 20);
			this.optionsToolStripMenuItem.Text = "&View";
			this.Option_DisplayValuesInHex.Name = "Option_DisplayValuesInHex";
			this.Option_DisplayValuesInHex.Size = new System.Drawing.Size(249, 22);
			this.Option_DisplayValuesInHex.Text = "Display Values In &Hex";
			this.Option_DisplayValuesInHex.CheckedChanged += new System.EventHandler(Option_DisplayValuesInHex_CheckedChanged);
			this.Option_DisplayValuesInHex.Click += new System.EventHandler(Option_DisplayValuesInHex_Click);
			this.Option_TryToDisplayNumbersAsStrings.Name = "Option_TryToDisplayNumbersAsStrings";
			this.Option_TryToDisplayNumbersAsStrings.Size = new System.Drawing.Size(249, 22);
			this.Option_TryToDisplayNumbersAsStrings.Text = "Try To Display Numbers As &Strings";
			this.Option_TryToDisplayNumbersAsStrings.Click += new System.EventHandler(Option_TryToDisplayNumbersAsStrings_Click);
			this.toolsToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[4] { this.defragToolStripMenuItem, this.reallocateToolStripMenuItem, this.toolStripMenuItem3, this.optionsToolStripMenuItem1 });
			this.toolsToolStripMenuItem.Name = "toolsToolStripMenuItem";
			this.toolsToolStripMenuItem.Size = new System.Drawing.Size(44, 20);
			this.toolsToolStripMenuItem.Text = "&Tools";
			this.defragToolStripMenuItem.Name = "defragToolStripMenuItem";
			this.defragToolStripMenuItem.Size = new System.Drawing.Size(197, 22);
			this.defragToolStripMenuItem.Text = "&Defragment";
			this.defragToolStripMenuItem.ToolTipText = "Defragment the OLESS file so it can be parsed correctly";
			this.defragToolStripMenuItem.Click += new System.EventHandler(defragToolStripMenuItem_Click);
			this.reallocateToolStripMenuItem.Name = "reallocateToolStripMenuItem";
			this.reallocateToolStripMenuItem.Size = new System.Drawing.Size(197, 22);
			this.reallocateToolStripMenuItem.Text = "&Repair and Defragment";
			this.reallocateToolStripMenuItem.ToolTipText = "Attempt to repair corrupt files and defragment";
			this.reallocateToolStripMenuItem.Click += new System.EventHandler(reallocateToolStripMenuItem_Click);
			this.toolStripMenuItem3.Name = "toolStripMenuItem3";
			this.toolStripMenuItem3.Size = new System.Drawing.Size(194, 6);
			this.optionsToolStripMenuItem1.Name = "optionsToolStripMenuItem1";
			this.optionsToolStripMenuItem1.Size = new System.Drawing.Size(197, 22);
			this.optionsToolStripMenuItem1.Text = "&Options...";
			this.optionsToolStripMenuItem1.Click += new System.EventHandler(optionsToolStripMenuItem1_Click);
			this.helpToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[1] { this.Help_About });
			this.helpToolStripMenuItem.Name = "helpToolStripMenuItem";
			this.helpToolStripMenuItem.Size = new System.Drawing.Size(40, 20);
			this.helpToolStripMenuItem.Text = "&Help";
			this.Help_About.Name = "Help_About";
			this.Help_About.Size = new System.Drawing.Size(126, 22);
			this.Help_About.Text = "&About...";
			this.Help_About.Click += new System.EventHandler(Help_About_Click);
			this.debugToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[1] { this.addParsingNotesToolStripMenuItem });
			this.debugToolStripMenuItem.Name = "debugToolStripMenuItem";
			this.debugToolStripMenuItem.Size = new System.Drawing.Size(50, 20);
			this.debugToolStripMenuItem.Text = "Debug";
			this.addParsingNotesToolStripMenuItem.Name = "addParsingNotesToolStripMenuItem";
			this.addParsingNotesToolStripMenuItem.Size = new System.Drawing.Size(173, 22);
			this.addParsingNotesToolStripMenuItem.Text = "Add Parsing Notes";
			this.addParsingNotesToolStripMenuItem.Click += new System.EventHandler(addParsingNotesToolStripMenuItem_Click);
			this.defaultToolTipController1.SetAllowHtmlText(this.StatusBar, DevExpress.Utils.DefaultBoolean.Default);
			this.StatusBar.Items.AddRange(new System.Windows.Forms.ToolStripItem[6] { this.TemporaryStatusMessage, this.CurrentPosition, this.SelectionLength, this.ParseTime, this.DetectionTime, this.DetectionLoadedForLabel });
			this.StatusBar.Location = new System.Drawing.Point(0, 551);
			this.StatusBar.Name = "StatusBar";
			this.StatusBar.ShowItemToolTips = true;
			this.StatusBar.Size = new System.Drawing.Size(926, 22);
			this.StatusBar.TabIndex = 6;
			this.StatusBar.Text = "statusStrip1";
			this.TemporaryStatusMessage.AutoSize = false;
			this.TemporaryStatusMessage.Name = "TemporaryStatusMessage";
			this.TemporaryStatusMessage.Size = new System.Drawing.Size(500, 17);
			this.TemporaryStatusMessage.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			this.CurrentPosition.AutoSize = false;
			this.CurrentPosition.BorderSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.Left;
			this.CurrentPosition.Name = "CurrentPosition";
			this.CurrentPosition.Size = new System.Drawing.Size(120, 17);
			this.CurrentPosition.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			this.SelectionLength.Name = "SelectionLength";
			this.SelectionLength.Size = new System.Drawing.Size(0, 17);
			this.ParseTime.AutoSize = false;
			this.ParseTime.AutoToolTip = true;
			this.ParseTime.BorderSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.Left | System.Windows.Forms.ToolStripStatusLabelBorderSides.Right;
			this.ParseTime.Name = "ParseTime";
			this.ParseTime.Size = new System.Drawing.Size(100, 17);
			this.ParseTime.ToolTipText = "Parsing Time";
			this.DetectionTime.AutoSize = false;
			this.DetectionTime.AutoToolTip = true;
			this.DetectionTime.BorderSides = System.Windows.Forms.ToolStripStatusLabelBorderSides.Right;
			this.DetectionTime.Name = "DetectionTime";
			this.DetectionTime.Size = new System.Drawing.Size(100, 17);
			this.DetectionTime.ToolTipText = "Detection Time";
			this.DetectionLoadedForLabel.AutoSize = false;
			this.DetectionLoadedForLabel.Name = "DetectionLoadedForLabel";
			this.DetectionLoadedForLabel.Overflow = System.Windows.Forms.ToolStripItemOverflow.Never;
			this.DetectionLoadedForLabel.Size = new System.Drawing.Size(487, 17);
			this.DetectionLoadedForLabel.Spring = true;
			this.DetectionLoadedForLabel.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			this.OpenDataFileDialog.Filter = "All Files (*.*)|*.*";
			this.OpenDataFileDialog.InitialDirectory = "X:\\Enlistments\\RedTeam\\src\\tools\\Defense\\Format Specification System\\010 Comparison";
			this.OpenDataFileDialog.Title = "Select A Data File";
			this.ParserFolderBrowser.Description = "Select The Folder Containing The Parsers";
			this.StatusBarClearingTimer.Interval = 5000;
			this.StatusBarClearingTimer.Tick += new System.EventHandler(StatusBarClearingTimer_Tick);
			this.SaveDataFileDialog.AddExtension = false;
			this.defaultToolTipController1.DefaultController.AutoPopDelay = 60000;
			this.defaultToolTipController1.DefaultController.Rounded = true;
			this.defaultToolTipController1.DefaultController.ShowBeak = true;
			this.defaultToolTipController1.DefaultController.ToolTipType = DevExpress.Utils.ToolTipType.SuperTip;
			this.defaultToolTipController1.SetAllowHtmlText(this.panel1, DevExpress.Utils.DefaultBoolean.Default);
			this.panel1.Controls.Add(this.ParseButton);
			this.panel1.Controls.Add(this.label1);
			this.panel1.Controls.Add(this.ParserList);
			this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
			this.panel1.Location = new System.Drawing.Point(0, 24);
			this.panel1.Name = "panel1";
			this.panel1.Size = new System.Drawing.Size(926, 34);
			this.panel1.TabIndex = 7;
			this.defaultToolTipController1.SetAllowHtmlText(this.ParseButton, DevExpress.Utils.DefaultBoolean.Default);
			this.ParseButton.Enabled = false;
			this.ParseButton.Location = new System.Drawing.Point(460, 3);
			this.ParseButton.Name = "ParseButton";
			this.ParseButton.Size = new System.Drawing.Size(75, 23);
			this.ParseButton.TabIndex = 2;
			this.ParseButton.Text = "Parse";
			this.ParseButton.UseVisualStyleBackColor = true;
			this.ParseButton.Click += new System.EventHandler(ParseButton_Click);
			this.defaultToolTipController1.SetAllowHtmlText(this.label1, DevExpress.Utils.DefaultBoolean.Default);
			this.label1.AutoSize = true;
			this.label1.Location = new System.Drawing.Point(2, 7);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(40, 13);
			this.label1.TabIndex = 1;
			this.label1.Text = "Parser:";
			this.defaultToolTipController1.SetAllowHtmlText(this.ParserList, DevExpress.Utils.DefaultBoolean.Default);
			this.ParserList.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
			this.ParserList.DropDownWidth = 600;
			this.ParserList.FormattingEnabled = true;
			this.ParserList.Location = new System.Drawing.Point(49, 4);
			this.ParserList.Name = "ParserList";
			this.ParserList.Size = new System.Drawing.Size(405, 21);
			this.ParserList.Sorted = true;
			this.ParserList.TabIndex = 0;
			this.ParserList.SelectedIndexChanged += new System.EventHandler(ParserList_SelectedIndexChanged);
			this.ExportFileDialog.DefaultExt = "xml";
			this.ExportFileDialog.Filter = "XML Files (*.xml)|*.xml";
			this.HexToolTipController.ToolTipType = DevExpress.Utils.ToolTipType.SuperTip;
			this.HexToolTipController.GetActiveObjectInfo += new DevExpress.Utils.ToolTipControllerGetActiveObjectInfoEventHandler(toolTipController1_GetActiveObjectInfo);
			this.defaultToolTipController1.SetAllowHtmlText(this, DevExpress.Utils.DefaultBoolean.Default);
			base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
			base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
			base.ClientSize = new System.Drawing.Size(926, 573);
			base.Controls.Add(this.VerticalSplitContainer);
			base.Controls.Add(this.panel1);
			base.Controls.Add(this.BrowserMainMenuStrip);
			base.Controls.Add(this.StatusBar);
			base.Icon = (System.Drawing.Icon)resources.GetObject("$this.Icon");
			base.Name = "Browser";
			base.StartPosition = System.Windows.Forms.FormStartPosition.Manual;
			this.Text = "ProductName Parser And Detector Test Environment";
			base.Load += new System.EventHandler(Browser_Load);
			base.Shown += new System.EventHandler(Browser_Shown);
			base.FormClosing += new System.Windows.Forms.FormClosingEventHandler(Browser_FormClosing);
			base.ResizeEnd += new System.EventHandler(Browser_ResizeEnd);
			this.VerticalSplitContainer.Panel1.ResumeLayout(false);
			this.VerticalSplitContainer.Panel2.ResumeLayout(false);
			this.VerticalSplitContainer.ResumeLayout(false);
			this.groupBox1.ResumeLayout(false);
			this.FileScanningGroupBox.ResumeLayout(false);
			((System.ComponentModel.ISupportInitialize)this.DataTL).EndInit();
			this.ObjectTreeContextMenu.ResumeLayout(false);
			((System.ComponentModel.ISupportInitialize)this.repositoryItemTextEdit1).EndInit();
			this.BottomTC.ResumeLayout(false);
			this.ParsingNotesTP.ResumeLayout(false);
			((System.ComponentModel.ISupportInitialize)this.ParsingNotesTL).EndInit();
			this.BrowserMainMenuStrip.ResumeLayout(false);
			this.BrowserMainMenuStrip.PerformLayout();
			this.StatusBar.ResumeLayout(false);
			this.StatusBar.PerformLayout();
			this.panel1.ResumeLayout(false);
			this.panel1.PerformLayout();
			base.ResumeLayout(false);
			base.PerformLayout();
		}
	}
	internal class AboutBox : Form
	{
		private IContainer components;

		private Label labelProductName;

		private Label labelVersion;

		private Button OK;

		public AboutBox()
		{
			InitializeComponent();
			labelProductName.Text = Utils.AssemblyProduct;
			labelVersion.Text = $"Version {Utils.AssemblyVersion}";
		}

		private void OK_Click(object sender, EventArgs e)
		{
			Hide();
		}

		protected override void Dispose(bool disposing)
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
			this.labelProductName = new System.Windows.Forms.Label();
			this.labelVersion = new System.Windows.Forms.Label();
			this.OK = new System.Windows.Forms.Button();
			base.SuspendLayout();
			this.labelProductName.AutoSize = true;
			this.labelProductName.Location = new System.Drawing.Point(15, 9);
			this.labelProductName.Margin = new System.Windows.Forms.Padding(6, 0, 3, 0);
			this.labelProductName.MaximumSize = new System.Drawing.Size(0, 17);
			this.labelProductName.Name = "labelProductName";
			this.labelProductName.Size = new System.Drawing.Size(72, 13);
			this.labelProductName.TabIndex = 24;
			this.labelProductName.Text = "ProductName";
			this.labelProductName.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			this.labelVersion.AutoSize = true;
			this.labelVersion.Location = new System.Drawing.Point(15, 26);
			this.labelVersion.Margin = new System.Windows.Forms.Padding(6, 0, 3, 0);
			this.labelVersion.MaximumSize = new System.Drawing.Size(0, 17);
			this.labelVersion.Name = "labelVersion";
			this.labelVersion.Size = new System.Drawing.Size(42, 13);
			this.labelVersion.TabIndex = 23;
			this.labelVersion.Text = "Version";
			this.labelVersion.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
			this.OK.Location = new System.Drawing.Point(76, 92);
			this.OK.Name = "OK";
			this.OK.Size = new System.Drawing.Size(75, 23);
			this.OK.TabIndex = 25;
			this.OK.Text = "OK";
			this.OK.UseVisualStyleBackColor = true;
			this.OK.Click += new System.EventHandler(OK_Click);
			base.AcceptButton = this.OK;
			base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
			base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
			base.ClientSize = new System.Drawing.Size(231, 127);
			base.Controls.Add(this.OK);
			base.Controls.Add(this.labelProductName);
			base.Controls.Add(this.labelVersion);
			base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = "AboutBox";
			base.Padding = new System.Windows.Forms.Padding(9);
			base.ShowIcon = false;
			base.ShowInTaskbar = false;
			base.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
			this.Text = "About";
			base.ResumeLayout(false);
			base.PerformLayout();
		}
	}
}
namespace GUT.Tools.FormatBrowser.Properties
{
	[GeneratedCode("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "10.0.0.0")]
	[CompilerGenerated]
	internal sealed class Settings : ApplicationSettingsBase
	{
		private static Settings defaultInstance = (Settings)SettingsBase.Synchronized(new Settings());

		public static Settings Default => defaultInstance;
	}
	[GeneratedCode("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
	[DebuggerNonUserCode]
	[CompilerGenerated]
	internal class Resources
	{
		private static ResourceManager resourceMan;

		private static CultureInfo resourceCulture;

		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static ResourceManager ResourceManager
		{
			get
			{
				if (object.ReferenceEquals(resourceMan, null))
				{
					ResourceManager resourceManager = new ResourceManager("GUT.Tools.FormatBrowser.Properties.Resources", typeof(Resources).Assembly);
					resourceMan = resourceManager;
				}
				return resourceMan;
			}
		}

		[EditorBrowsable(EditorBrowsableState.Advanced)]
		internal static CultureInfo Culture
		{
			get
			{
				return resourceCulture;
			}
			set
			{
				resourceCulture = value;
			}
		}

		internal Resources()
		{
		}
	}
}
namespace GUT.Tools.FormatBrowser
{
	public class GotoDialog : Form
	{
		private IContainer components;

		private Button Cancel;

		private Button GoButton;

		private TextBox Offset;

		private Label label1;

		public GotoDialog()
		{
			InitializeComponent();
		}

		public ulong GetOffset(ulong defaultValue)
		{
			try
			{
				if (Offset.Text.StartsWith("0x", StringComparison.InvariantCultureIgnoreCase))
				{
					return ulong.Parse(Offset.Text.Substring(2), NumberStyles.HexNumber);
				}
				return ulong.Parse(Offset.Text);
			}
			catch (Exception)
			{
				return defaultValue;
			}
		}

		protected override void Dispose(bool disposing)
		{
			if (disposing && components != null)
			{
				components.Dispose();
			}
			base.Dispose(disposing);
		}

		private void InitializeComponent()
		{
			this.Cancel = new System.Windows.Forms.Button();
			this.GoButton = new System.Windows.Forms.Button();
			this.Offset = new System.Windows.Forms.TextBox();
			this.label1 = new System.Windows.Forms.Label();
			base.SuspendLayout();
			this.Cancel.DialogResult = System.Windows.Forms.DialogResult.Cancel;
			this.Cancel.Location = new System.Drawing.Point(351, 66);
			this.Cancel.Name = "Cancel";
			this.Cancel.Size = new System.Drawing.Size(75, 23);
			this.Cancel.TabIndex = 14;
			this.Cancel.Text = "Cancel";
			this.Cancel.UseVisualStyleBackColor = true;
			this.GoButton.DialogResult = System.Windows.Forms.DialogResult.OK;
			this.GoButton.Location = new System.Drawing.Point(270, 66);
			this.GoButton.Name = "GoButton";
			this.GoButton.Size = new System.Drawing.Size(75, 23);
			this.GoButton.TabIndex = 13;
			this.GoButton.Text = "&Go";
			this.GoButton.UseVisualStyleBackColor = true;
			this.Offset.Location = new System.Drawing.Point(60, 11);
			this.Offset.Name = "Offset";
			this.Offset.Size = new System.Drawing.Size(366, 20);
			this.Offset.TabIndex = 12;
			this.label1.AutoSize = true;
			this.label1.Location = new System.Drawing.Point(16, 14);
			this.label1.Name = "label1";
			this.label1.Size = new System.Drawing.Size(38, 13);
			this.label1.TabIndex = 11;
			this.label1.Text = "Offset:";
			base.AcceptButton = this.GoButton;
			base.AutoScaleDimensions = new System.Drawing.SizeF(6f, 13f);
			base.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
			base.CancelButton = this.Cancel;
			base.ClientSize = new System.Drawing.Size(441, 101);
			base.Controls.Add(this.Cancel);
			base.Controls.Add(this.GoButton);
			base.Controls.Add(this.Offset);
			base.Controls.Add(this.label1);
			base.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
			base.MaximizeBox = false;
			base.MinimizeBox = false;
			base.Name = "GotoDialog";
			base.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
			this.Text = "Goto Offset";
			base.ResumeLayout(false);
			base.PerformLayout();
		}
	}
	public class ParseData
	{
		public string Name;

		public object Value;

		public object Structure;

		public ulong Offset;

		public ulong Length;

		public List<ParseData> Children;

		public int BitFieldShift;

		public int BitFieldSize;

		public bool Expanded;

		public int Depth;

		public Color Color = Color.White;

		public bool HasChildren()
		{
			if (Children != null && Children.Count > 0)
			{
				return true;
			}
			return false;
		}

		public bool IsBitField()
		{
			return BitFieldSize > 0;
		}

		public ulong GetUnshiftedBitFieldMask()
		{
			if (BitFieldSize <= 0)
			{
				return ulong.MaxValue;
			}
			ulong num = 0uL;
			for (int i = 0; i < BitFieldSize; i++)
			{
				num <<= 1;
				num |= 1;
			}
			return num;
		}

		public List<ParseData> GetAllChildren()
		{
			List<ParseData> list = new List<ParseData>();
			if (HasChildren())
			{
				foreach (ParseData child in Children)
				{
					list.Add(child);
					list.AddRange(child.GetAllChildren());
				}
			}
			return list;
		}

		public void SetDepths(int depth)
		{
			Depth = depth;
			if (!HasChildren())
			{
				return;
			}
			foreach (ParseData child in Children)
			{
				child.SetDepths(depth + 1);
			}
		}

		public void AddChild(ParseData child)
		{
			if (Children == null)
			{
				Children = new List<ParseData>();
			}
			Children.Add(child);
		}

		public void CalculateSizeFromChildren()
		{
			ulong num = 0uL;
			ulong num2 = 0uL;
			if (Children != null)
			{
				if (Children.Count > 0)
				{
					num = Children[0].Offset;
					num2 = num + Children[0].Length;
				}
				foreach (ParseData child in Children)
				{
					if (child.Offset < num)
					{
						num = child.Offset;
					}
					if (child.Offset + child.Length > num2)
					{
						num2 = child.Offset + child.Length;
					}
				}
			}
			Offset = num;
			Length = num2 - num;
		}

		public string GetTypeDisplayName()
		{
			if (Structure != null)
			{
				if (Structure.GetType().IsGenericType)
				{
					string text = Structure.GetType().Name;
					int num = text.IndexOf('`');
					if (num >= 0)
					{
						text = text.Substring(0, num);
					}
					string text2 = "";
					Type[] genericArguments = Structure.GetType().GetGenericArguments();
					foreach (Type type in genericArguments)
					{
						if (text2.Length > 0)
						{
							text2 += ",";
						}
						text2 += type.Name;
					}
					return text + "<" + text2 + ">";
				}
				if (BitFieldSize > 0)
				{
					return Structure.GetType().Name + ":" + BitFieldSize;
				}
				return Structure.GetType().Name;
			}
			return "";
		}

		public string GetValueStringOfNumberIfPossible()
		{
			byte[] array = null;
			if (Value is sbyte)
			{
				array = BitConverter.GetBytes((sbyte)Value);
			}
			if (Value is short)
			{
				array = BitConverter.GetBytes((short)Value);
			}
			if (Value is int)
			{
				array = BitConverter.GetBytes((int)Value);
			}
			if (Value is long)
			{
				array = BitConverter.GetBytes((long)Value);
			}
			if (Value is byte)
			{
				array = BitConverter.GetBytes((byte)Value);
			}
			if (Value is ushort)
			{
				array = BitConverter.GetBytes((ushort)Value);
			}
			if (Value is uint)
			{
				array = BitConverter.GetBytes((uint)Value);
			}
			if (Value is ulong)
			{
				array = BitConverter.GetBytes((ulong)Value);
			}
			if (array == null)
			{
				return null;
			}
			string text = "";
			for (int num = array.Length - 1; num >= 0; num--)
			{
				if (array[num] < 32 || array[num] > 127)
				{
					return null;
				}
				text += (char)array[num];
			}
			return text;
		}

		public string GetValueString(bool hex, bool numbersAsStrings)
		{
			if (Value == null)
			{
				return "";
			}
			if (numbersAsStrings)
			{
				string valueStringOfNumberIfPossible = GetValueStringOfNumberIfPossible();
				if (valueStringOfNumberIfPossible != null)
				{
					return valueStringOfNumberIfPossible;
				}
			}
			string text = (hex ? "X2" : "");
			if (Value is string)
			{
				return Value as string;
			}
			if (Value is Guid?)
			{
				return Value.ToString();
			}
			if (Value.GetType().FullName == "System.SByte[]")
			{
				sbyte[] array = (sbyte[])Value;
				string text2 = "";
				for (int i = 0; i < 10 && i < array.Length; i++)
				{
					text2 = text2 + array[i].ToString(text) + " ";
				}
				return text2;
			}
			if (Value.GetType().FullName == "System.Byte[]")
			{
				byte[] array2 = (byte[])Value;
				string text3 = "";
				for (int j = 0; j < 10 && j < array2.Length; j++)
				{
					text3 = text3 + array2[j].ToString(text) + " ";
				}
				return text3;
			}
			if (!hex)
			{
				return Value.ToString();
			}
			if (Value is sbyte)
			{
				return "0x" + ((sbyte)Value).ToString("X");
			}
			if (Value is short)
			{
				return "0x" + ((short)Value).ToString("X");
			}
			if (Value is int)
			{
				return "0x" + ((int)Value).ToString("X");
			}
			if (Value is long)
			{
				return "0x" + ((long)Value).ToString("X");
			}
			if (Value is byte)
			{
				return "0x" + ((byte)Value).ToString("X");
			}
			if (Value is ushort)
			{
				return "0x" + ((ushort)Value).ToString("X");
			}
			if (Value is uint)
			{
				return "0x" + ((uint)Value).ToString("X");
			}
			if (Value is ulong)
			{
				return "0x" + ((ulong)Value).ToString("X");
			}
			return "";
		}
	}
	public class AppData
	{
		public delegate void NotifyEvent(object sender);

		public TreeList DataTL;

		public TabControl BottomTC;

		public ParseData ParsedData;

		public DynamicByteProvider ByteProvider;

		public object Parser;

		public bool FileParsed;

		public bool Closing;

		public event NotifyEvent Initialized;

		public event NotifyEvent StateChanged;

		public event NotifyEvent ConfigurationLoaded;

		public void DoInitialized(object sender)
		{
			if (this.Initialized != null)
			{
				this.Initialized(sender);
			}
		}

		public void DoStateChanged(object sender)
		{
			if (this.StateChanged != null)
			{
				this.StateChanged(sender);
			}
		}

		public void DoConfigurationLoaded(object sender)
		{
			if (this.ConfigurationLoaded != null)
			{
				this.ConfigurationLoaded(sender);
			}
		}
	}
	internal static class Program
	{
		[STAThread]
		private static void Main()
		{
			Application.EnableVisualStyles();
			Application.SetCompatibleTextRenderingDefault(defaultValue: false);
			Application.Run(new Browser());
		}
	}
}
