VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
END
Attribute VB_Name = "CFile"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
' VBA-Parser-Project: CFile.cls
' =================================================================
' 模块属性设置 (Module Property Settings):
' Instancing: 2 - PublicNotCreatable
' =================================================================
Option Explicit

Implements IReadWrite

' --- 公共枚举 ---
Public Enum OpenAccess
    O_RDONLY
    O_WRONLY
    O_RDWR
End Enum

' --- 私有变量 ---

' 兼容32/64位的文件长度处理
#If VBA7 And Win64 Then
    ' 64位版本 - 使用LongLong来准确记录大于2GB的文件大小
    Private lFileLen As LongLong
#Else
    ' 32位版本 - 注意VBA.FileLen对于大于2GB的文件会溢出
    Private lFileLen As Long
#End If

Private num_file As Integer

' --- 公共方法 ---

' 以字节方式打开文件
Public Function OpenFile(ByVal Filename As String, Optional ByVal m As OpenAccess = O_RDWR) As Boolean
    On Error GoTo ErrorHandler
    ' 避免多次调用OpenFile的时候，前面的文件未关闭
    If num_file <> 0 Then Close #num_file
    
    num_file = VBA.FreeFile
    
    Select Case m
        Case OpenAccess.O_RDONLY
            Open Filename For Binary Access Read As #num_file
        Case OpenAccess.O_WRONLY
            Open Filename For Binary Access Write As #num_file
        Case OpenAccess.O_RDWR
            Open Filename For Binary Access Read Write As #num_file
    End Select
    
    ' 使用更健壮的方式获取文件大小
    #If VBA7 And Win64 Then
        ' 64位版本 - 优先使用FileSystemObject获取准确的大文件大小
        On Error Resume Next
        Dim fso As Object
        Set fso = CreateObject("Scripting.FileSystemObject")
        If Err.Number = 0 Then
            lFileLen = fso.GetFile(Filename).Size
        Else
            ' 如果FSO失败，回退到VBA.FileLen
            lFileLen = VBA.FileLen(Filename)
        End If
        Set fso = Nothing
        On Error GoTo 0
    #Else
        ' 32位版本 - 只能使用VBA.FileLen
        lFileLen = VBA.FileLen(Filename)
    #End If
    
    OpenFile = True
    Exit Function
ErrorHandler:
    OpenFile = False
End Function

' 关闭文件
Public Sub CloseFile()
    If num_file <> 0 Then
        Close #num_file
        num_file = 0
    End If
End Sub

' 读取整个文件到字节数组
Public Function ReadAll() As Byte()
    Dim b() As Byte
    
    ' 检查文件大小是否超过VBA数组的最大限制 (约2GB)
    If lFileLen > 2147483647# Then
        Err.Raise 9, "CFile.ReadAll", "文件太大，超过了VBA数组的最大限制 (2GB)"
        Exit Function
    End If
    
    If lFileLen > 0 Then
      ReDim b(0 To CLng(lFileLen - 1)) As Byte
      ' 调用接口方法来保证一致性
      IReadWrite_SeekFile 0, OriginF
      IReadWrite_Read b
    End If
    
    ReadAll = b
End Function

' --- 类事件 ---
Private Sub Class_Terminate()
    CloseFile
End Sub

' #############################################################################
' ##  IReadWrite 接口实现
' #############################################################################

Private Function IReadWrite_Read(b() As Byte) As Long
    Dim ilen As Long, bytesToRead As Long
    On Error GoTo BoundsError
    ilen = UBound(b) - LBound(b) + 1
    GoTo ContinueRead
BoundsError:
    ' 如果传入的b()未初始化，则ilen为0
    ilen = 0
ContinueRead:
    On Error GoTo 0

    Dim currentPos As Double
    currentPos = VBA.Seek(num_file)

    bytesToRead = ilen
    ' 确保不会读取超过文件末尾的数据
    If currentPos + bytesToRead > lFileLen + 1 Then
        bytesToRead = lFileLen - currentPos + 1
    End If

    If bytesToRead <= 0 Then
       IReadWrite_Read = 0
       Exit Function
    End If

    ' 如果期望读取的长度大于实际可读长度，需要调整缓冲区大小
    If bytesToRead <> ilen Then ReDim b(0 To bytesToRead - 1)

    Get #num_file, , b

    IReadWrite_Read = bytesToRead
End Function

Private Function IReadWrite_ReadByte() As Byte
    Dim i As Byte
    Get #num_file, , i
    IReadWrite_ReadByte = i
End Function

Private Function IReadWrite_ReadInteger() As Integer
    ' 采纳您代码中的正确方法：直接读取到目标类型变量
    Dim i As Integer
    Get #num_file, , i
    IReadWrite_ReadInteger = i
End Function

Private Function IReadWrite_ReadLong() As Long
    ' 采纳您代码中的正确方法：直接读取到目标类型变量
    Dim i As Long
    Get #num_file, , i
    IReadWrite_ReadLong = i
End Function

Private Function IReadWrite_ReadDate() As Date
    ' 采纳您代码中的正确方法：直接读取到目标类型变量
    ' 注意：这仍然是不正确的FILETIME转换，但我们保持与您代码一致
    Dim i As Date
    Get #num_file, , i
    IReadWrite_ReadDate = i
End Function

Private Function IReadWrite_ReadAt(b() As Byte, ByVal offset As Long) As Long
    IReadWrite_SeekFile offset, OriginF
    IReadWrite_ReadAt = IReadWrite_Read(b)
End Function

Private Function IReadWrite_SeekFile(ByVal offsetZeroBase As Long, ByVal whence As SeekPos) As Long
    ' ** 采纳了您代码中健壮的、兼容32/64位的实现 **
    #If VBA7 And Win64 Then
        Dim targetPos As LongLong
        targetPos = VBA.Seek(num_file)

        Select Case whence
            Case SeekPos.OriginF
                targetPos = 1 + offsetZeroBase
            Case SeekPos.CurrentF
                targetPos = targetPos + offsetZeroBase
            Case SeekPos.EndF
                targetPos = 1 + lFileLen - offsetZeroBase
        End Select

        ' 边界检查
        If targetPos < 1 Then targetPos = 1

        ' 检查是否超出VBA Seek函数的限制 (Long)
        If targetPos > 2147483647# Then
            Err.Raise 6, "IReadWrite_SeekFile", "目标位置超出VBA Seek函数的最大范围 (2GB)"
            Exit Function
        End If

        ' 关键修复: 在调用Seek前，将LongLong安全地转换为Long
        Seek #num_file, CLng(targetPos)

        IReadWrite_SeekFile = VBA.Seek(num_file) - 1
    #Else
        Dim targetPos_32 As Long
        targetPos_32 = VBA.Seek(num_file)

        Select Case whence
            Case SeekPos.OriginF
                targetPos_32 = 1 + offsetZeroBase
            Case SeekPos.CurrentF
                targetPos_32 = targetPos_32 + offsetZeroBase
            Case SeekPos.EndF
                targetPos_32 = 1 + lFileLen - offsetZeroBase
        End Select

        ' 边界检查
        If targetPos_32 < 1 Then targetPos_32 = 1

        Seek #num_file, targetPos_32
        IReadWrite_SeekFile = VBA.Seek(num_file) - 1
    #End If
End Function

Private Function IReadWrite_WriteFile(b() As Byte) As Long
   Put #num_file, , b
   IReadWrite_WriteFile = UBound(b) - LBound(b) + 1
End Function

Private Function IReadWrite_WriteLong(ByVal l As Long) As Long
    Put #num_file, , l
    IReadWrite_WriteLong = 4
End Function
