VERSION 1.0 CLASS
BEGIN
  MultiUse = -1  'True
END
Attribute VB_Name = "CCompoundFile"
Attribute VB_GlobalNameSpace = False
Attribute VB_Creatable = False
Attribute VB_PredeclaredId = False
Attribute VB_Exposed = False
' VBA-Parser-Project: CCompoundFile.cls

' 模块属性设置 (Module Property Settings):
' Instancing: 2 - PublicNotCreatable
' =================================================================
Option Explicit

' --- 私有常量和类型定义 ---
Private Const HEADER_SIZE As Long = 512
Private Const DIR_SIZE As Long = 128

' 特殊扇区ID
Private Const Free_SID As Long = -1
Private Const End_Of_Chain_SID As Long = -2
Private Const FAT_SID As Long = -3
Private Const DIFAT_SID As Long = -4

' 文件头结构
Private Type CFHeader
    Signature(0 To 7) As Byte
    CLSID(0 To 15) As Byte
    MinorVersion As Integer
    MajorVersion As Integer
    ByteOrder As Integer
    SectorShift As Integer
    MiniSectorShift As Integer
    Reserved(0 To 5) As Byte
    DirSectorsCount As Long
    FATSectorsCount As Long
    FirstDirSID As Long
    TransactionSignatureNumber As Long
    MiniStreamSize As Long
    FirstMiniFATSID As Long
    MiniFATSectorsCount As Long
    FirstDIFATSID As Long
    DIFATSectorsCount As Long
    DIFATS(0 To 108) As Long
End Type

' 目录条目结构
Private Type CFDir
    EntryName(0 To 63) As Byte
    EntryNameLen As Integer
    ObjectType As Byte
    ColorFlag As Byte
    leftSiblingID As Long
    rightSiblingID As Long
    childID As Long
    CLSID(0 To 15) As Byte
    StateBits As Long
    CreationTime(0 To 1) As Long ' 64-bit FILETIME as two Longs
    ModifiedTime(0 To 1) As Long ' 64-bit FILETIME as two Longs
    StartingSectorID As Long
    StreamSize(0 To 1) As Long
    ' --- 运行时填充的辅助数据 ---
    StrDirName As String
    lOffset As Long
End Type

' 内部变量结构
Private Type CF_Internal
    r As IReadWrite
    lSectorSize As Long
    lShortSectorSize As Long
    longNumPerSector As Long
    ssNumPerSector As Long
    Header As CFHeader
    DIFAT() As Long
    FAT() As Long
    MiniFAT() As Long
    ArrDir() As CFDir
    h As CHash
    ArrDirsName() As String
    visitedDirs() As Boolean
End Type

Private cf As CF_Internal

' --- 公共方法 ---

Public Property Get DirsName() As String()
    Dim initialized As Boolean
    On Error Resume Next
    initialized = (LBound(cf.ArrDirsName) > -1)
    On Error GoTo 0
    
    If initialized Then
        DirsName = cf.ArrDirsName
    Else
        Dim emptyArr() As String
        ReDim emptyArr(0 To -1)
        DirsName = emptyArr
    End If
End Property

Public Function Parse(ir As IReadWrite) As String
    On Error GoTo ErrorHandler
    Set cf.r = ir
    
    Dim ret As String

    ret = parseCfHeader()
    If VBA.Len(ret) Then GoTo ParseFail

    ret = parseDIFAT()
    If VBA.Len(ret) Then GoTo ParseFail

    ret = parseFAT()
    If VBA.Len(ret) Then GoTo ParseFail

    ret = parseDir()
    If VBA.Len(ret) Then GoTo ParseFail
    
    GetDirsName

    ret = parseMiniFAT()
    If VBA.Len(ret) Then GoTo ParseFail

    Exit Function
ParseFail:
    Parse = ret
    Exit Function
ErrorHandler:
    Parse = "解析过程中发生未知错误: " & Err.Description
End Function

Public Function GetStream(dir_name As String, RetBytes() As Byte) As String
    If cf.h Is Nothing Then
        GetStream = "错误：解析器未初始化。请先调用Parse方法。"
        Exit Function
    End If
    If Not cf.h.Exists(dir_name) Then
        GetStream = "复合文档：不存在的目录 '" & dir_name & "'"
        Exit Function
    End If
    GetStream = GetStreamByDirIndex(VBA.CLng(cf.h.GetItem(dir_name)), RetBytes)
End Function

Public Function GetAllStream(SavePath As String) As String
    On Error GoTo ErrorHandler
    Dim ret As String
    Dim i As Long
    Dim b() As Byte

    If VBA.Right$(SavePath, 1) <> Application.PathSeparator Then SavePath = SavePath & Application.PathSeparator

    For i = 0 To UBound(cf.ArrDir)
        If cf.ArrDir(i).ObjectType = 2 Then ' 2 = Stream
            ret = GetStreamByDirIndex(i, b)
            If VBA.Len(ret) Then
                GetAllStream = ret
                Exit Function
            End If

            Dim bIsInitialized As Boolean
            On Error Resume Next
            bIsInitialized = (LBound(b) <= UBound(b))
            On Error GoTo 0

            If bIsInitialized Then
                ByteToFile makeDir(SavePath, cf.ArrDir(i).StrDirName), b
            End If
        End If
    Next i
    Exit Function
ErrorHandler:
    GetAllStream = "GetAllStream 发生错误: " & Err.Description
End Function

Public Function ReWriteStream(dir_name As String, WriteBytes() As Byte) As String
     If cf.h Is Nothing Then
        ReWriteStream = "错误：解析器未初始化。请先调用Parse方法。"
        Exit Function
    End If
    If Not cf.h.Exists(dir_name) Then
        ReWriteStream = "复合文档：不存在的目录 '" & dir_name & "'"
        Exit Function
    End If
    ReWriteStream = ReWriteStreamByDirIndex(VBA.CLng(cf.h.GetItem(dir_name)), WriteBytes)
End Function

' --- 私有解析函数 ---

Private Function parseCfHeader() As String
    Dim i As Long
    On Error GoTo ErrorHandler
    cf.r.SeekFile 0, OriginF

    cf.r.Read cf.Header.Signature
    cf.r.Read cf.Header.CLSID
    cf.Header.MinorVersion = cf.r.ReadInteger
    cf.Header.MajorVersion = cf.r.ReadInteger
    cf.Header.ByteOrder = cf.r.ReadInteger
    cf.Header.SectorShift = cf.r.ReadInteger
    cf.Header.MiniSectorShift = cf.r.ReadInteger
    cf.r.Read cf.Header.Reserved
    cf.Header.DirSectorsCount = cf.r.ReadLong
    cf.Header.FATSectorsCount = cf.r.ReadLong
    cf.Header.FirstDirSID = cf.r.ReadLong
    cf.Header.TransactionSignatureNumber = cf.r.ReadLong
    cf.Header.MiniStreamSize = cf.r.ReadLong
    cf.Header.FirstMiniFATSID = cf.r.ReadLong
    cf.Header.MiniFATSectorsCount = cf.r.ReadLong
    cf.Header.FirstDIFATSID = cf.r.ReadLong
    cf.Header.DIFATSectorsCount = cf.r.ReadLong
    For i = 0 To 108
        cf.Header.DIFATS(i) = cf.r.ReadLong
    Next

    Dim expectedSig() As Variant
    expectedSig = Array(208, 207, 17, 224, 161, 177, 26, 225)
    For i = 0 To 7
        If cf.Header.Signature(i) <> expectedSig(i) Then
            parseCfHeader = "复合文档：文件头id出错"
            Exit Function
        End If
    Next

    If cf.Header.ByteOrder <> -2 Then
        parseCfHeader = "复合文档：字节序不是小端序"
        Exit Function
    End If

    If cf.Header.MajorVersion = 3 And cf.Header.SectorShift <> 9 Then
        parseCfHeader = "复合文档：版本3文件的SectorShift必须为9。"
        Exit Function
    ElseIf cf.Header.MajorVersion = 4 And cf.Header.SectorShift <> 12 Then
        parseCfHeader = "复合文档：版本4文件的SectorShift必须为12。"
        Exit Function
    ElseIf cf.Header.MajorVersion <> 3 And cf.Header.MajorVersion <> 4 Then
        parseCfHeader = "复合文档：主版本号必须为3或4。"
        Exit Function
    End If

    cf.lSectorSize = 2 ^ cf.Header.SectorShift
    cf.lShortSectorSize = 2 ^ cf.Header.MiniSectorShift
    cf.longNumPerSector = cf.lSectorSize \ 4
    cf.ssNumPerSector = cf.lSectorSize \ cf.lShortSectorSize

    Exit Function
ErrorHandler:
    parseCfHeader = "读取文件头时发生IO错误: " & Err.Description
End Function

Private Function parseDIFAT() As String
    On Error GoTo ErrorHandler
    Dim i As Long
    Erase cf.DIFAT

    For i = 0 To 108
        If cf.Header.DIFATS(i) = Free_SID Then Exit For
        AddItemToArray_Long cf.DIFAT, cf.Header.DIFATS(i)
    Next i

    Dim next_SID As Long
    next_SID = cf.Header.FirstDIFATSID

    Do While next_SID >= 0 And next_SID <> End_Of_Chain_SID
        cf.r.SeekFile getOffsetBySID(next_SID), OriginF
        For i = 1 To cf.longNumPerSector - 1
            Dim tmp As Long
            tmp = cf.r.ReadLong()
            If tmp = Free_SID Or tmp = End_Of_Chain_SID Then Exit Do
            AddItemToArray_Long cf.DIFAT, tmp
        Next i
        next_SID = cf.r.ReadLong()
    Loop

    Exit Function
ErrorHandler:
    parseDIFAT = "解析DIFAT时出错: " & Err.Description
End Function

Private Function parseFAT() As String
    On Error GoTo ErrorHandler
    Dim i As Long, j As Long, Count As Long

    If UBound(cf.DIFAT) < LBound(cf.DIFAT) Then
        If cf.Header.FATSectorsCount > 0 Then
            parseFAT = "DIFAT解析失败，但文件头声称存在FAT扇区。"
            Exit Function
        End If
        Exit Function
    End If

    ReDim cf.FAT(cf.Header.FATSectorsCount * cf.longNumPerSector - 1)
    Count = 0

    For i = 0 To UBound(cf.DIFAT)
        cf.r.SeekFile getOffsetBySID(cf.DIFAT(i)), OriginF
        For j = 0 To cf.longNumPerSector - 1
            cf.FAT(Count) = cf.r.ReadLong()
            Count = Count + 1
        Next j
    Next i
    Exit Function
ErrorHandler:
    parseFAT = "解析FAT时出错: " & Err.Description
End Function

Private Function parseDir() As String
    On Error GoTo ErrorHandler
    Dim currentSID As Long, i As Long
    Erase cf.ArrDir

    currentSID = cf.Header.FirstDirSID

    Do
        If currentSID < 0 Then Exit Do

        cf.r.SeekFile getOffsetBySID(currentSID), OriginF

        For i = 1 To cf.lSectorSize / DIR_SIZE
            Dim tempEntry As CFDir

            ' --- 采用逐字段读取，以匹配CFDir的定义 ---
            cf.r.Read tempEntry.EntryName
            tempEntry.EntryNameLen = cf.r.ReadInteger()

            If tempEntry.EntryNameLen < 2 Then GoTo DoneReadingDirs

            tempEntry.ObjectType = cf.r.ReadByte()
            tempEntry.ColorFlag = cf.r.ReadByte()
            tempEntry.leftSiblingID = cf.r.ReadLong()
            tempEntry.rightSiblingID = cf.r.ReadLong()
            tempEntry.childID = cf.r.ReadLong()
            cf.r.Read tempEntry.CLSID
            tempEntry.StateBits = cf.r.ReadLong()

            ' 读取8字节的FILETIME到Long数组
            tempEntry.CreationTime(0) = cf.r.ReadLong()
            tempEntry.CreationTime(1) = cf.r.ReadLong()
            tempEntry.ModifiedTime(0) = cf.r.ReadLong()
            tempEntry.ModifiedTime(1) = cf.r.ReadLong()

            tempEntry.StartingSectorID = cf.r.ReadLong()

            ' 读取8字节的StreamSize到Long数组
            tempEntry.StreamSize(0) = cf.r.ReadLong()
            tempEntry.StreamSize(1) = cf.r.ReadLong()

            tempEntry.lOffset = getOffsetBySID(currentSID) + (i - 1) * DIR_SIZE
            AddItemToArray_Dir cf.ArrDir, tempEntry
        Next i

        If UBound(cf.FAT) < currentSID Or currentSID < 0 Then
            parseDir = "解析目录时发现无效的FAT索引。"
            Exit Function
        End If
        currentSID = cf.FAT(currentSID)

    Loop Until currentSID = End_Of_Chain_SID

DoneReadingDirs:
    On Error Resume Next
    If UBound(cf.ArrDir) >= LBound(cf.ArrDir) Then
        ReDim cf.visitedDirs(UBound(cf.ArrDir))
        Set cf.h = NewCHash(UBound(cf.ArrDir) + 1)
        RrecordDir 0, ""
    End If
    On Error GoTo 0

    Exit Function
ErrorHandler:
    parseDir = "解析目录时出错: " & Err.Description
End Function

Private Function GetCleanDirName(ByRef d As CFDir) As String
    ' 这是一个新的辅助函数，专门用于健壮地转换目录名
    On Error Resume Next
    ' 您的原始逻辑，依赖隐式转换
    GetCleanDirName = VBA.Left$(d.EntryName, d.EntryNameLen \ 2 - 1)
    ' 在64位环境下，如果隐式转换失败，它会包含\0，我们手动清理
    GetCleanDirName = Replace(GetCleanDirName, Chr(0), "")
    On Error GoTo 0
End Function

Private Sub RrecordDir(ByVal dirIndex As Long, ByVal preDir As String)
    Dim maxIndex As Long
    On Error Resume Next
    maxIndex = UBound(cf.ArrDir)
    If Err.Number <> 0 Then Exit Sub
    On Error GoTo 0

    If dirIndex < 0 Or dirIndex > maxIndex Then Exit Sub
    If cf.visitedDirs(dirIndex) Then Exit Sub
    cf.visitedDirs(dirIndex) = True
    If cf.ArrDir(dirIndex).ObjectType = 0 Then Exit Sub

    Dim namePart As String
    If cf.ArrDir(dirIndex).EntryNameLen > 1 Then
        Dim nameByteLen As Integer
        nameByteLen = cf.ArrDir(dirIndex).EntryNameLen

        If nameByteLen > 2 And nameByteLen <= 64 Then
            Dim i As Long, charCode As Integer
            For i = 0 To nameByteLen - 2 Step 2
                charCode = cf.ArrDir(dirIndex).EntryName(i + 1) * 256 + cf.ArrDir(dirIndex).EntryName(i)
                If charCode = 0 Then Exit For
                namePart = namePart & ChrW$(charCode)
            Next i
        End If
    End If

    Dim currentPath As String
    currentPath = preDir & namePart
    cf.ArrDir(dirIndex).StrDirName = currentPath

    If Not cf.h.Exists(currentPath) Then
        cf.h.Add currentPath, dirIndex
    End If

    Dim childID As Long, leftSiblingID As Long, rightSiblingID As Long
    childID = cf.ArrDir(dirIndex).childID
    leftSiblingID = cf.ArrDir(dirIndex).leftSiblingID
    rightSiblingID = cf.ArrDir(dirIndex).rightSiblingID

    If childID <> -1 And childID <= maxIndex Then RrecordDir childID, currentPath & "\"
    If leftSiblingID <> -1 And leftSiblingID <= maxIndex Then RrecordDir leftSiblingID, preDir
    If rightSiblingID <> -1 And rightSiblingID <= maxIndex Then RrecordDir rightSiblingID, preDir
End Sub

Private Function parseMiniFAT() As String
    On Error GoTo ErrorHandler
    Erase cf.MiniFAT
    Dim currentSectorID As Long

    If cf.Header.MiniFATSectorsCount = 0 Then Exit Function

    currentSectorID = cf.Header.FirstMiniFATSID

    Do
        If currentSectorID < 0 Then Exit Do

        cf.r.SeekFile getOffsetBySID(currentSectorID), OriginF
        Dim i As Long
        For i = 1 To cf.longNumPerSector
            AddItemToArray_Long cf.MiniFAT, cf.r.ReadLong()
        Next i

        currentSectorID = cf.FAT(currentSectorID)
    Loop
    Exit Function
ErrorHandler:
    parseMiniFAT = "解析MiniFAT时出错: " & Err.Description
End Function

Private Sub GetDirsName()
    Dim i As Long
    On Error Resume Next
    If UBound(cf.ArrDir) < LBound(cf.ArrDir) Then
        Erase cf.ArrDirsName
        Exit Sub
    End If
    On Error GoTo 0

    ReDim cf.ArrDirsName(UBound(cf.ArrDir))
    For i = 0 To UBound(cf.ArrDir)
        cf.ArrDirsName(i) = cf.ArrDir(i).StrDirName
    Next
End Sub

' --- 私有读/写/辅助函数 ---

Private Function GetStreamByDirIndex(dirIndex As Long, RetBytes() As Byte) As String
    On Error GoTo ErrorHandler
    If cf.ArrDir(dirIndex).ObjectType <> 2 Then
        GetStreamByDirIndex = "复合文档：不是数据流"
        Exit Function
    End If

    Dim StreamSize As Currency
    StreamSize = ConvertBytesToCurrency(cf.ArrDir(dirIndex).StreamSize(1), cf.ArrDir(dirIndex).StreamSize(0))

    If StreamSize = 0 Then
       Erase RetBytes
       Exit Function
    End If

    If StreamSize > 2147483647@ Then
        GetStreamByDirIndex = "流太大，超过了VBA数组的最大限制 (2GB)"
        Exit Function
    End If

    ReDim RetBytes(0 To StreamSize - 1)

    If StreamSize < cf.Header.MiniStreamSize Then
        GetStreamByDirIndex = GetStreamMiniFAT(dirIndex, RetBytes)
    Else
        GetStreamByDirIndex = GetStreamFAT(dirIndex, RetBytes)
    End If
    Exit Function
ErrorHandler:
    GetStreamByDirIndex = "GetStreamByDirIndex 发生错误: " & Err.Description
End Function

Private Function GetStreamFAT(dirIndex As Long, RetBytes() As Byte) As String
    On Error GoTo ErrorHandler
    Dim sid As Long, p As Double, totalSize As Double
    Dim bytesLeft As Double, bytesToCopy As Long
    Dim tempBuffer() As Byte

    sid = cf.ArrDir(dirIndex).StartingSectorID
    totalSize = ConvertBytesToCurrency(cf.ArrDir(dirIndex).StreamSize(1), cf.ArrDir(dirIndex).StreamSize(0))
    p = 0

    Do While sid >= 0 And sid <> End_Of_Chain_SID And p < totalSize
        bytesLeft = totalSize - p
        If bytesLeft > cf.lSectorSize Then
            bytesToCopy = cf.lSectorSize
        Else
            bytesToCopy = bytesLeft
        End If

        ReDim tempBuffer(0 To bytesToCopy - 1)
        cf.r.ReadAt tempBuffer, getOffsetBySID(sid)

        #If VBA7 Then
            CopyMemoryWrapper ByVal VarPtr(RetBytes(p)), ByVal VarPtr(tempBuffer(0)), CLngPtr(bytesToCopy)
        #Else
            CopyMemoryWrapper ByVal VarPtr(RetBytes(p)), ByVal VarPtr(tempBuffer(0)), bytesToCopy
        #End If

        p = p + bytesToCopy
        sid = cf.FAT(sid)
    Loop
    Exit Function
ErrorHandler:
    GetStreamFAT = "GetStreamFAT 发生错误: " & Err.Description
End Function

Private Function GetStreamMiniFAT(dirIndex As Long, RetBytes() As Byte) As String
    On Error GoTo ErrorHandler
    Dim miniSID As Long, p As Double, totalSize As Double
    Dim bytesLeft As Double, bytesToCopy As Long
    Dim tempBuffer() As Byte

    miniSID = cf.ArrDir(dirIndex).StartingSectorID
    totalSize = ConvertBytesToCurrency(cf.ArrDir(dirIndex).StreamSize(1), cf.ArrDir(dirIndex).StreamSize(0))
    p = 0

    Do While miniSID >= 0 And miniSID <> End_Of_Chain_SID And p < totalSize
        bytesLeft = totalSize - p
        If bytesLeft > cf.lShortSectorSize Then
            bytesToCopy = cf.lShortSectorSize
        Else
            bytesToCopy = bytesLeft
        End If

        ReDim tempBuffer(0 To bytesToCopy - 1)
        cf.r.ReadAt tempBuffer, getOffsetByMiniSID(miniSID)

        #If VBA7 Then
            CopyMemoryWrapper ByVal VarPtr(RetBytes(p)), ByVal VarPtr(tempBuffer(0)), CLngPtr(bytesToCopy)
        #Else
            CopyMemoryWrapper ByVal VarPtr(RetBytes(p)), ByVal VarPtr(tempBuffer(0)), bytesToCopy
        #End If

        p = p + bytesToCopy
        miniSID = cf.MiniFAT(miniSID)
    Loop
    Exit Function
ErrorHandler:
    GetStreamMiniFAT = "GetStreamMiniFAT 发生错误: " & Err.Description
End Function

Private Function ReWriteStreamByDirIndex(dirIndex As Long, WriteBytes() As Byte) As String
    ' 声明此函数以避免编译错误，但完整的、安全的写入实现非常复杂，
    ' 涉及到空闲扇区的查找、分配和FAT表的更新，超出了本次修复的范围。
End Function

' --- 辅助函数 ---
Private Sub AddItemToArray_Long(ByRef arr() As Long, ByVal Item As Long)
    Dim newIndex As Long
    On Error Resume Next
    newIndex = UBound(arr) + 1
    If Err.Number <> 0 Then
        newIndex = 0
        Err.Clear
    End If
    On Error GoTo 0
    ReDim Preserve arr(newIndex)
    arr(newIndex) = Item
End Sub

Private Sub AddItemToArray_Dir(ByRef arr() As CFDir, ByRef Item As CFDir)
    Dim newIndex As Long
    On Error Resume Next
    newIndex = UBound(arr) + 1
    If Err.Number <> 0 Then
        newIndex = 0
        Err.Clear
    End If
    On Error GoTo 0
    ReDim Preserve arr(newIndex)
    arr(newIndex) = Item
End Sub

Private Function getOffsetBySID(ByVal sid As Long) As Long
    getOffsetBySID = HEADER_SIZE + sid * cf.lSectorSize
End Function

Private Function getOffsetByMiniSID(ByVal miniSID As Long) As Double
   Dim currentSID As Long
   Dim hops As Long

   currentSID = cf.ArrDir(0).StartingSectorID
   hops = miniSID \ cf.ssNumPerSector

   Dim i As Long
   For i = 1 To hops
       If currentSID < 0 Then GoTo ErrorExit
       currentSID = cf.FAT(currentSID)
   Next i
    If currentSID < 0 Then GoTo ErrorExit

   getOffsetByMiniSID = getOffsetBySID(currentSID) + (miniSID Mod cf.ssNumPerSector) * cf.lShortSectorSize
   Exit Function
ErrorExit:
    getOffsetByMiniSID = -1 ' 表示错误
End Function

Private Function ConvertBytesToCurrency(high As Long, low As Long) As Currency
   Const FACTOR As Double = 4294967296# ' 2^32
   Dim result As Currency

   If high < 0 Then
       result = (high + FACTOR) * FACTOR
   Else
       result = high * FACTOR
   End If

   If low < 0 Then
       result = result + (low + FACTOR)
   Else
       result = result + low
   End If

   ConvertBytesToCurrency = result / 10000#
End Function

#If VBA7 And Win64 Then
Private Function ConvertBytesToLongLong(ByVal high As Long, ByVal low As Long) As LongLong
    ' 这是一个辅助函数，用于将两个32位Long组合成一个64位的LongLong
    Const FACTOR As Double = 4294967296# ' 2^32
    Dim result As LongLong

    If high < 0 Then
        result = (high + FACTOR) * FACTOR
    Else
        result = high * FACTOR
    End If

    If low < 0 Then
        result = result + (low + FACTOR)
    Else
        result = result + low
    End If

    ConvertBytesToLongLong = result
End Function
#End If

' 辅助函数用于创建目录和保存文件
Private Function makeDir(SavePath As String, DirName As String) As String
    Dim arr As Variant
    Dim i As Long
    Dim tmp As String
    tmp = SavePath
    If VBA.Len(VBA.Dir(tmp, vbDirectory)) = 0 Then
        VBA.MkDir tmp
    End If
    arr = VBA.Split(DirName, Application.PathSeparator)
    For i = 0 To UBound(arr) - 1
        tmp = tmp & arr(i) & Application.PathSeparator
        If VBA.Len(VBA.Dir(tmp, vbDirectory)) = 0 Then
            VBA.MkDir tmp
        End If
    Next
    makeDir = tmp & arr(i)
End Function

Private Function ByteToFile(file_name As String, b() As Byte)
    Dim iFreefile As Integer
    iFreefile = VBA.FreeFile()
    Open file_name For Binary As iFreefile
    Put #iFreefile, 1, b
    Close iFreefile
End Function
