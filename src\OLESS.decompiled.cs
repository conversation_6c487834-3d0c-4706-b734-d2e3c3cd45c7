using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using System.Runtime.InteropServices.ComTypes;
using System.Security;
using System.Security.Permissions;
using System.Text;
using Parse;

[assembly: CompilationRelaxations(8)]
[assembly: AssemblyDescription("")]
[assembly: Debuggable(DebuggableAttribute.DebuggingModes.IgnoreSymbolStoreSequencePoints)]
[assembly: AssemblyCompany("")]
[assembly: RuntimeCompatibility(WrapNonExceptionThrows = true)]
[assembly: AssemblyProduct("OLESS")]
[assembly: AssemblyTitle("OLESS")]
[assembly: AssemblyConfiguration("")]
[assembly: AssemblyTrademark("")]
[assembly: AssemblyFileVersion("*******")]
[assembly: AssemblyCopyright("Copyright ©  2008")]
[assembly: ComVisible(false)]
[assembly: Guid("3e0c1dbf-3c06-4292-98e7-8564f9d2aa23")]
[assembly: SecurityPermission(SecurityAction.RequestMinimum, SkipVerification = true)]
[assembly: AssemblyVersion("*******")]
[module: UnverifiableCode]
namespace Parse
{
	public class EmbeddedStreamOffset
	{
		public long StreamOffset;

		public long ParentOffset;

		public long Count;
	}
	public class EmbeddedStream
	{
		public EmbeddedStream Parent;

		public Stream Stream = new MemoryStream();

		public List<EmbeddedStreamOffset> Map = new List<EmbeddedStreamOffset>();

		public void AddRangeMap(long streamOffset, long parentOffset, long count)
		{
			EmbeddedStreamOffset embeddedStreamOffset;
			if (Map.Count > 0)
			{
				embeddedStreamOffset = Map[Map.Count - 1];
				if (embeddedStreamOffset.ParentOffset + embeddedStreamOffset.Count == parentOffset && embeddedStreamOffset.StreamOffset + embeddedStreamOffset.Count == streamOffset)
				{
					embeddedStreamOffset.Count += count;
					return;
				}
			}
			embeddedStreamOffset = new EmbeddedStreamOffset();
			embeddedStreamOffset.StreamOffset = streamOffset;
			embeddedStreamOffset.ParentOffset = parentOffset;
			embeddedStreamOffset.Count = count;
			Map.Add(embeddedStreamOffset);
		}

		public long EmbeddedToParentOffset(long address)
		{
			foreach (EmbeddedStreamOffset item in Map)
			{
				if (address >= item.StreamOffset && address < item.StreamOffset + item.Count)
				{
					return item.ParentOffset + (address - item.StreamOffset);
				}
			}
			return -1L;
		}

		public long ParentToEmbeddedOffset(long address)
		{
			foreach (EmbeddedStreamOffset item in Map)
			{
				if (address >= item.ParentOffset && address < item.ParentOffset + item.Count)
				{
					return item.StreamOffset + (address - item.ParentOffset);
				}
			}
			return -1L;
		}
	}
	public class BitFieldData
	{
		public ulong bits;

		public Type type;
	}
	public class BitField
	{
		public int shift;

		public int count;

		public BitFieldData data;

		public ulong Value
		{
			get
			{
				return (data.bits & GetMask()) >> shift;
			}
			set
			{
				data.bits = (data.bits & ~GetMask()) | ((value << shift) & GetMask());
			}
		}

		public ulong GetMask()
		{
			ulong num = 0uL;
			for (int i = 0; i < count; i++)
			{
				num <<= 1;
				num |= 1;
			}
			return num << shift;
		}

		public string GetMaskString()
		{
			int num = Marshal.SizeOf(data.type) * 8;
			string text = "";
			ulong num2 = GetMask();
			for (int i = 0; i < num; i++)
			{
				text = (((num2 & 1) != 1) ? ("0" + text) : ("1" + text));
				num2 >>= 1;
			}
			return text;
		}
	}
	public class MarkupStruct
	{
		public string Name = "";

		public string Type = "";

		public string Value = "";

		public EmbeddedStream Data;

		public long Offset;

		public long Size;

		public MarkupStruct Parent;

		public object Instance;

		public string Tag = "";

		private List<MarkupStruct> Fields;

		public List<BaseMessage> Messages;

		public virtual MarkupStruct this[string field] => FindChildField(field, recurse: false);

		public string GetTagPath()
		{
			return Tag.Substring(0, Tag.LastIndexOf('\\'));
		}

		public string GetTagName()
		{
			return Tag.Substring(Tag.LastIndexOf('\\') + 1);
		}

		public List<MarkupStruct> Find(string path)
		{
			List<MarkupStruct> list = new List<MarkupStruct>();
			bool recurse = false;
			bool flag = false;
			string text = "";
			string text2 = "";
			if (path.StartsWith("\\\\") || path.StartsWith("//"))
			{
				recurse = true;
				flag = true;
				text = path.Substring(2);
			}
			else if (!path.StartsWith("\\") && !path.StartsWith("/"))
			{
				text = ((!path.StartsWith(".")) ? path : path.Substring(1));
			}
			else
			{
				flag = true;
				text = path.Substring(1);
			}
			int i = 0;
			for (int length = text.Length; i < length; i++)
			{
				if (text[i] == '\\' || text[i] == '.' || text[i] == '/')
				{
					text2 = text.Substring(i);
					text = text.Substring(0, i);
					break;
				}
			}
			if (flag)
			{
				foreach (MarkupStruct item in FindChildFieldsByType(text.Replace("::", "."), recurse))
				{
					if (text2.Length > 0)
					{
						list.AddRange(item.Find(text2));
					}
					else
					{
						list.Add(item);
					}
				}
			}
			else
			{
				foreach (MarkupStruct item2 in FindChildFields(text, recurse))
				{
					if (text2.Length > 0)
					{
						list.AddRange(item2.Find(text2));
					}
					else
					{
						list.Add(item2);
					}
				}
			}
			return list;
		}

		public bool HasFields()
		{
			return GetFieldCount() > 0;
		}

		public List<MarkupStruct> GetFields()
		{
			List<MarkupStruct> list = new List<MarkupStruct>();
			if (Fields != null)
			{
				list.AddRange(Fields);
			}
			return list;
		}

		public int GetFieldCount()
		{
			if (Fields != null)
			{
				return Fields.Count;
			}
			return 0;
		}

		public MarkupStruct GetLastField()
		{
			if (GetFieldCount() > 0)
			{
				return Fields[Fields.Count - 1];
			}
			return null;
		}

		public T AsT<T>()
		{
			return (T)Instance;
		}

		public int AsInteger()
		{
			return int.Parse(Value);
		}

		public long AsLong()
		{
			return long.Parse(Value);
		}

		public void AddField(MarkupStruct field)
		{
			if (Fields == null)
			{
				Fields = new List<MarkupStruct>();
			}
			Fields.Add(field);
		}

		public void AddMessage(BaseMessage msg)
		{
			if (Messages == null)
			{
				Messages = new List<BaseMessage>();
			}
			msg.Fields.Add(this);
			Messages.Add(msg);
		}

		public void AddHint(int id, string message)
		{
			ParseHint msg = new ParseHint(id, message);
			AddMessage(msg);
		}

		public void AddHint(int id, string message, object[] values)
		{
			ParseHint msg = new ParseHint(id, message, values);
			AddMessage(msg);
		}

		public void AddWarning(int id, string message)
		{
			ParseWarning msg = new ParseWarning(id, message);
			AddMessage(msg);
		}

		public void AddWarning(int id, string message, object[] values)
		{
			ParseWarning msg = new ParseWarning(id, message, values);
			AddMessage(msg);
		}

		public void AddErrorCVE(int id, string message)
		{
			ParseError msg = new ParseError(id, message);
			AddMessage(msg);
		}

		public void AddErrorCVE(int id, string message, object[] values)
		{
			ParseError msg = new ParseError(id, message, values);
			AddMessage(msg);
		}

		public bool HasMessages()
		{
			if (Messages == null)
			{
				return false;
			}
			return Messages.Count > 0;
		}

		public string GetMessageSummary()
		{
			string text = "";
			if (Messages != null)
			{
				foreach (BaseMessage message in Messages)
				{
					if (text.Length > 0)
					{
						text += Environment.NewLine;
					}
					text += message.Message;
				}
			}
			return text;
		}

		public void ClearMessages(bool recurse)
		{
			if (Messages != null)
			{
				Messages.Clear();
			}
			if (!recurse || Fields == null)
			{
				return;
			}
			foreach (MarkupStruct field in Fields)
			{
				field.ClearMessages(recurse);
			}
		}

		public List<BaseMessage> FindMessages(bool recurse)
		{
			List<BaseMessage> list = new List<BaseMessage>();
			if (Messages != null)
			{
				list.AddRange(Messages);
			}
			if (recurse && Fields != null)
			{
				foreach (MarkupStruct field in Fields)
				{
					list.AddRange(field.FindMessages(recurse));
				}
			}
			return list;
		}

		public MarkupStruct FindParentByType(string type)
		{
			if (Parent != null)
			{
				if (Parent.Type.Equals(type, StringComparison.InvariantCultureIgnoreCase))
				{
					return Parent;
				}
				return Parent.FindParentByType(type);
			}
			return null;
		}

		public Stream GetStream()
		{
			if (Data != null)
			{
				return Data.Stream;
			}
			if (Parent != null)
			{
				return Parent.GetStream();
			}
			return null;
		}

		public long ConvertToGlobalAddress(long address)
		{
			if (Parent != null)
			{
				if (Parent.Data != null)
				{
					for (EmbeddedStream embeddedStream = Parent.Data; embeddedStream != null; embeddedStream = embeddedStream.Parent)
					{
						address = embeddedStream.EmbeddedToParentOffset(address);
					}
					return address;
				}
				return Parent.ConvertToGlobalAddress(address);
			}
			return address;
		}

		public MarkupStruct FindChildField(string name, bool recurse)
		{
			if (Fields != null)
			{
				foreach (MarkupStruct field in Fields)
				{
					if (field.Name.Equals(name, StringComparison.InvariantCultureIgnoreCase))
					{
						return field;
					}
					if (recurse)
					{
						MarkupStruct markupStruct = field.FindChildField(name, recurse);
						if (markupStruct != null)
						{
							return markupStruct;
						}
					}
				}
			}
			return null;
		}

		public List<MarkupStruct> FindChildFields(string name, bool recurse)
		{
			List<MarkupStruct> list = new List<MarkupStruct>();
			if (Fields != null)
			{
				foreach (MarkupStruct field in Fields)
				{
					if (field.Name.Equals(name, StringComparison.InvariantCultureIgnoreCase))
					{
						list.Add(field);
					}
					if (recurse)
					{
						list.AddRange(field.FindChildFields(name, recurse));
					}
				}
			}
			return list;
		}

		public List<MarkupStruct> FindChildFieldsByType(string type, bool recurse)
		{
			List<MarkupStruct> list = new List<MarkupStruct>();
			if (Fields != null)
			{
				foreach (MarkupStruct field in Fields)
				{
					if (field.Type.Equals(type, StringComparison.InvariantCultureIgnoreCase))
					{
						list.Add(field);
					}
					if (recurse)
					{
						list.AddRange(field.FindChildFieldsByType(type, recurse));
					}
				}
			}
			return list;
		}

		public List<MarkupStruct> FindChildFieldsByType(Type type, bool recurse)
		{
			return FindChildFieldsByType(type.FullName, recurse);
		}

		public List<MarkupStruct> FindChildFieldsOfTypes(string[] types, bool recurse)
		{
			List<MarkupStruct> list = new List<MarkupStruct>();
			if (Fields != null)
			{
				foreach (MarkupStruct field in Fields)
				{
					foreach (string text in types)
					{
						if (field.Type.Equals(text, StringComparison.InvariantCultureIgnoreCase))
						{
							list.Add(field);
						}
						if (recurse)
						{
							list.AddRange(field.FindChildFieldsByType(text, recurse));
						}
					}
				}
			}
			return list;
		}

		public List<MarkupStruct> FindChildFieldsOfTypes(Type[] types, bool recurse)
		{
			string[] array = new string[types.Length];
			for (int i = 0; i < types.Length; i++)
			{
				array[i] = types[i].FullName;
			}
			return FindChildFieldsOfTypes(array, recurse);
		}
	}
	public class BaseMessage
	{
		public string RawMessage = "";

		public int ID = 1;

		public List<MarkupStruct> Fields = new List<MarkupStruct>();

		public object[] Values;

		public long Offset = -1L;

		public string Message
		{
			get
			{
				if (Values != null)
				{
					return string.Format(RawMessage, Values);
				}
				return RawMessage;
			}
		}

		public BaseMessage(string message)
		{
			RawMessage = message;
		}

		public BaseMessage(int id, string message)
		{
			RawMessage = message;
			ID = id;
		}

		public BaseMessage(int id, string message, object[] values)
		{
			RawMessage = message;
			ID = id;
			Values = values;
		}

		public long GetOffset()
		{
			if (Offset >= 0)
			{
				return Offset;
			}
			if (Fields.Count > 0)
			{
				return Fields[0].ConvertToGlobalAddress(Fields[0].Offset);
			}
			return -1L;
		}

		public long GetStreamOffset()
		{
			if (Fields.Count > 0)
			{
				return Fields[0].Offset;
			}
			return -1L;
		}
	}
	public class ParseError : BaseMessage
	{
		public ParseError(string message)
			: base(message)
		{
		}

		public ParseError(int id, string message)
			: base(id, message)
		{
		}

		public ParseError(int id, string message, object[] values)
			: base(id, message, values)
		{
		}
	}
	public class ParseWarning : BaseMessage
	{
		public ParseWarning(string message)
			: base(message)
		{
		}

		public ParseWarning(int id, string message)
			: base(id, message)
		{
		}

		public ParseWarning(int id, string message, object[] values)
			: base(id, message, values)
		{
		}
	}
	public class ParseHint : BaseMessage
	{
		public ParseHint(string message)
			: base(message)
		{
		}

		public ParseHint(int id, string message)
			: base(id, message)
		{
		}

		public ParseHint(int id, string message, object[] values)
			: base(id, message, values)
		{
		}
	}
	[AttributeUsage(AttributeTargets.Field)]
	public class BitFieldAttribute : Attribute
	{
		public int BitCount { get; set; }

		public Type Type { get; set; }

		public BitFieldAttribute(int bits, Type type)
		{
			BitCount = bits;
			Type = type;
		}
	}
	[AttributeUsage(AttributeTargets.Field)]
	public class ArraySizeAttribute : Attribute
	{
		public int ArraySize { get; set; }

		public ArraySizeAttribute(int size)
		{
			ArraySize = size;
		}
	}
	[AttributeUsage(AttributeTargets.Field)]
	public class ParseStage : Attribute
	{
		public int Stage { get; set; }

		public ParseStage(int stage)
		{
			Stage = stage;
		}
	}
	public class Parser
	{
		private class ParsePosition
		{
			public Stream Stream;

			public long Position;
		}

		private Stack<ParsePosition> PositionStack = new Stack<ParsePosition>();

		private Stream BaseStream;

		private BinaryReader r;

		private Stack<MarkupStruct> MarkupStack = new Stack<MarkupStruct>();

		public long Position
		{
			get
			{
				return BaseStream.Position;
			}
			set
			{
				BaseStream.Position = value;
			}
		}

		public long Length => BaseStream.Length;

		public MarkupStruct this[string field] => GetCurrentStruct()[field];

		public void PushPosition()
		{
			ParsePosition parsePosition = new ParsePosition();
			parsePosition.Stream = BaseStream;
			parsePosition.Position = BaseStream.Position;
			PositionStack.Push(parsePosition);
		}

		public void PopPosition()
		{
			if (BaseStream != PositionStack.Peek().Stream)
			{
				SetStream(PositionStack.Peek().Stream);
			}
			BaseStream.Position = PositionStack.Peek().Position;
			PositionStack.Pop();
		}

		public EmbeddedStream GetEmbeddedStream()
		{
			for (MarkupStruct markupStruct = GetCurrentStruct(); markupStruct != null; markupStruct = markupStruct.Parent)
			{
				if (markupStruct.Data != null)
				{
					return markupStruct.Data;
				}
			}
			return null;
		}

		public Stream GetStream()
		{
			return BaseStream;
		}

		public void SetStream(Stream stream)
		{
			BaseStream = stream;
			r = new BinaryReader(BaseStream);
		}

		public void SetMarkupRoot(MarkupStruct root)
		{
			MarkupStack.Clear();
			MarkupStack.Push(root);
		}

		public Parser()
		{
			MarkupStruct item = new MarkupStruct();
			MarkupStack.Push(item);
		}

		public Parser(MarkupStruct data)
		{
			MarkupStack.Push(data);
		}

		public long ReadLong()
		{
			return r.ReadInt64();
		}

		public ulong ReadULong()
		{
			return r.ReadUInt64();
		}

		public short ReadShort()
		{
			return r.ReadInt16();
		}

		public ushort ReadUShort()
		{
			return r.ReadUInt16();
		}

		public ulong ReadUInt64()
		{
			return r.ReadUInt64();
		}

		public uint ReadUInt32()
		{
			return r.ReadUInt32();
		}

		public ushort ReadUInt16()
		{
			return r.ReadUInt16();
		}

		public long ReadInt64()
		{
			return r.ReadInt64();
		}

		public int ReadInt32()
		{
			return r.ReadInt32();
		}

		public short ReadInt16()
		{
			return r.ReadInt16();
		}

		public byte ReadByte()
		{
			return r.ReadByte();
		}

		public byte[] ReadBytes(int count)
		{
			return r.ReadBytes(count);
		}

		public object ReadPrimitive(Type t)
		{
			return t.FullName switch
			{
				"System.Byte" => ReadByte(), 
				"System.UInt16" => ReadUInt16(), 
				"System.UInt32" => ReadUInt32(), 
				"System.UInt64" => ReadUInt64(), 
				"System.Int16" => ReadInt16(), 
				"System.Int32" => ReadInt32(), 
				"System.Int64" => ReadInt64(), 
				_ => null, 
			};
		}

		public object ParsePrimitive(Type t, string name)
		{
			return t.FullName switch
			{
				"System.Byte" => ParseByte(name), 
				"System.UInt16" => ParseUInt16(name), 
				"System.UInt32" => ParseUInt32(name), 
				"System.UInt64" => ParseUInt64(name), 
				"System.Int16" => ParseInt16(name), 
				"System.Int32" => ParseInt32(name), 
				"System.Int64" => ParseInt64(name), 
				_ => null, 
			};
		}

		public object ParseField(FieldInfo fi)
		{
			object obj = ParsePrimitive(fi.FieldType, fi.Name);
			if (obj != null)
			{
				return obj;
			}
			if (fi.FieldType == typeof(BitField))
			{
				object[] customAttributes = fi.GetCustomAttributes(typeof(BitFieldAttribute), inherit: false);
				if (customAttributes.Length > 0)
				{
					BitFieldAttribute bitFieldAttribute = (BitFieldAttribute)customAttributes[0];
					BitField bitField = new BitField();
					bitField.count = bitFieldAttribute.BitCount;
					long offset = Position;
					MarkupStruct currentField = GetCurrentField();
					BitField bitField2 = null;
					if (currentField.Instance != null && currentField.Instance.GetType() == typeof(BitField))
					{
						bitField2 = (BitField)currentField.Instance;
					}
					if (bitField2 != null && bitField2.data.type == bitFieldAttribute.Type && bitField.count + bitField2.shift + bitField2.count <= Marshal.SizeOf(bitFieldAttribute.Type) * 8)
					{
						bitField.shift = bitField2.shift + bitField2.count;
						bitField.data = bitField2.data;
						offset = currentField.Offset;
					}
					if (bitField.data == null)
					{
						bitField.data = new BitFieldData();
						bitField.data.type = bitFieldAttribute.Type;
						bitField.data.bits = Convert.ToUInt64(ReadPrimitive(bitFieldAttribute.Type));
					}
					currentField = new MarkupStruct();
					currentField.Name = fi.Name;
					currentField.Type = $"{bitFieldAttribute.Type.FullName}:{bitField.GetMaskString()}";
					currentField.Value = bitField.Value.ToString();
					currentField.Instance = bitField;
					currentField.Offset = offset;
					currentField.Size = Marshal.SizeOf(bitFieldAttribute.Type);
					AddMarkup(currentField);
					return bitField;
				}
				throw new Exception("BitField defined without BitField attribute");
			}
			if (fi.FieldType.IsArray)
			{
				object[] customAttributes2 = fi.GetCustomAttributes(typeof(ArraySizeAttribute), inherit: false);
				if (customAttributes2.Length > 0)
				{
					ArraySizeAttribute arraySizeAttribute = (ArraySizeAttribute)customAttributes2[0];
					Array array = Array.CreateInstance(fi.FieldType.GetElementType(), arraySizeAttribute.ArraySize);
					Enter(fi.Name, fi.FieldType.GetElementType().FullName + "[" + arraySizeAttribute.ArraySize + "]");
					for (int i = 0; i < arraySizeAttribute.ArraySize; i++)
					{
						array.SetValue(ParseClass(fi.FieldType.GetElementType(), "[" + i + "]"), i);
					}
					Leave();
					return array;
				}
				return null;
			}
			if (fi.FieldType.IsClass)
			{
				return ParseClass(fi.FieldType, fi.Name);
			}
			throw new Exception("Error parsing type " + fi.FieldType.FullName);
		}

		public T ParseT<T>(string name)
		{
			return (T)ParseClass(typeof(T), name);
		}

		public void ParseStage(object obj, int stage)
		{
			int num = 0;
			FieldInfo[] fields = obj.GetType().GetFields(BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic);
			foreach (FieldInfo fieldInfo in fields)
			{
				if (fieldInfo.DeclaringType == obj.GetType())
				{
					object[] customAttributes = fieldInfo.GetCustomAttributes(typeof(ParseStage), inherit: false);
					if (customAttributes.Length > 0)
					{
						ParseStage parseStage = (ParseStage)customAttributes[0];
						num = parseStage.Stage;
					}
					if (num == stage)
					{
						fieldInfo.SetValue(obj, ParseField(fieldInfo));
					}
				}
			}
		}

		public object ParseClass(Type t, string name)
		{
			if (t.IsClass)
			{
				Enter(name, t.FullName);
				object obj = t.Assembly.CreateInstance(t.FullName);
				GetCurrentStruct().Instance = obj;
				MethodInfo method = t.GetMethod("Parse", BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic);
				if (method != null)
				{
					object[] parameters = new object[1] { this };
					try
					{
						method.Invoke(obj, parameters);
					}
					catch (TargetInvocationException ex)
					{
						throw ex.InnerException;
					}
				}
				else
				{
					FieldInfo[] fields = t.GetFields(BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic);
					foreach (FieldInfo fieldInfo in fields)
					{
						if (fieldInfo.DeclaringType == t)
						{
							fieldInfo.SetValue(obj, ParseField(fieldInfo));
						}
					}
				}
				Leave();
				return obj;
			}
			return ParsePrimitive(t, name);
		}

		public T[] ParseTArray<T>(int count, string name)
		{
			return (T[])ParseClassArray(count, typeof(T), name);
		}

		public Array ParseClassArray(int count, Type t, string name)
		{
			Array array = Array.CreateInstance(t, count);
			Enter(name, t.FullName + "[" + count + "]");
			for (int i = 0; i < count; i++)
			{
				array.SetValue(ParseClass(t, "[" + i + "]"), i);
			}
			Leave();
			return array;
		}

		public ulong ParseULong(string name)
		{
			return ParseUInt64(name);
		}

		public long ParseLong(string name)
		{
			return ParseInt64(name);
		}

		public short ParseShort(string name)
		{
			return ParseInt16(name);
		}

		public ushort ParseUShort(string name)
		{
			return ParseUInt16(name);
		}

		public ulong ParseUInt64(string name)
		{
			ulong result = ReadUInt64();
			AddMarkup(name, "System.UInt64", result.ToString(), BaseStream.Position - 8, 8L);
			return result;
		}

		public uint ParseUInt32(string name)
		{
			uint result = ReadUInt32();
			AddMarkup(name, "System.UInt32", result.ToString(), BaseStream.Position - 4, 4L);
			return result;
		}

		public uint TryParseUInt32(string name, uint def)
		{
			uint result = def;
			try
			{
				result = ReadUInt32();
			}
			catch (Exception)
			{
			}
			AddMarkup(name, "System.UInt32", result.ToString(), BaseStream.Position - 4, 4L);
			return result;
		}

		public ushort ParseUInt16(string name)
		{
			ushort result = ReadUInt16();
			AddMarkup(name, "System.UInt16", result.ToString(), BaseStream.Position - 2, 2L);
			return result;
		}

		public long ParseInt64(string name)
		{
			long result = ReadInt64();
			AddMarkup(name, "System.Int64", result.ToString(), BaseStream.Position - 8, 8L);
			return result;
		}

		public int ParseInt32(string name)
		{
			int result = ReadInt32();
			AddMarkup(name, "System.Int32", result.ToString(), BaseStream.Position - 4, 4L);
			return result;
		}

		public short ParseInt16(string name)
		{
			short result = ReadInt16();
			AddMarkup(name, "System.Int16", result.ToString(), BaseStream.Position - 2, 2L);
			return result;
		}

		public byte ParseByte(string name)
		{
			byte result = ReadByte();
			AddMarkup(name, "System.Byte", result.ToString(), BaseStream.Position - 1, 1L);
			return result;
		}

		public byte[] ParseBytes(int count, string name)
		{
			Enter(name, "System.Byte[" + count + "]");
			byte[] result = ReadBytes(count);
			Leave();
			return result;
		}

		public string ParseASCIIString(int count, string name)
		{
			string text = Encoding.ASCII.GetString(ReadBytes(count));
			if (text.Length > 0 && text[text.Length - 1] == '\0')
			{
				text = text.Substring(0, text.Length - 1);
			}
			AddMarkup(name, "ASCII String", text, BaseStream.Position - count, count);
			return text;
		}

		public string ParseUTF16String(int count, string name)
		{
			StringBuilder stringBuilder = new StringBuilder();
			stringBuilder.Capacity = count;
			for (int i = 0; i < count; i++)
			{
				stringBuilder.Append((char)ReadUInt16());
			}
			string text = stringBuilder.ToString();
			AddMarkup(name, "UTF16 String", text, BaseStream.Position - count * 2, count * 2);
			return text;
		}

		public MarkupStruct GetCurrentField()
		{
			MarkupStruct currentStruct = GetCurrentStruct();
			if (currentStruct.GetFieldCount() > 0)
			{
				return currentStruct.GetLastField();
			}
			return currentStruct;
		}

		public MarkupStruct GetCurrentStruct()
		{
			return MarkupStack.Peek();
		}

		public MarkupStruct GetRootStruct()
		{
			MarkupStruct markupStruct = GetCurrentStruct();
			while (markupStruct.Parent != null)
			{
				markupStruct = markupStruct.Parent;
			}
			return markupStruct;
		}

		public MarkupStruct FindParentStruct(string name)
		{
			foreach (MarkupStruct item in MarkupStack)
			{
				if (item.Name.Equals(name, StringComparison.InvariantCultureIgnoreCase))
				{
					return item;
				}
			}
			return null;
		}

		public int GetMarkupDepth()
		{
			return MarkupStack.Count;
		}

		public void AddMarkup(string name, string type, string value, long offset, long size)
		{
			MarkupStruct markupStruct = new MarkupStruct();
			markupStruct.Name = name;
			markupStruct.Type = type;
			markupStruct.Value = value;
			markupStruct.Offset = offset;
			markupStruct.Size = size;
			AddMarkup(markupStruct);
		}

		private void AddMarkup(MarkupStruct mf)
		{
			mf.Parent = GetCurrentStruct();
			GetCurrentStruct().AddField(mf);
		}

		public MarkupStruct Enter(string name)
		{
			MarkupStruct markupStruct = new MarkupStruct();
			AddMarkup(markupStruct);
			markupStruct.Name = name;
			markupStruct.Offset = BaseStream.Position;
			MarkupStack.Push(markupStruct);
			return markupStruct;
		}

		public MarkupStruct Enter(string name, string type)
		{
			MarkupStruct markupStruct = Enter(name);
			markupStruct.Type = type;
			return markupStruct;
		}

		public void Leave()
		{
			MarkupStruct markupStruct = MarkupStack.Pop();
			markupStruct.Size = BaseStream.Position - markupStruct.Offset;
		}
	}
}
namespace OLESS
{
	public class StorageMode
	{
		public const uint STGM_SIMPLE = 134217728u;

		public const uint STGM_CREATE = 4096u;

		public const uint STGM_READ = 0u;

		public const uint STGM_READWRITE = 2u;

		public const uint STGM_SHARE_EXCLUSIVE = 16u;

		public const uint STGM_SHARE_DENY_WRITE = 32u;
	}
	public class OLE32
	{
		[ComImport]
		[Guid("0000000d-0000-0000-C000-000000000046")]
		[InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
		public interface IEnumSTATSTG
		{
			[PreserveSig]
			uint Next(uint celt, [Out][MarshalAs(UnmanagedType.LPArray)] System.Runtime.InteropServices.ComTypes.STATSTG[] rgelt, out uint pceltFetched);

			void Skip(uint celt);

			void Reset();

			[return: MarshalAs(UnmanagedType.Interface)]
			IEnumSTATSTG Clone();
		}

		[ComImport]
		[InterfaceType(ComInterfaceType.InterfaceIsIUnknown)]
		[Guid("0000000b-0000-0000-C000-000000000046")]
		public interface IStorage
		{
			uint CreateStream(string pwcsName, uint grfMode, uint reserved1, uint reserved2, out IStream ppstm);

			uint OpenStream(string pwcsName, IntPtr reserved1, uint grfMode, uint reserved2, out IStream ppstm);

			uint CreateStorage(string pwcsName, uint grfMode, uint reserved1, uint reserved2, out IStorage ppstg);

			uint OpenStorage(string pwcsName, IStorage pstgPriority, uint grfMode, IntPtr snbExclude, uint reserved, out IStorage ppstg);

			void CopyTo(uint ciidExclude, Guid rgiidExclude, IntPtr snbExclude, IStorage pstgDest);

			void MoveElementTo(string pwcsName, IStorage pstgDest, string pwcsNewName, uint grfFlags);

			void Commit(uint grfCommitFlags);

			void Revert();

			uint EnumElements(uint reserved1, IntPtr reserved2, uint reserved3, out IEnumSTATSTG ppenum);

			void DestroyElement(string pwcsName);

			void RenameElement(string pwcsOldName, string pwcsNewName);

			void SetElementTimes(string pwcsName, System.Runtime.InteropServices.ComTypes.FILETIME pctime, System.Runtime.InteropServices.ComTypes.FILETIME patime, System.Runtime.InteropServices.ComTypes.FILETIME pmtime);

			void SetClass(Guid clsid);

			void SetStateBits(uint grfStateBits, uint grfMask);

			void Stat(out System.Runtime.InteropServices.ComTypes.STATSTG pstatstg, uint grfStatFlag);
		}

		[DllImport("ole32.dll")]
		public static extern uint StgOpenStorage([MarshalAs(UnmanagedType.LPWStr)] string pwcsName, IStorage pstgPriority, uint grfMode, IntPtr snbExclude, uint reserved, out IStorage ppstgOpen);

		[DllImport("ole32.dll")]
		public static extern uint StgCreateDocfile([MarshalAs(UnmanagedType.LPWStr)] string pwcsName, uint grfMode, uint reserved, out IStorage ppstgOpen);
	}
	public enum StorageType
	{
		Storage = 1,
		Stream,
		LockBytes,
		Property,
		MultiStream
	}
	public class StorageStats
	{
		private System.Runtime.InteropServices.ComTypes.STATSTG Stats;

		public string Name => Stats.pwcsName;

		public long Size => Stats.cbSize;

		public StorageType Type => (StorageType)Stats.type;

		public StorageStats()
		{
		}

		public StorageStats(System.Runtime.InteropServices.ComTypes.STATSTG stats)
		{
			Stats = stats;
		}

		public StorageStats(IStream stream)
		{
			stream.Stat(out Stats, 1);
		}

		public StorageStats(OLE32.IStorage storage)
		{
			storage.Stat(out Stats, 1u);
		}
	}
	public class Stream
	{
		private IStream stream;

		public StorageStats Stats;

		public Stream()
		{
		}

		public Stream(IStream stream)
		{
			this.stream = stream;
			Stats = new StorageStats(stream);
		}

		~Stream()
		{
			Close();
		}

		public void Close()
		{
			if (stream != null)
			{
				Marshal.ReleaseComObject(stream);
				stream = null;
			}
		}

		public unsafe byte[] Read()
		{
			MemoryStream memoryStream = new MemoryStream();
			long num = Stats.Size;
			int num2 = 0;
			IntPtr pcbRead = new IntPtr(&num2);
			while (memoryStream.Length < Stats.Size && num > 0)
			{
				try
				{
					byte[] array = new byte[num];
					stream.Read(array, array.Length, pcbRead);
					memoryStream.Write(array, 0, num2);
					if (num2 == 0)
					{
						break;
					}
				}
				catch (Exception)
				{
					num /= 2;
				}
			}
			return memoryStream.ToArray();
		}

		public unsafe void Write(byte[] data)
		{
			int num = 0;
			IntPtr pcbWritten = new IntPtr(&num);
			stream.Write(data, data.Length, pcbWritten);
			if (num != data.Length)
			{
				throw new Exception("Write error: attempted to write " + data.Length + " bytes, wrote " + num + " bytes");
			}
		}
	}
	public class Storage
	{
		public OLE32.IStorage storage;

		private List<StorageStats> Items;

		public StorageStats Stats;

		public Storage()
		{
		}

		public Storage(OLE32.IStorage storage)
		{
			this.storage = storage;
			Stats = new StorageStats(storage);
		}

		~Storage()
		{
			Close();
		}

		public void Close()
		{
			if (storage != null)
			{
				Marshal.ReleaseComObject(storage);
				storage = null;
			}
		}

		public Storage CreateStorage(string filename)
		{
			if (storage.CreateStorage(filename, 18u, 0u, 0u, out var ppstg) != 0)
			{
				throw new Exception("CreateStorage error");
			}
			return new Storage(ppstg);
		}

		public void OpenFile(string filename)
		{
			if (OLE32.StgOpenStorage(filename, null, 32u, IntPtr.Zero, 0u, out storage) != 0)
			{
				throw new Exception("StgOpenStorage error");
			}
		}

		public void CreateFile(string filename)
		{
			if (OLE32.StgCreateDocfile(filename, 4114u, 0u, out storage) != 0)
			{
				throw new Exception("StgCreateDocfile error");
			}
		}

		public Storage OpenStorage(string filename)
		{
			if (storage.OpenStorage(filename, null, 16u, IntPtr.Zero, 0u, out var ppstg) != 0)
			{
				throw new Exception("OpenStorage error");
			}
			return new Storage(ppstg);
		}

		public Stream OpenStream(string filename)
		{
			if (storage.OpenStream(filename, IntPtr.Zero, 16u, 0u, out var ppstm) != 0)
			{
				throw new Exception("OpenStream error");
			}
			return new Stream(ppstm);
		}

		public Stream CreateStream(string filename)
		{
			if (storage.CreateStream(filename, 18u, 0u, 0u, out var ppstm) != 0)
			{
				throw new Exception("CreateStream error");
			}
			return new Stream(ppstm);
		}

		public List<StorageStats> GetItems()
		{
			if (Items == null)
			{
				Items = new List<StorageStats>();
				if (storage.EnumElements(0u, IntPtr.Zero, 0u, out var ppenum) != 0)
				{
					throw new Exception("EnumElements error");
				}
				ppenum.Reset();
				uint pceltFetched = 0u;
				System.Runtime.InteropServices.ComTypes.STATSTG[] array = new System.Runtime.InteropServices.ComTypes.STATSTG[1];
				uint num = ppenum.Next(1u, array, out pceltFetched);
				while (num == 0 && pceltFetched != 0)
				{
					Items.Add(new StorageStats(array[0]));
					ppenum.Next(1u, array, out pceltFetched);
				}
			}
			return Items;
		}
	}
	public class SectorType
	{
		public const uint FREESECT = uint.MaxValue;

		public const uint ENDOFCHAIN = 4294967294u;

		public const uint FATSECT = 4294967293u;

		public const uint DIFSECT = 4294967292u;

		public static bool IsRegularSector(long sector)
		{
			if (sector != uint.MaxValue && sector != 4294967294u && sector != 4294967293u)
			{
				return sector != 4294967292u;
			}
			return false;
		}

		public static string SectorToString(long sector)
		{
			long num = sector;
			if (num <= uint.MaxValue && num >= 4294967292u)
			{
				switch (num - 4294967292u)
				{
				case 3L:
					return "FREESECT";
				case 2L:
					return "ENDOFCHAIN";
				case 1L:
					return "FATSECT";
				case 0L:
					return "DIFSECT";
				}
			}
			return sector.ToString();
		}
	}
	public class OLEGUID
	{
		public uint dw1;

		public ushort w1;

		public ushort w2;

		private byte[] aby;

		public OLEGUID()
		{
			dw1 = 0u;
			w1 = 0;
			w2 = 0;
			byte[] array = new byte[8];
			aby = array;
		}

		public void Read(Parser p)
		{
			p.Enter("GUID");
			dw1 = p.ParseUInt32("dword");
			w1 = p.ParseUInt16("word");
			w2 = p.ParseUInt16("word");
			aby = p.ParseBytes(8, "bytes");
			p.Leave();
		}

		public void Write(BinaryWriter w)
		{
			w.Write(dw1);
			w.Write(w1);
			w.Write(w2);
			w.Write(aby);
		}

		public bool IsNull()
		{
			if (dw1 != 0 || w1 != 0 || w2 != 0)
			{
				return false;
			}
			if (aby.Length != 8)
			{
				return false;
			}
			byte[] array = aby;
			for (int i = 0; i < array.Length; i++)
			{
				if (array[i] != 0)
				{
					return false;
				}
			}
			return true;
		}
	}
	internal class OLESSSignatures
	{
		public static int SignatureLength = 8;

		public static byte[] sig = new byte[8] { 208, 207, 17, 224, 161, 177, 26, 225 };

		public static bool IsOLESSSignature(byte[] signature)
		{
			if (signature.Length != SignatureLength)
			{
				return false;
			}
			for (int i = 0; i < sig.Length; i++)
			{
				if (signature[i] != sig[i])
				{
					return false;
				}
			}
			return true;
		}
	}
	internal enum OLESSMessages
	{
		GUIDIsNotNull = 1000,
		InvalidDifatAfterEnded = 1001,
		FatSectorOOBHint = 1002,
		FileTruncated = 1003,
		InvalidMiniFatSectorOOBHint = 1004,
		InvalidHeaderSignature = 10000,
		UnsupportedByteOrder = 10001,
		InvalidDirectoryFirstSector = 10002,
		InvalidMiniFatFirstSector = 10003,
		InvalidMiniFatSectorOOB = 10004,
		InvalidDiFatFirstSector = 10005,
		InvalidDiFatSectorOOB = 10006,
		NameLengthTooLarge = 10007,
		DiFatLoop = 10008,
		DiFatTooManySectors = 10009,
		FatSectorOOB = 10010,
		FatTooSmall = 10011,
		InvalidFinalSector = 10012,
		OverlappingElementData = 10013,
		ElementInvalidStartSector = 10014,
		ElementLoopInTree = 10015,
		ElementInvalidChild = 10016,
		ElementOverflowsSectorChain = 10017,
		ElementOverflowsMiniSectorChain = 10018,
		LoopInSectorChain = 10019,
		ChainTooManySectors = 10020,
		ChainInvalidSectorOOB = 10021,
		ReadingInvalidSector = 10022,
		MiniChainLoop = 10023,
		MiniChainTooManySectors = 10024,
		MiniChainInvalidSectorOOB = 10025,
		ReadingInvalidMiniSector = 10026
	}
	public class FileHeader
	{
		public const int SIZE = 512;

		public byte[] abySig;

		public OLEGUID clsidNull = new OLEGUID();

		public ushort wVerMinor;

		public ushort wVerDll;

		public ushort wByteOrder;

		public ushort wSectorShift;

		public ushort wMiniSecShift;

		public ushort wReserved;

		public uint dwReserved;

		public uint dwNumDirSects;

		public uint dwNumFatSects;

		public uint dwDirSect1;

		public uint stTransactSig;

		public uint dwMiniStrMax;

		public uint dwMiniFatSect1;

		public uint dwNumMiniFatSects;

		public uint dwDifatSect1;

		public uint dwNumDifatSects;

		public uint[] dwDiFat = new uint[109];

		public long FileSize;

		public long GetSectorPosition(long sector)
		{
			return (sector + 1) * GetSectorSize();
		}

		public long GetMiniSectorPosition(long sector)
		{
			return sector * GetMiniSectorSize();
		}

		public int GetSectorSize()
		{
			return 1 << (int)wSectorShift;
		}

		public int GetMiniSectorSize()
		{
			return 1 << (int)wMiniSecShift;
		}

		public long GetMiniFatCutoffSize()
		{
			return dwMiniStrMax;
		}

		public void Read(Parser p)
		{
			FileSize = p.Length;
			abySig = p.ParseBytes(OLESSSignatures.SignatureLength, "Signature");
			if (!OLESSSignatures.IsOLESSSignature(abySig))
			{
				p.GetCurrentField().AddWarning(10000, "Invalid header signature");
			}
			clsidNull.Read(p);
			if (!clsidNull.IsNull())
			{
				p.GetCurrentField().AddHint(1000, "GUID is not null");
			}
			wVerMinor = p.ParseUInt16("Version Minor");
			wVerDll = p.ParseUInt16("Version DLL");
			wByteOrder = p.ParseUInt16("Byte Order");
			if (wByteOrder != 65534)
			{
				p.GetCurrentField().AddWarning(10001, "Unsupported byte order");
			}
			wSectorShift = p.ParseUInt16("Sector Shift");
			wMiniSecShift = p.ParseUInt16("Mini Sector Shift");
			wReserved = p.ParseUInt16("Reserved");
			dwReserved = p.ParseUInt32("Reserved");
			dwNumDirSects = p.ParseUInt32("Num Dir Sects");
			dwNumFatSects = p.ParseUInt32("Num Fat Sects");
			dwDirSect1 = p.ParseUInt32("Dir Sect 1");
			if (SectorType.IsRegularSector(dwDirSect1) && GetSectorPosition(dwDirSect1) + GetSectorSize() > FileSize)
			{
				p.GetCurrentField().AddWarning(10002, "Invalid Directory: first sector out of bounds");
			}
			stTransactSig = p.ParseUInt32("TransactSig");
			dwMiniStrMax = p.ParseUInt32("Mini Str Max");
			dwMiniFatSect1 = p.ParseUInt32("Mini Fat Sect 1");
			if (SectorType.IsRegularSector(dwMiniFatSect1) && GetSectorPosition(dwMiniFatSect1) + GetSectorSize() > FileSize)
			{
				p.GetCurrentField().AddWarning(10003, "Invalid MiniFat: first sector out of bounds");
			}
			dwNumMiniFatSects = p.ParseUInt32("Num Mini Fat Sects");
			dwDifatSect1 = p.ParseUInt32("Difat Sect 1");
			if (SectorType.IsRegularSector(dwDifatSect1) && GetSectorPosition(dwDifatSect1) + GetSectorSize() > FileSize)
			{
				p.GetCurrentField().AddWarning(10005, "Invalid DiFat: first sector out of bounds");
			}
			dwNumDifatSects = p.ParseUInt32("Num Difat Sects");
			p.Enter("Difat", "System.UInt32[109]");
			bool flag = false;
			for (int i = 0; i < 109; i++)
			{
				dwDiFat[i] = p.ParseUInt32("[" + i + "]");
				if (SectorType.IsRegularSector(dwDiFat[i]))
				{
					if (GetSectorPosition(dwDiFat[i]) + GetSectorSize() > FileSize)
					{
						if (flag)
						{
							p.GetCurrentField().AddHint(1001, "Invalid DiFat Entry after DiFat ended");
						}
						else
						{
							p.GetCurrentField().AddWarning(10006, "Invalid DiFat Entry: sector out of bounds");
						}
					}
				}
				else
				{
					flag = true;
				}
			}
			p.Leave();
		}

		public void Write(BinaryWriter w)
		{
			w.Write(abySig);
			clsidNull.Write(w);
			w.Write(wVerMinor);
			w.Write(wVerDll);
			w.Write(wByteOrder);
			w.Write(wSectorShift);
			w.Write(wMiniSecShift);
			w.Write(wReserved);
			w.Write(dwReserved);
			w.Write(dwNumDirSects);
			w.Write(dwNumFatSects);
			w.Write(dwDirSect1);
			w.Write(stTransactSig);
			w.Write(dwMiniStrMax);
			w.Write(dwMiniFatSect1);
			w.Write(dwNumMiniFatSects);
			w.Write(dwDifatSect1);
			w.Write(dwNumDifatSects);
			for (int i = 0; i < 109; i++)
			{
				w.Write(dwDiFat[i]);
			}
		}
	}
	public enum ElementType
	{
		Invalid,
		Storage,
		Stream,
		LockBytes,
		Property,
		MultiStream
	}
	public class Element
	{
		public const int SIZE = 128;

		public const int NAMEMAXLEN = 64;

		public byte[] Name;

		public ushort NameLength;

		public byte Type;

		public byte Flags;

		public uint sidLeft;

		public uint sidRight;

		public uint sidChild;

		public OLEGUID ClsID = new OLEGUID();

		public uint dwUserFlags;

		public ulong tCreateTime;

		public ulong tModifyTime;

		public uint dwStartSect;

		public uint dwSizeLow;

		public uint dwSizeHigh;

		public Element Parent;

		public List<Element> Elements = new List<Element>();

		public EmbeddedStream Data = new EmbeddedStream();

		public MarkupStruct Markup;

		public long GetDataLength()
		{
			if (Data.Stream != null)
			{
				return Data.Stream.Length;
			}
			return 0L;
		}

		public ElementType GetElementType()
		{
			return (ElementType)Type;
		}

		public bool IsValidElement()
		{
			return Type != 0;
		}

		public bool IsDataElement()
		{
			if (Type != 2)
			{
				return Type == 5;
			}
			return true;
		}

		public long GetSize()
		{
			if ((dwSizeLow & 0x80000000u) != 0)
			{
				return 0L;
			}
			return dwSizeLow;
		}

		public string GetName()
		{
			int num = NameLength - 2;
			if (num > 64)
			{
				num = 64;
			}
			if (num <= 0)
			{
				return "";
			}
			return Encoding.Unicode.GetString(Name, 0, num);
		}

		public string GetPath()
		{
			if (Parent != null)
			{
				return Parent.GetPath() + "\\" + Parent.GetName();
			}
			return "";
		}

		public void Write(BinaryWriter w)
		{
			w.Write(Name);
			w.Write(NameLength);
			w.Write(Type);
			w.Write(Flags);
			w.Write(sidLeft);
			w.Write(sidRight);
			w.Write(sidChild);
			ClsID.Write(w);
			w.Write(dwUserFlags);
			w.Write(tCreateTime);
			w.Write(tModifyTime);
			w.Write(dwStartSect);
			w.Write(dwSizeLow);
			w.Write(dwSizeHigh);
		}

		public void Read(Parser p)
		{
			Name = p.ParseBytes(64, "Name");
			MarkupStruct currentField = p.GetCurrentField();
			NameLength = p.ParseUInt16("Name Length");
			if (NameLength > 66)
			{
				p.GetCurrentField().AddWarning(10007, "Name Length too large: {0}, Max length is 66", new object[1] { NameLength });
			}
			currentField.Value = GetName();
			Type = p.ParseByte("Type");
			Flags = p.ParseByte("Flags");
			sidLeft = p.ParseUInt32("Left");
			sidRight = p.ParseUInt32("Right");
			sidChild = p.ParseUInt32("Child");
			ClsID.Read(p);
			dwUserFlags = p.ParseUInt32("User Flags");
			tCreateTime = p.ParseUInt64("Create Time");
			tModifyTime = p.ParseUInt64("Modify Time");
			dwStartSect = p.ParseUInt32("Start Sector");
			dwSizeLow = p.ParseUInt32("Size Low");
			dwSizeHigh = p.ParseUInt32("Size High");
		}
	}
	public class File
	{
		public class FatEntry
		{
			public FatType Type;

			public Element Element;

			public int Index;

			public long Next;

			public string Identify()
			{
				return Type switch
				{
					FatType.Element => "Element:" + Element.GetName(), 
					FatType.Dir => "Directory", 
					FatType.Fat => "Fat", 
					FatType.MiniFat => "MiniFat", 
					FatType.Unallocated => "Unallocated", 
					FatType.Allocated => "Allocated", 
					_ => "Unknown", 
				};
			}
		}

		public enum FatType
		{
			Unallocated,
			Allocated,
			Element,
			Fat,
			MiniFat,
			Dir
		}

		public Parser Parser;

		public FileHeader Header = new FileHeader();

		public List<Element> Elements = new List<Element>();

		public Element RootElement;

		public List<long> Fat = new List<long>();

		public List<long> MiniFat = new List<long>();

		public List<long> MiniFatSectors;

		public List<long> FatSectors;

		public List<long> DiFatSectors;

		private List<FatEntry> FatAllocations;

		private List<FatEntry> MiniFatAllocations;

		public int GetSectorsInFile()
		{
			return (int)((Header.FileSize - 512 + Header.GetSectorSize() - 1) / Header.GetSectorSize());
		}

		public int GetMiniSectorsInFile()
		{
			if (RootElement != null)
			{
				return (int)((RootElement.GetDataLength() + Header.GetMiniSectorSize() - 1) / Header.GetMiniSectorSize());
			}
			return 0;
		}

		public float GetFragmentationPercent()
		{
			int fragmentedSectorCount = GetFragmentedSectorCount();
			int contiguousSectorCount = GetContiguousSectorCount();
			return (float)fragmentedSectorCount / (float)(fragmentedSectorCount + contiguousSectorCount);
		}

		public int GetContiguousSectorCount()
		{
			int num = 0;
			long num2 = -1L;
			foreach (long item in Fat)
			{
				int num3 = (int)item;
				if (SectorType.IsRegularSector(num3) && SectorType.IsRegularSector(num2) && num2 + 1 == num3)
				{
					num++;
				}
				num2 = num3;
			}
			return num;
		}

		public int GetFragmentedSectorCount()
		{
			int num = 0;
			long num2 = -1L;
			foreach (long item in Fat)
			{
				if (SectorType.IsRegularSector(item) && SectorType.IsRegularSector(num2) && num2 + 1 != item)
				{
					num++;
				}
				num2 = item;
			}
			return num;
		}

		public int GetAllocatedSectorCount()
		{
			int num = 0;
			foreach (long item in Fat)
			{
				if (item != uint.MaxValue)
				{
					num++;
				}
			}
			return num;
		}

		public long GetUnallocatedSectorCount()
		{
			int num = 0;
			foreach (long item in Fat)
			{
				if (item == uint.MaxValue)
				{
					num++;
				}
			}
			return num;
		}

		public int GetSectorCount()
		{
			return Fat.Count;
		}

		public int GetFatSectorCount()
		{
			return FatSectors.Count;
		}

		public List<long> ReadDiFatChainIndices(Parser p)
		{
			List<long> list = new List<long>();
			long num = Header.dwDifatSect1;
			while (SectorType.IsRegularSector(num))
			{
				list.Add(num);
				p.Position = Header.GetSectorPosition(num) + Header.GetSectorSize() - 4;
				num = p.TryParseUInt32("Sector", uint.MaxValue);
				if (list.Contains(num))
				{
					p.GetCurrentField().AddWarning(10008, "Loop in DiFat sector chain");
					break;
				}
				if (list.Count >= GetSectorsInFile())
				{
					p.GetCurrentField().AddWarning(10009, "Invalid DiFat sector chain: too many sectors");
					break;
				}
				if (SectorType.IsRegularSector(num) && num >= GetSectorsInFile())
				{
					p.GetCurrentField().AddWarning(10006, "DiFat sector out of bounds: sector: {0} sectors in file: {1}", new object[2]
					{
						num,
						GetSectorsInFile()
					});
				}
			}
			return list;
		}

		public void ReadFatSectors(Parser p)
		{
			FatSectors = new List<long>();
			for (int i = 0; i < 109 && Header.dwDiFat[i] != uint.MaxValue; i++)
			{
				FatSectors.Add(Header.dwDiFat[i]);
			}
			p.Enter("Difat Sectors");
			DiFatSectors = ReadDiFatChainIndices(p);
			p.Leave();
			p.Enter("Sectors");
			foreach (long diFatSector in DiFatSectors)
			{
				p.Position = Header.GetSectorPosition(diFatSector);
				for (int num = Header.GetSectorSize() / 4; num > 1; num--)
				{
					long num2 = p.TryParseUInt32("Sector", uint.MaxValue);
					if (!SectorType.IsRegularSector(num2))
					{
						break;
					}
					FatSectors.Add(num2);
					if (num2 >= GetSectorsInFile())
					{
						p.GetCurrentField().AddWarning(10010, "Fat sector out of bounds: sector: {0} sectors in file: {1}", new object[2]
						{
							num2,
							GetSectorsInFile()
						});
					}
				}
			}
			p.Leave();
		}

		public void ReadHeader(Parser r)
		{
			r.Enter("Header");
			Header.Read(r);
			r.Leave();
		}

		public void WriteFatSectors(BinaryWriter w)
		{
			int num = 109;
			for (int i = 0; i < DiFatSectors.Count; i++)
			{
				w.BaseStream.Position = Header.GetSectorPosition(DiFatSectors[i]);
				for (int num2 = Header.GetSectorSize() / 4; num2 > 1; num2--)
				{
					if (num < FatSectors.Count)
					{
						w.Write((uint)FatSectors[num++]);
					}
					else
					{
						w.Write(uint.MaxValue);
					}
				}
				if (i < DiFatSectors.Count - 1)
				{
					w.Write((uint)DiFatSectors[i + 1]);
				}
				else
				{
					w.Write(uint.MaxValue);
				}
			}
		}

		public void WriteFat(BinaryWriter w)
		{
			WriteFatSectors(w);
			int num = 0;
			foreach (long fatSector in FatSectors)
			{
				if (fatSector >= GetSectorsInFile())
				{
					continue;
				}
				w.BaseStream.Position = Header.GetSectorPosition(fatSector);
				for (int num2 = Header.GetSectorSize() / 4; num2 > 0; num2--)
				{
					if (num < Fat.Count)
					{
						w.Write((uint)Fat[num++]);
					}
					else
					{
						w.Write(uint.MaxValue);
					}
				}
			}
		}

		public void ReadFat(Parser p)
		{
			p.Enter("Fat");
			p.Enter("Difat");
			ReadFatSectors(p);
			p.Leave();
			foreach (long fatSector in FatSectors)
			{
				if (fatSector < GetSectorsInFile())
				{
					p.Position = Header.GetSectorPosition(fatSector);
					for (int num = Header.GetSectorSize() / 4; num > 0; num--)
					{
						long num2 = p.TryParseUInt32("Sector", uint.MaxValue);
						Fat.Add(num2);
						if (SectorType.IsRegularSector(num2) && num2 >= GetSectorsInFile())
						{
							p.GetCurrentField().AddHint(1002, "Invalid data in fat: sector: {0} is out of bounds.  Sectors in file: {1}", new object[2]
							{
								num2,
								GetSectorsInFile()
							});
						}
					}
				}
				else
				{
					for (int num3 = Header.GetSectorSize() / 4; num3 > 0; num3--)
					{
						Fat.Add(4294967295L);
					}
				}
			}
			if (Fat.Count < GetSectorsInFile())
			{
				p.GetCurrentStruct().AddWarning(10011, "Fewer fat entries than sectors in file");
				while (Fat.Count < GetSectorsInFile())
				{
					Fat.Add(4294967295L);
				}
			}
			p.Leave();
		}

		public void Read(Parser p)
		{
			ReadHeader(p);
			ReadFat(p);
			ReadMiniFat(p);
			ReadElements(p);
			int num = (int)(p.Length % 512);
			if (num == 0)
			{
				return;
			}
			p.GetCurrentStruct().AddHint(1003, "File appears to be truncated");
			if (GetSectorsInFile() > 0)
			{
				GetFatAllocations();
				FatEntry fatEntry = FatAllocations[GetSectorsInFile() - 1];
				if (fatEntry.Element != null && (fatEntry.Next != 4294967294u || num != fatEntry.Element.GetSize() % 512))
				{
					p.GetCurrentStruct().AddWarning(10012, "Invalid final sector (allocated to '{0}' and remainders do not match", new object[1] { fatEntry.Element.GetName() });
				}
			}
		}

		public void DefragFatChain(List<long> newFat, long startSector)
		{
			while (SectorType.IsRegularSector(startSector) && startSector < Fat.Count)
			{
				newFat.Add(startSector);
				startSector = Fat[SectorToInt(startSector)];
			}
		}

		public void DefragFat(bool randomize, bool validate)
		{
			FatAllocations = null;
			List<Element> list = new List<Element>();
			list.AddRange(Elements);
			Random random = new Random();
			if (randomize)
			{
				for (int i = 2; i < list.Count; i++)
				{
					int index = random.Next(1, i);
					Element value = list[i];
					list[i] = list[index];
					list[index] = value;
				}
			}
			List<long> list2 = new List<long>();
			foreach (long diFatSector in DiFatSectors)
			{
				if (SectorType.IsRegularSector(diFatSector))
				{
					list2.Add(diFatSector);
				}
			}
			foreach (long fatSector in FatSectors)
			{
				if (SectorType.IsRegularSector(fatSector))
				{
					list2.Add(fatSector);
				}
			}
			DefragFatChain(list2, Header.dwDirSect1);
			DefragFatChain(list2, Header.dwMiniFatSect1);
			foreach (Element item in list)
			{
				if (item.IsDataElement() && !IsInMiniStream(item))
				{
					DefragFatChain(list2, item.dwStartSect);
				}
			}
			AdjustFat(list2, validate);
		}

		public void DefragMiniFatChain(List<long> newFat, long startSector)
		{
			while (SectorType.IsRegularSector(startSector) && startSector < MiniFat.Count)
			{
				newFat.Add(startSector);
				startSector = MiniFat[SectorToInt(startSector)];
			}
		}

		public void DefragMiniFat(bool randomize, bool validate)
		{
			MiniFatAllocations = null;
			List<Element> list = new List<Element>();
			list.AddRange(Elements);
			Random random = new Random();
			if (randomize)
			{
				for (int i = 2; i < list.Count; i++)
				{
					int index = random.Next(1, i);
					Element value = list[i];
					list[i] = list[index];
					list[index] = value;
				}
			}
			List<long> newFat = new List<long>();
			foreach (Element item in list)
			{
				if (item.IsDataElement() && IsInMiniStream(item))
				{
					DefragMiniFatChain(newFat, item.dwStartSect);
				}
			}
			AdjustMiniFat(newFat, validate);
		}

		public void Defrag(bool randomize, bool validate)
		{
			DefragMiniFat(randomize, validate);
			DefragFat(randomize, validate);
		}

		public uint GetAdjustedSector(long sector, Dictionary<long, uint> sectorMap)
		{
			if (SectorType.IsRegularSector(sector) && sectorMap.ContainsKey(sector))
			{
				return sectorMap[sector];
			}
			return (uint)sector;
		}

		public void ValidateAdjustedFatChain(long sector, List<long> newFat, Dictionary<long, uint> sectorMap)
		{
			long num = newFat.IndexOf(sector);
			while (SectorType.IsRegularSector(sector))
			{
				if (num < 0)
				{
					throw new Exception("Fat adjustment failed");
				}
				sector = Fat[SectorToInt(sector)];
				num = GetAdjustedSector(Fat[SectorToInt(newFat[SectorToInt(num)])], sectorMap);
			}
			if (num != sector)
			{
				throw new Exception("Fat adjustment failed");
			}
		}

		public bool IsFatValid()
		{
			foreach (long item in Fat)
			{
				if (SectorType.IsRegularSector(item) && item >= Fat.Count)
				{
					return false;
				}
			}
			return true;
		}

		public bool IsMiniFatValid()
		{
			foreach (long item in MiniFat)
			{
				if (SectorType.IsRegularSector(item) && item >= MiniFat.Count)
				{
					return false;
				}
			}
			foreach (Element element in Elements)
			{
				Dictionary<long, bool> dictionary = new Dictionary<long, bool>();
				if (!element.IsDataElement() || !IsInMiniStream(element))
				{
					continue;
				}
				List<long> list = ReadSectorChainIndices(element);
				foreach (long item2 in list)
				{
					if (dictionary.ContainsKey(item2))
					{
						return false;
					}
					dictionary.Add(item2, value: true);
				}
			}
			return true;
		}

		public void AdjustFat(List<long> newFat, bool validate)
		{
			if (validate && !IsFatValid())
			{
				throw new Exception("Invalid fat going in");
			}
			Dictionary<long, uint> dictionary = new Dictionary<long, uint>();
			for (uint num = 0u; num < newFat.Count; num++)
			{
				if (!dictionary.ContainsKey(newFat[(int)num]))
				{
					dictionary.Add(newFat[(int)num], num);
				}
			}
			foreach (Element element in Elements)
			{
				if (element.IsDataElement() && !IsInMiniStream(element))
				{
					if (validate)
					{
						ValidateAdjustedFatChain(element.dwStartSect, newFat, dictionary);
					}
					element.dwStartSect = GetAdjustedSector(element.dwStartSect, dictionary);
				}
			}
			Header.dwDifatSect1 = GetAdjustedSector(Header.dwDifatSect1, dictionary);
			Header.dwDirSect1 = GetAdjustedSector(Header.dwDirSect1, dictionary);
			Header.dwMiniFatSect1 = GetAdjustedSector(Header.dwMiniFatSect1, dictionary);
			for (int i = 0; i < 109; i++)
			{
				Header.dwDiFat[i] = GetAdjustedSector(Header.dwDiFat[i], dictionary);
			}
			for (int j = 0; j < DiFatSectors.Count; j++)
			{
				DiFatSectors[j] = GetAdjustedSector(DiFatSectors[j], dictionary);
			}
			for (int k = 0; k < FatSectors.Count; k++)
			{
				FatSectors[k] = GetAdjustedSector(FatSectors[k], dictionary);
			}
			for (int l = 0; l < MiniFatSectors.Count; l++)
			{
				MiniFatSectors[l] = GetAdjustedSector(MiniFatSectors[l], dictionary);
			}
			List<long> list = new List<long>();
			list.AddRange(Fat);
			for (int m = 0; m < Fat.Count; m++)
			{
				if (m < newFat.Count && newFat[m] < list.Count)
				{
					Fat[m] = GetAdjustedSector(list[SectorToInt(newFat[m])], dictionary);
				}
				else
				{
					Fat[m] = 4294967295L;
				}
			}
			if (!validate)
			{
				return;
			}
			foreach (Element element2 in Elements)
			{
				if (element2.IsDataElement() && !IsInMiniStream(element2) && ReadSectorChainIndices(element2.dwStartSect).Count * Header.GetSectorSize() < element2.GetSize())
				{
					throw new Exception("Adjust fat size mismatch");
				}
			}
			if (!IsFatValid())
			{
				throw new Exception("Invalid fat going out");
			}
		}

		public void AdjustMiniFat(List<long> newFat, bool validate)
		{
			if (validate && !IsMiniFatValid())
			{
				throw new Exception("Invalid minifat going in");
			}
			Dictionary<long, uint> dictionary = new Dictionary<long, uint>();
			for (uint num = 0u; num < newFat.Count; num++)
			{
				if (!dictionary.ContainsKey(newFat[(int)num]))
				{
					dictionary.Add(newFat[(int)num], num);
				}
			}
			foreach (Element element in Elements)
			{
				if (element.IsDataElement() && IsInMiniStream(element))
				{
					element.dwStartSect = GetAdjustedSector(element.dwStartSect, dictionary);
				}
			}
			List<long> list = new List<long>();
			list.AddRange(MiniFat);
			for (int i = 0; i < MiniFat.Count; i++)
			{
				if (i < newFat.Count && newFat[i] < list.Count)
				{
					MiniFat[i] = GetAdjustedSector(list[SectorToInt(newFat[i])], dictionary);
				}
				else
				{
					MiniFat[i] = 4294967295L;
				}
			}
			if (validate && !IsMiniFatValid())
			{
				throw new Exception("Invalid minifat going out");
			}
		}

		public void FragMiniFat(bool validate)
		{
			MiniFatAllocations = null;
			Random random = new Random();
			List<long> list = new List<long>();
			int num = 0;
			for (int i = 0; i < MiniFat.Count; i++)
			{
				if (SectorType.IsRegularSector(MiniFat[i]) && MiniFat[i] > num)
				{
					num = SectorToInt(MiniFat[i]);
				}
			}
			for (int j = 0; j <= num; j++)
			{
				list.Add(j);
			}
			for (int k = 1; k < list.Count; k++)
			{
				int index = random.Next(k);
				long value = list[k];
				list[k] = list[index];
				list[index] = value;
			}
			AdjustMiniFat(list, validate);
		}

		public void FragFat(bool validate)
		{
			FatAllocations = null;
			Random random = new Random();
			List<long> list = new List<long>();
			for (int i = 0; i < Fat.Count; i++)
			{
				list.Add(i);
			}
			for (int j = 1; j < list.Count; j++)
			{
				int index = random.Next(j);
				long value = list[j];
				list[j] = list[index];
				list[index] = value;
			}
			AdjustFat(list, validate);
		}

		public void Frag(bool validate)
		{
			FragMiniFat(validate);
			FragFat(validate);
		}

		public uint AllocateFat(long length, List<long> chain)
		{
			if (length <= 0)
			{
				return 4294967294u;
			}
			uint count = (uint)Fat.Count;
			while (length > 0)
			{
				length -= Header.GetSectorSize();
				chain?.Add(Fat.Count);
				if (length > 0)
				{
					Fat.Add(Fat.Count + 1);
				}
				else
				{
					Fat.Add(4294967294L);
				}
			}
			return count;
		}

		public uint AllocateMiniFat(long length, List<long> chain)
		{
			if (length <= 0)
			{
				return 4294967294u;
			}
			uint count = (uint)MiniFat.Count;
			while (length > 0)
			{
				length -= Header.GetMiniSectorSize();
				chain?.Add(MiniFat.Count);
				if (length > 0)
				{
					MiniFat.Add(MiniFat.Count + 1);
				}
				else
				{
					MiniFat.Add(4294967294L);
				}
			}
			return count;
		}

		public long GetSectorCount(long len)
		{
			return (len + Header.GetSectorSize() - 1) / Header.GetSectorSize();
		}

		public long GetMiniSectorCount(long len)
		{
			return (len + Header.GetMiniSectorSize() - 1) / Header.GetMiniSectorSize();
		}

		public void Reallocate(bool randomize, bool pad)
		{
			for (int i = 0; i < 109; i++)
			{
				Header.dwDiFat[i] = uint.MaxValue;
			}
			DiFatSectors.Clear();
			FatSectors.Clear();
			Fat.Clear();
			FatAllocations = null;
			MiniFatSectors.Clear();
			MiniFat.Clear();
			MiniFatAllocations = null;
			List<Element> list = new List<Element>();
			list.AddRange(Elements);
			Random random = new Random();
			if (pad)
			{
				for (int num = random.Next(1, 4); num > 0; num--)
				{
					list.Add(null);
				}
			}
			if (randomize)
			{
				for (int j = 2; j < list.Count; j++)
				{
					int index = random.Next(1, j);
					Element value = list[j];
					list[j] = list[index];
					list[index] = value;
				}
			}
			long num2 = 0L;
			foreach (Element item in list)
			{
				if (item != null && item.IsDataElement())
				{
					item.dwSizeLow = (uint)item.GetDataLength();
				}
			}
			foreach (Element item2 in list)
			{
				if (item2 != null && item2.IsDataElement() && IsInMiniStream(item2))
				{
					item2.dwStartSect = AllocateMiniFat(item2.GetDataLength(), null);
					num2 += GetMiniSectorCount(item2.GetDataLength()) * Header.GetMiniSectorSize();
				}
			}
			long num3 = 0L;
			RootElement.Data.Stream.SetLength(num2);
			RootElement.dwSizeLow = (uint)RootElement.GetDataLength();
			foreach (Element item3 in list)
			{
				if (item3 == null)
				{
					AllocateFat(1L, null);
				}
				else if (item3.IsDataElement() && !IsInMiniStream(item3))
				{
					item3.dwStartSect = AllocateFat(item3.Data.Stream.Length, null);
					num3 += GetSectorCount(item3.Data.Stream.Length) * Header.GetSectorSize();
				}
			}
			Header.dwDirSect1 = AllocateFat(Elements.Count * 128, null);
			Header.dwNumDirSects = 0u;
			Header.dwMiniFatSect1 = AllocateFat(MiniFat.Count * 4, MiniFatSectors);
			Header.dwNumMiniFatSects = (uint)MiniFatSectors.Count;
			long num4 = 0L;
			long num5 = 0L;
			long num6 = 0L;
			do
			{
				num6 = num5;
				num4 = GetSectorCount(Fat.Count * 4 + num5 * Header.GetSectorSize());
				long num7 = Header.GetSectorSize() / 4 - 1;
				num5 = ((num4 <= 109) ? 0 : ((num4 - 109 + num7 - 1) / num7));
			}
			while (num6 != num5);
			Header.dwDifatSect1 = AllocateFat(num5 * Header.GetSectorSize(), DiFatSectors);
			Header.dwNumDifatSects = (uint)DiFatSectors.Count;
			foreach (long diFatSector in DiFatSectors)
			{
				Fat[SectorToInt(diFatSector)] = 4294967292L;
			}
			num4 = GetSectorCount(Fat.Count * 4);
			AllocateFat((Fat.Count + num4) * 4, FatSectors);
			Header.dwNumFatSects = (uint)FatSectors.Count;
			foreach (long fatSector in FatSectors)
			{
				Fat[SectorToInt(fatSector)] = 4294967293L;
			}
			for (int k = 0; k < 109; k++)
			{
				if (k < FatSectors.Count)
				{
					Header.dwDiFat[k] = (uint)FatSectors[k];
				}
				else
				{
					Header.dwDiFat[k] = uint.MaxValue;
				}
			}
			Header.FileSize = 512 + Fat.Count * Header.GetSectorSize();
		}

		public void WriteMiniSectorChain(BinaryWriter w, long sector, System.IO.Stream ms)
		{
			ms.Position = 0L;
			while (ms.Position < ms.Length && SectorType.IsRegularSector(sector) && sector < MiniFat.Count)
			{
				w.BaseStream.Position = Header.GetMiniSectorPosition(sector);
				for (int num = Header.GetMiniSectorSize(); num > 0; num--)
				{
					if (ms.Position < ms.Length)
					{
						w.Write((byte)ms.ReadByte());
					}
					else
					{
						w.Write((byte)0);
					}
				}
				sector = MiniFat[SectorToInt(sector)];
			}
		}

		public void WriteSectorChain(BinaryWriter w, long sector, System.IO.Stream ms)
		{
			ms.Position = 0L;
			while (ms.Position < ms.Length && SectorType.IsRegularSector(sector) && sector < Fat.Count)
			{
				w.BaseStream.Position = Header.GetSectorPosition(sector);
				for (int num = Header.GetSectorSize(); num > 0; num--)
				{
					if (ms.Position < ms.Length)
					{
						w.Write((byte)ms.ReadByte());
					}
					else
					{
						w.Write((byte)0);
					}
				}
				sector = Fat[SectorToInt(sector)];
			}
		}

		public void Write(BinaryWriter w)
		{
			Header.Write(w);
			WriteFat(w);
			System.IO.Stream stream = new MemoryStream();
			BinaryWriter w2 = new BinaryWriter(stream);
			WriteElements(w2);
			stream.Position = 0L;
			WriteSectorChain(w, Header.dwDirSect1, stream);
			WriteMiniFat(w);
			WriteAllElementData(w);
		}

		public void WriteAllElementData(BinaryWriter w)
		{
			RootElement.Data.Stream = new MemoryStream();
			foreach (Element element in Elements)
			{
				if (element.IsDataElement() && IsInMiniStream(element))
				{
					BinaryWriter w2 = new BinaryWriter(RootElement.Data.Stream);
					WriteMiniSectorChain(w2, element.dwStartSect, element.Data.Stream);
				}
			}
			foreach (Element element2 in Elements)
			{
				if (element2.IsDataElement() && !IsInMiniStream(element2))
				{
					WriteSectorChain(w, element2.dwStartSect, element2.Data.Stream);
				}
			}
		}

		public void WriteElements(BinaryWriter w)
		{
			foreach (Element element in Elements)
			{
				element.Write(w);
			}
		}

		public void WriteMiniFat(BinaryWriter w)
		{
			int num = 0;
			foreach (long miniFatSector in MiniFatSectors)
			{
				w.BaseStream.Position = Header.GetSectorPosition(miniFatSector);
				for (int num2 = Header.GetSectorSize() / 4; num2 > 0; num2--)
				{
					if (num < MiniFat.Count)
					{
						w.Write((uint)MiniFat[num++]);
					}
					else
					{
						w.Write(uint.MaxValue);
					}
				}
			}
		}

		public void ReadMiniFat(Parser p)
		{
			p.Enter("MiniFat");
			MiniFatSectors = ReadSectorChainIndices(Header.dwMiniFatSect1);
			foreach (long miniFatSector in MiniFatSectors)
			{
				if (miniFatSector < GetSectorsInFile())
				{
					p.Position = Header.GetSectorPosition(miniFatSector);
					for (int num = Header.GetSectorSize() / 4; num > 0; num--)
					{
						long item = p.TryParseUInt32("Sector", uint.MaxValue);
						MiniFat.Add(item);
					}
					continue;
				}
				p.GetCurrentStruct().AddWarning(10004, "MiniFat sector out of bounds: sector: {0} sectors in file: {1}", new object[2]
				{
					miniFatSector,
					GetSectorsInFile()
				});
				for (int num2 = Header.GetSectorSize() / 4; num2 > 0; num2--)
				{
					MiniFat.Add(4294967295L);
				}
			}
			p.Leave();
		}

		public void DeleteElement(Element elem)
		{
			elem.Data.Stream.SetLength(0L);
			elem.Type = 0;
			elem.dwStartSect = uint.MaxValue;
			elem.dwSizeLow = 0u;
			elem.dwSizeHigh = 0u;
		}

		public Element GetElementByName(string name)
		{
			foreach (Element element in Elements)
			{
				if (element.GetName() == name)
				{
					return element;
				}
			}
			return null;
		}

		public Element GetRootElementByName(string name)
		{
			foreach (Element element in RootElement.Elements)
			{
				if (element.GetName() == name)
				{
					return element;
				}
			}
			return null;
		}

		public long GetElementDataSize(Element element)
		{
			if (element.IsDataElement())
			{
				if (IsInMiniStream(element))
				{
					List<long> list = ReadMiniSectorChainIndices(element.dwStartSect);
					return list.Count * Header.GetMiniSectorSize();
				}
				List<long> list2 = ReadSectorChainIndices(element.dwStartSect);
				return list2.Count * Header.GetSectorSize();
			}
			return 0L;
		}

		public int SectorToInt(long sector)
		{
			if (sector > int.MaxValue)
			{
				return int.MaxValue;
			}
			return Convert.ToInt32(sector);
		}

		public void MarkChain(List<FatEntry> allocs, long firstSector, int index, FatType type, Element elem)
		{
			List<long> list = ReadSectorChainIndices(firstSector);
			foreach (long item in list)
			{
				FatEntry fatEntry = allocs[SectorToInt(item)];
				if (fatEntry.Type == FatType.Allocated)
				{
					fatEntry.Type = type;
					fatEntry.Element = elem;
					fatEntry.Index = index;
				}
			}
		}

		public void MarkMiniChain(List<FatEntry> allocs, long firstSector, int index, FatType type, Element elem)
		{
			List<long> list = ReadMiniSectorChainIndices(firstSector);
			foreach (long item in list)
			{
				FatEntry fatEntry = allocs[SectorToInt(item)];
				if (fatEntry.Type == FatType.Allocated)
				{
					fatEntry.Type = type;
					fatEntry.Element = elem;
					fatEntry.Index = index;
				}
			}
		}

		public List<FatEntry> GetFatAllocations()
		{
			if (FatAllocations == null)
			{
				FatAllocations = new List<FatEntry>();
				foreach (long item in Fat)
				{
					FatEntry fatEntry = new FatEntry();
					fatEntry.Next = item;
					long num = item;
					if (num == uint.MaxValue)
					{
						fatEntry.Type = FatType.Unallocated;
					}
					else
					{
						fatEntry.Type = FatType.Allocated;
						fatEntry.Index = -1;
					}
					FatAllocations.Add(fatEntry);
				}
				foreach (long fatSector in FatSectors)
				{
					if (fatSector < GetSectorsInFile())
					{
						FatAllocations[SectorToInt(fatSector)].Type = FatType.Fat;
					}
				}
				MarkChain(FatAllocations, Header.dwDifatSect1, -1, FatType.Fat, null);
				MarkChain(FatAllocations, Header.dwDirSect1, -1, FatType.Dir, null);
				MarkChain(FatAllocations, Header.dwMiniFatSect1, -1, FatType.MiniFat, null);
				int num2 = 0;
				foreach (Element element in Elements)
				{
					if (element.IsDataElement() && !IsInMiniStream(element))
					{
						MarkChain(FatAllocations, element.dwStartSect, num2, FatType.Element, element);
						num2++;
					}
				}
			}
			return FatAllocations;
		}

		public List<FatEntry> GetMiniFatAllocations()
		{
			if (MiniFatAllocations == null)
			{
				MiniFatAllocations = new List<FatEntry>();
				foreach (long item in MiniFat)
				{
					FatEntry fatEntry = new FatEntry();
					fatEntry.Next = item;
					long num = item;
					if (num == uint.MaxValue)
					{
						fatEntry.Type = FatType.Unallocated;
					}
					else
					{
						fatEntry.Type = FatType.Allocated;
						fatEntry.Index = -1;
					}
					MiniFatAllocations.Add(fatEntry);
				}
				int num2 = 0;
				foreach (Element element in Elements)
				{
					if (element.IsDataElement() && IsInMiniStream(element))
					{
						MarkMiniChain(MiniFatAllocations, element.dwStartSect, num2, FatType.Element, element);
						num2++;
					}
				}
			}
			return MiniFatAllocations;
		}

		public bool IsInMiniStream(Element elem)
		{
			if (elem.GetElementType() != ElementType.Stream)
			{
				return false;
			}
			if (elem == RootElement)
			{
				return false;
			}
			return elem.GetSize() < Header.GetMiniFatCutoffSize();
		}

		public List<long> ReadSectorChainIndices(long sector)
		{
			List<long> list = new List<long>();
			Dictionary<long, bool> dictionary = new Dictionary<long, bool>();
			while (SectorType.IsRegularSector(sector))
			{
				if (dictionary.ContainsKey(sector))
				{
					Parser.GetCurrentStruct().AddWarning(10019, "Loop in sector chain");
					break;
				}
				if (list.Count >= GetSectorsInFile())
				{
					Parser.GetCurrentStruct().AddWarning(10020, "Invalid sector chain: too many sectors");
					break;
				}
				int num = SectorToInt(sector);
				if (num >= Fat.Count)
				{
					Parser.GetCurrentStruct().AddWarning(10021, "Invalid sector chain: fat has {0} sectors, chain has sector {1}", new object[2] { Fat.Count, num });
					break;
				}
				list.Add(sector);
				dictionary.Add(sector, value: true);
				sector = Fat[num];
			}
			return list;
		}

		public EmbeddedStream ReadSectorChainExtended(Parser r, long sector)
		{
			EmbeddedStream embeddedStream = new EmbeddedStream();
			embeddedStream.Stream = new MemoryStream();
			List<long> list = ReadSectorChainIndices(sector);
			foreach (long item in list)
			{
				if (item < GetSectorsInFile())
				{
					r.Position = Header.GetSectorPosition(item);
					int num = Header.GetSectorSize();
					if (num > r.Length - r.Position)
					{
						num = (int)(r.Length - r.Position);
					}
					embeddedStream.AddRangeMap(embeddedStream.Stream.Position, r.Position, Header.GetSectorSize());
					embeddedStream.Stream.Write(r.ReadBytes(num), 0, num);
					continue;
				}
				for (int i = 0; i < Header.GetSectorSize(); i++)
				{
					if (r.Position < r.Length)
					{
						embeddedStream.Stream.Write(r.ReadBytes(1), 0, 1);
					}
					else
					{
						embeddedStream.Stream.WriteByte(0);
					}
				}
				Parser.GetCurrentStruct().AddWarning(10022, "reading invalid sector:{0}", new object[1] { item });
			}
			if (r.Position < r.Length)
			{
				int num2 = (int)(r.Length - r.Position);
				embeddedStream.AddRangeMap(embeddedStream.Stream.Position, r.Position, num2);
				embeddedStream.Stream.Write(r.ReadBytes(num2), 0, num2);
			}
			embeddedStream.Stream.Position = 0L;
			return embeddedStream;
		}

		public EmbeddedStream ReadSectorChain(Parser r, long sector)
		{
			EmbeddedStream embeddedStream = new EmbeddedStream();
			embeddedStream.Stream = new MemoryStream();
			List<long> list = ReadSectorChainIndices(sector);
			foreach (long item in list)
			{
				if (item < GetSectorsInFile())
				{
					r.Position = Header.GetSectorPosition(item);
					int num = Header.GetSectorSize();
					if (num > r.Length - r.Position)
					{
						num = (int)(r.Length - r.Position);
					}
					embeddedStream.AddRangeMap(embeddedStream.Stream.Position, r.Position, Header.GetSectorSize());
					embeddedStream.Stream.Write(r.ReadBytes(num), 0, num);
				}
				else
				{
					for (int i = 0; i < Header.GetSectorSize(); i++)
					{
						embeddedStream.Stream.WriteByte(0);
					}
					Parser.GetCurrentStruct().AddWarning(10022, "reading invalid sector:{0}", new object[1] { item });
				}
			}
			embeddedStream.Stream.Position = 0L;
			return embeddedStream;
		}

		public List<long> ReadMiniSectorChainIndices(long sector)
		{
			List<long> list = new List<long>();
			Dictionary<long, bool> dictionary = new Dictionary<long, bool>();
			while (SectorType.IsRegularSector(sector))
			{
				if (dictionary.ContainsKey(sector))
				{
					Parser.GetCurrentStruct().AddWarning(10023, "Loop in mini sector chain");
					break;
				}
				if (list.Count >= GetMiniSectorsInFile())
				{
					Parser.GetCurrentStruct().AddWarning(10024, "Invalid mini sector chain: too many mini sectors");
					break;
				}
				int num = SectorToInt(sector);
				if (num >= MiniFat.Count)
				{
					Parser.GetCurrentStruct().AddWarning(10025, "Invalid mini sector chain: minifat has {0} sectors, chain has sector {1}", new object[2] { MiniFat.Count, num });
					break;
				}
				list.Add(sector);
				dictionary.Add(sector, value: true);
				sector = MiniFat[num];
			}
			return list;
		}

		public EmbeddedStream ReadMiniSectorChain(Parser r, long sector)
		{
			EmbeddedStream embeddedStream = new EmbeddedStream();
			embeddedStream.Stream = new MemoryStream();
			embeddedStream.Parent = RootElement.Data;
			List<long> list = ReadMiniSectorChainIndices(sector);
			foreach (long item in list)
			{
				if (item < GetMiniSectorsInFile())
				{
					r.Position = Header.GetMiniSectorPosition(item);
					int num = Header.GetMiniSectorSize();
					if (num > r.Length - r.Position)
					{
						num = (int)(r.Length - r.Position);
					}
					embeddedStream.AddRangeMap(embeddedStream.Stream.Position, r.Position, Header.GetMiniSectorSize());
					embeddedStream.Stream.Write(r.ReadBytes(num), 0, num);
				}
				else
				{
					for (int i = 0; i < Header.GetMiniSectorSize(); i++)
					{
						embeddedStream.Stream.WriteByte(0);
					}
					Parser.GetCurrentStruct().AddWarning(10026, "Reading invalid minisector: {0}", new object[1] { item });
				}
			}
			embeddedStream.Stream.Position = 0L;
			return embeddedStream;
		}

		public List<long> ReadSectorChainIndices(Element elem)
		{
			if (IsInMiniStream(elem))
			{
				return ReadMiniSectorChainIndices(elem.dwStartSect);
			}
			return ReadSectorChainIndices(elem.dwStartSect);
		}

		public EmbeddedStream ReadSectorChain(Parser r, Element elem)
		{
			if (IsInMiniStream(elem))
			{
				return ReadMiniSectorChain(r, elem.dwStartSect);
			}
			return ReadSectorChain(r, elem.dwStartSect);
		}

		public void AddChildren(Element element, int childIndex)
		{
			if (childIndex <= 0)
			{
				return;
			}
			if (childIndex < Elements.Count)
			{
				if (Elements[childIndex].Parent != null)
				{
					Parser.GetCurrentStruct().AddWarning(10015, "Loop in element tree");
					return;
				}
				Elements[childIndex].Parent = element;
				element.Elements.Add(Elements[childIndex]);
				AddChildren(element, (int)Elements[childIndex].sidLeft);
				AddChildren(element, (int)Elements[childIndex].sidRight);
				AddChildren(Elements[childIndex], (int)Elements[childIndex].sidChild);
			}
			else
			{
				Parser.GetCurrentStruct().AddWarning(10016, "Invalid child element: child: {0}, elements: {1}", new object[2] { childIndex, Elements.Count });
			}
		}

		public EmbeddedStream ReadElementData(Parser p, Element element)
		{
			if (IsInMiniStream(element))
			{
				p.PushPosition();
				RootElement.Data.Stream.Position = 0L;
				p.SetStream(RootElement.Data.Stream);
				EmbeddedStream embeddedStream = ReadMiniSectorChain(p, element.dwStartSect);
				if (element.GetSize() > embeddedStream.Stream.Length)
				{
					Parser.GetCurrentStruct().AddWarning(10018, "Element {0} length overflows mini sector chain length: Declared size:{1} available size:{2}", new object[3]
					{
						element.GetName(),
						element.GetSize(),
						embeddedStream.Stream.Length
					});
				}
				else
				{
					embeddedStream.Stream.SetLength(element.GetSize());
				}
				p.PopPosition();
				return embeddedStream;
			}
			EmbeddedStream embeddedStream2 = ReadSectorChain(p, element.dwStartSect);
			if (element.GetSize() > embeddedStream2.Stream.Length)
			{
				Parser.GetCurrentStruct().AddWarning(10017, "Element {0} length overflows sector chain length: Declared size:{1} available size:{2}", new object[3]
				{
					element.GetName(),
					element.GetSize(),
					embeddedStream2.Stream.Length
				});
				embeddedStream2.Stream.Position = embeddedStream2.Stream.Length;
				long num = element.GetSize() - embeddedStream2.Stream.Length;
				long num2 = p.Length - p.Position;
				long num3 = num;
				if (num3 > num2)
				{
					num3 = num2;
				}
				if (num3 > 0)
				{
					embeddedStream2.Stream.Write(p.ReadBytes((int)num3), 0, (int)num3);
					num -= num3;
				}
				embeddedStream2.Stream.Position = 0L;
			}
			else
			{
				embeddedStream2.Stream.SetLength(element.GetSize());
			}
			return embeddedStream2;
		}

		public void ReadElementsData(System.IO.Stream stream)
		{
			Dictionary<long, Element> dictionary = new Dictionary<long, Element>();
			Dictionary<long, Element> dictionary2 = new Dictionary<long, Element>();
			foreach (Element element in Elements)
			{
				if (!element.IsDataElement())
				{
					continue;
				}
				Parser parser = new Parser(element.Markup);
				parser.SetStream(stream);
				parser.Enter("Data Stream", "OLESS.Element");
				parser.GetCurrentStruct().Tag = element.GetPath() + "\\" + element.GetName();
				bool flag = true;
				if (SectorType.IsRegularSector(element.dwStartSect))
				{
					if (IsInMiniStream(element))
					{
						if (dictionary2.ContainsKey(element.dwStartSect))
						{
							parser.GetCurrentStruct().AddWarning(10013, "Element data overlaps existing element");
							flag = false;
						}
						else
						{
							dictionary2.Add(element.dwStartSect, element);
						}
					}
					else if (dictionary.ContainsKey(element.dwStartSect))
					{
						parser.GetCurrentStruct().AddWarning(10013, "Element data overlaps existing element");
						flag = false;
					}
					else
					{
						dictionary.Add(element.dwStartSect, element);
					}
				}
				else
				{
					flag = false;
					if (element.GetSize() != 0)
					{
						parser.GetCurrentStruct().AddWarning(10014, "Element has invalid start sector and non-zero data length");
					}
				}
				if (flag)
				{
					element.Data = ReadElementData(parser, element);
				}
				parser.GetCurrentStruct().Data = element.Data;
				parser.Leave();
			}
		}

		public void ReadElements(Parser p, long size)
		{
			long num = size / 128;
			long num2 = (p.Length - p.Position) / 128;
			RootElement = null;
			while (Elements.Count < num)
			{
				Element element = new Element();
				p.Enter("[" + Elements.Count + "]");
				element.Markup = p.GetCurrentStruct();
				element.Read(p);
				p.GetCurrentStruct().Value = element.GetName();
				if (RootElement == null)
				{
					RootElement = element;
				}
				Elements.Add(element);
				p.Leave();
				if (element.sidLeft >= num && element.sidLeft < num2)
				{
					num = element.sidLeft + 1;
				}
				if (element.sidRight >= num && element.sidRight < num2)
				{
					num = element.sidRight + 1;
				}
				if (element.sidChild >= num && element.sidChild < num2)
				{
					num = element.sidChild + 1;
				}
			}
			AddChildren(RootElement, (int)RootElement.sidChild);
		}

		public void ReadElements(Parser p)
		{
			long size = ReadSectorChainIndices(Header.dwDirSect1).Count * Header.GetSectorSize();
			EmbeddedStream embeddedStream = ReadSectorChainExtended(p, Header.dwDirSect1);
			p.PushPosition();
			p.SetStream(embeddedStream.Stream);
			p.Enter("Elements").Data = embeddedStream;
			ReadElements(p, size);
			p.Leave();
			p.PopPosition();
			ReadElementsData(p.GetStream());
		}

		public bool IsOLESSFile(System.IO.Stream s)
		{
			if (s.Length >= 8)
			{
				s.Position = 0L;
				byte[] array = new byte[8];
				s.Read(array, 0, 8);
				return OLESSSignatures.IsOLESSSignature(array);
			}
			return false;
		}

		public void LoadFromStream(System.IO.Stream s)
		{
			Parser = new Parser();
			Parser.SetStream(s);
			Parser.Position = 0L;
			Read(Parser);
		}

		public void Load(string filename)
		{
			FileStream fileStream = new FileStream(filename, FileMode.Open, FileAccess.Read);
			try
			{
				LoadFromStream(fileStream);
			}
			finally
			{
				fileStream.Close();
			}
		}

		public void SaveToStream(System.IO.Stream s)
		{
			BinaryWriter w = new BinaryWriter(s);
			Write(w);
		}

		public void SaveAs(string filename)
		{
			FileStream fileStream = new FileStream(filename, FileMode.Create, FileAccess.Write);
			MemoryStream memoryStream = new MemoryStream();
			try
			{
				SaveToStream(memoryStream);
				fileStream.Write(memoryStream.ToArray(), 0, (int)memoryStream.Length);
			}
			finally
			{
				fileStream.Close();
			}
		}
	}
	public class Utils
	{
		public class NeuterOptions
		{
			public bool Randomize;

			public bool Pad;

			internal int PadIndex = -1;

			internal bool Padded;
		}

		private static string filenameChars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

		public static bool IsOLESSFile(string filename)
		{
			Storage storage = new Storage();
			try
			{
				storage.OpenFile(filename);
			}
			catch (Exception)
			{
				return false;
			}
			storage.Close();
			return true;
		}

		public static bool HasOLESSHeader(string filename)
		{
			FileStream input = new FileStream(filename, FileMode.Open, FileAccess.Read);
			BinaryReader binaryReader = new BinaryReader(input);
			byte[] signature = binaryReader.ReadBytes(OLESSSignatures.SignatureLength);
			return OLESSSignatures.IsOLESSSignature(signature);
		}

		public SortedList<string, string> ParseStreamProperties(Element elem)
		{
			return new SortedList<string, string>();
		}

		private static List<StorageStats> Randomize(List<StorageStats> items)
		{
			List<StorageStats> list = new List<StorageStats>();
			list.AddRange(items);
			Random random = new Random((int)DateTime.Now.Ticks);
			for (int i = 0; i < list.Count; i++)
			{
				StorageStats value = list[i];
				int index = random.Next(i);
				list[i] = list[index];
				list[index] = value;
			}
			return list;
		}

		public static void Neuter(Storage src, Storage dst, NeuterOptions opts)
		{
			List<StorageStats> list = ((!opts.Randomize) ? src.GetItems() : Randomize(src.GetItems()));
			int num = -1;
			if (opts.Pad && opts.PadIndex < 0)
			{
				Random random = new Random();
				num = (opts.PadIndex = random.Next(0, list.Count));
			}
			int num2 = 0;
			foreach (StorageStats item in list)
			{
				if (num2++ == num)
				{
					PadWithRandomData(dst);
					opts.Padded = true;
				}
				switch (item.Type)
				{
				case StorageType.Storage:
				{
					Storage storage = src.OpenStorage(item.Name);
					Storage storage2 = dst.CreateStorage(item.Name);
					Neuter(storage, storage2, opts);
					storage.Close();
					storage2.Close();
					break;
				}
				case StorageType.Stream:
				{
					Stream stream = src.OpenStream(item.Name);
					Stream stream2 = dst.CreateStream(item.Name);
					stream2.Write(stream.Read());
					stream.Close();
					stream2.Close();
					break;
				}
				}
			}
		}

		public static void PadWithRandomData(Storage dst)
		{
			StringBuilder stringBuilder = new StringBuilder(32);
			Random random = new Random();
			for (int i = 0; i < 31; i++)
			{
				stringBuilder.Append(filenameChars[random.Next(0, filenameChars.Length)]);
			}
			Stream stream = dst.CreateStream(stringBuilder.ToString());
			int num = random.Next(1, 4);
			byte[] array = new byte[num * 512 + 4096];
			for (int j = 0; j < num * 512 + 4096; j++)
			{
				array[j] = (byte)random.Next(0, 256);
			}
			stream.Write(array);
			stream.Close();
		}

		public static void Neuter(string srcfile, string dstfile, NeuterOptions opts)
		{
			Storage storage = new Storage();
			storage.OpenFile(srcfile);
			Storage storage2 = new Storage();
			storage2.CreateFile(dstfile);
			Neuter(storage, storage2, opts);
			if (opts.Pad && !opts.Padded)
			{
				PadWithRandomData(storage2);
			}
			storage.Close();
			storage2.Close();
		}
	}
}
