Private Function parseMiniFAT() As String
    On Error GoTo ErrorHandler
    Erase cf.MiniFAT
    Dim currentSectorID As Long

    If cf.Header.MiniFATSectorsCount = 0 Then Exit Function

    currentSectorID = cf.Header.FirstMiniFATSID

    Do
        If currentSectorID < 0 Then Exit Do
        
        cf.r.SeekFile getOffsetBySID(currentSectorID), OriginF
        Dim i As Long
        For i = 1 To cf.longNumPerSector
            AddItemToArray_Long cf.MiniFAT, cf.r.ReadLong()
        Next i
        
        currentSectorID = cf.FAT(currentSectorID)
    Loop
    Exit Function
ErrorHandler:
    parseMiniFAT = "解析MiniFAT时出错: " & Err.Description
End Function

Private Sub GetDirsName()
    Dim i As Long
    On Error Resume Next
    If UBound(cf.ArrDir) < LBound(cf.ArrDir) Then
        Erase cf.ArrDirsName
        Exit Sub
    End If
    On Error GoTo 0
    
    ReDim cf.ArrDirsName(UBound(cf.ArrDir))
    For i = 0 To UBound(cf.ArrDir)
        cf.ArrDirsName(i) = cf.ArrDir(i).StrDirName
    Next
End Sub

' --- 私有读/写/辅助函数 ---

Private Function GetStreamByDirIndex(dirIndex As Long, RetBytes() As Byte) As String
    On Error GoTo ErrorHandler
    If cf.ArrDir(dirIndex).ObjectType <> 2 Then
        GetStreamByDirIndex = "复合文档：不是数据流"
        Exit Function
    End If

    Dim StreamSize As Currency
    StreamSize = ConvertBytesToCurrency(cf.ArrDir(dirIndex).StreamSize(1), cf.ArrDir(dirIndex).StreamSize(0))

    If StreamSize = 0 Then
       Erase RetBytes
       Exit Function
    End If
    
    If StreamSize > 2147483647@ Then
        GetStreamByDirIndex = "流太大，超过了VBA数组的最大限制 (2GB)"
        Exit Function
    End If

    ReDim RetBytes(0 To StreamSize - 1)

    If StreamSize < cf.Header.MiniStreamSize Then
        GetStreamByDirIndex = GetStreamMiniFAT(dirIndex, RetBytes)
    Else
        GetStreamByDirIndex = GetStreamFAT(dirIndex, RetBytes)
    End If
    Exit Function
ErrorHandler:
    GetStreamByDirIndex = "GetStreamByDirIndex 发生错误: " & Err.Description
End Function

Private Function GetStreamFAT(dirIndex As Long, RetBytes() As Byte) As String
    On Error GoTo ErrorHandler
    Dim sid As Long, p As Double, totalSize As Double
    Dim bytesLeft As Double, bytesToCopy As Long
    Dim tempBuffer() As Byte

    sid = cf.ArrDir(dirIndex).StartingSectorID
    totalSize = ConvertBytesToCurrency(cf.ArrDir(dirIndex).StreamSize(1), cf.ArrDir(dirIndex).StreamSize(0))
    p = 0

    Do While sid >= 0 And sid <> End_Of_Chain_SID And p < totalSize
        bytesLeft = totalSize - p
        If bytesLeft > cf.lSectorSize Then
            bytesToCopy = cf.lSectorSize
        Else
            bytesToCopy = bytesLeft
        End If

        ReDim tempBuffer(0 To bytesToCopy - 1)
        cf.r.ReadAt tempBuffer, getOffsetBySID(sid)
        
        CopyMemory ByVal VarPtr(RetBytes(p)), ByVal VarPtr(tempBuffer(0)), bytesToCopy

        p = p + bytesToCopy
        sid = cf.FAT(sid)
    Loop
    Exit Function
ErrorHandler:
    GetStreamFAT = "GetStreamFAT 发生错误: " & Err.Description
End Function

Private Function GetStreamMiniFAT(dirIndex As Long, RetBytes() As Byte) As String
    On Error GoTo ErrorHandler
    Dim miniSID As Long, p As Double, totalSize As Double
    Dim bytesLeft As Double, bytesToCopy As Long
    Dim tempBuffer() As Byte

    miniSID = cf.ArrDir(dirIndex).StartingSectorID
    totalSize = ConvertBytesToCurrency(cf.ArrDir(dirIndex).StreamSize(1), cf.ArrDir(dirIndex).StreamSize(0))
    p = 0

    Do While miniSID >= 0 And miniSID <> End_Of_Chain_SID And p < totalSize
        bytesLeft = totalSize - p
        If bytesLeft > cf.lShortSectorSize Then
            bytesToCopy = cf.lShortSectorSize
        Else
            bytesToCopy = bytesLeft
        End If

        ReDim tempBuffer(0 To bytesToCopy - 1)
        cf.r.ReadAt tempBuffer, getOffsetByMiniSID(miniSID)
        
        CopyMemory ByVal VarPtr(RetBytes(p)), ByVal VarPtr(tempBuffer(0)), bytesToCopy

        p = p + bytesToCopy
        miniSID = cf.MiniFAT(miniSID)
    Loop
    Exit Function
ErrorHandler:
    GetStreamMiniFAT = "GetStreamMiniFAT 发生错误: " & Err.Description
End Function

Private Function ReWriteStreamByDirIndex(dirIndex As Long, WriteBytes() As Byte) As String
    ' 声明此函数以避免编译错误，但完整的、安全的写入实现非常复杂，
    ' 涉及到空闲扇区的查找、分配和FAT表的更新，超出了本次修复的范围。
End Function

' --- 辅助函数 ---
Private Sub AddItemToArray_Long(ByRef arr() As Long, ByVal Item As Long)
    Dim newIndex As Long
    On Error Resume Next
    newIndex = UBound(arr) + 1
    If Err.Number <> 0 Then
        newIndex = 0
        Err.Clear
    End If
    On Error GoTo 0
    ReDim Preserve arr(newIndex)
    arr(newIndex) = Item
End Sub

Private Sub AddItemToArray_Dir(ByRef arr() As CFDir, ByRef Item As CFDir)
    Dim newIndex As Long
    On Error Resume Next
    newIndex = UBound(arr) + 1
    If Err.Number <> 0 Then
        newIndex = 0
        Err.Clear
    End If
    On Error GoTo 0
    ReDim Preserve arr(newIndex)
    arr(newIndex) = Item
End Sub

Private Function getOffsetBySID(ByVal sid As Long) As Long
    getOffsetBySID = HEADER_SIZE + sid * cf.lSectorSize
End Function

Private Function getOffsetByMiniSID(ByVal miniSID As Long) As Double
   Dim currentSID As Long
   Dim hops As Long

   currentSID = cf.ArrDir(0).StartingSectorID
   hops = miniSID \ cf.ssNumPerSector

   Dim i As Long
   For i = 1 To hops
       If currentSID < 0 Then GoTo ErrorExit
       currentSID = cf.FAT(currentSID)
   Next i
    If currentSID < 0 Then GoTo ErrorExit

   getOffsetByMiniSID = getOffsetBySID(currentSID) + (miniSID Mod cf.ssNumPerSector) * cf.lShortSectorSize
   Exit Function
ErrorExit:
    getOffsetByMiniSID = -1 ' 表示错误
End Function

Private Function ConvertBytesToCurrency(high As Long, low As Long) As Currency
   Const FACTOR As Double = 4294967296# ' 2^32
   Dim result As Currency

   If high < 0 Then
       result = (high + FACTOR) * FACTOR
   Else
       result = high * FACTOR
   End If

   If low < 0 Then
       result = result + (low + FACTOR)
   Else
       result = result + low
   End If
   
   ConvertBytesToCurrency = result / 10000#
End Function

#If VBA7 And Win64 Then
Private Function ConvertBytesToLongLong(ByVal high As Long, ByVal low As Long) As LongLong
    ' 这是一个辅助函数，用于将两个32位Long组合成一个64位的LongLong
    Const FACTOR As Double = 4294967296# ' 2^32
    Dim result As LongLong
    
    If high < 0 Then
        result = (high + FACTOR) * FACTOR
    Else
        result = high * FACTOR
    End If
    
    If low < 0 Then
        result = result + (low + FACTOR)
    Else
        result = result + low
    End If
    
    ConvertBytesToLongLong = result
End Function
#End If

' 辅助函数用于创建目录和保存文件
Private Function makeDir(SavePath As String, DirName As String) As String
    Dim arr As Variant
    Dim i As Long
    Dim tmp As String
    tmp = SavePath
    If VBA.Len(VBA.Dir(tmp, vbDirectory)) = 0 Then
        VBA.MkDir tmp
    End If
    arr = VBA.Split(DirName, Application.PathSeparator)
    For i = 0 To UBound(arr) - 1
        tmp = tmp & arr(i) & Application.PathSeparator
        If VBA.Len(VBA.Dir(tmp, vbDirectory)) = 0 Then
            VBA.MkDir tmp
        End If
    Next
    makeDir = tmp & arr(i)
End Function

Private Function ByteToFile(file_name As String, b() As Byte)
    Dim iFreefile As Integer
    iFreefile = VBA.FreeFile()
    Open file_name For Binary As iFreefile
    Put #iFreefile, 1, b
    Close iFreefile
End Function
